import { StoryElementType } from 'types/Story';

import Gallery, { galleryHeights } from './Gallery';
import Generic, { genericHeights } from './Generic';
import ExploreTravelGeneric from './Generic/ExploreTravelGeneric';
import Heading, { headingHeights } from './Heading';
import ExploreTravelHeading, {
  headingHeightsExploreTravel,
} from './Heading/ExploreTravelHeading';
import Image, { imageHeights } from './Image';
import ExploreTravelImage, {
  imageHeightsExploreTravel,
} from './Image/ExploreTravelImage';
import List, { listHeights } from './List';
import ExploreTravelList from './List/ExploreTravelList';
import Listbox from './Listbox';
import Paragraph, { paragraphHeights } from './Paragraph';
import ExploreTravelParagraph, {
  paragraphHeightsExploreTravel,
} from './Paragraph/ExploreTravelParagraph';
import Quote, { quoteHeights } from './Quote';
import ExploreTravelQuote, {
  quoteHeightsExploreTravel,
} from './Quote/ExploreTravelQuote';
import StaticAnchorBox from './StaticAnchorBox';

import type { EstimateHeightFn } from './common/storyElements';

export type StoryComponentMap = Record<
  string,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [React.ComponentType<any>, EstimateHeightFn?]
>;

export const defaultStoryComponentMap: StoryComponentMap = {
  [StoryElementType.Heading]: [Heading, headingHeights as EstimateHeightFn],
  [StoryElementType.Image]: [Image, imageHeights as EstimateHeightFn],
  [StoryElementType.List]: [List, listHeights as EstimateHeightFn],
  [StoryElementType.Listbox]: [Listbox],
  [StoryElementType.Paragraph]: [
    Paragraph,
    paragraphHeights as EstimateHeightFn,
  ],
  [StoryElementType.Quote]: [Quote, quoteHeights as EstimateHeightFn],
  [StoryElementType.Gallery]: [Gallery, galleryHeights as EstimateHeightFn],
  [StoryElementType.Generic]: [Generic, genericHeights as EstimateHeightFn],
};

export const exploreTravelStoryComponentMap: StoryComponentMap = {
  ...defaultStoryComponentMap,
  [StoryElementType.Image]: [
    ExploreTravelImage,
    imageHeightsExploreTravel as EstimateHeightFn,
  ],
  [StoryElementType.Heading]: [
    ExploreTravelHeading,
    headingHeightsExploreTravel as EstimateHeightFn,
  ],
  [StoryElementType.Paragraph]: [
    ExploreTravelParagraph,
    paragraphHeightsExploreTravel as EstimateHeightFn,
  ],
  [StoryElementType.Quote]: [
    ExploreTravelQuote,
    quoteHeightsExploreTravel as EstimateHeightFn,
  ],
  [StoryElementType.AnchorBox]: [StaticAnchorBox],
  [StoryElementType.Generic]: [
    ExploreTravelGeneric,
    genericHeights as EstimateHeightFn,
  ],
  [StoryElementType.List]: [
    ExploreTravelList,
    genericHeights as EstimateHeightFn,
  ],
};
