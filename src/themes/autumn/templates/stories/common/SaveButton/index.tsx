import { twMerge } from 'tailwind-merge';

import BookmarkButton from 'components/Bookmark';
import { useAppSelector } from 'store/hooks';
import { UserBookmarkResourceType } from 'types/sepang-types/bookmark';
import { useWindowHref } from 'util/hooks';

export interface SaveButtonProps {
  className?: string;
}

const SaveButton = ({ className }: SaveButtonProps) => {
  const story = useAppSelector((state) => state.story);
  const url = useWindowHref({ strip: true });

  if (!story.id) {
    return null;
  }
  return (
    <BookmarkButton
      containerClassName={twMerge(
        'border-0 hover:bg-gray-100 py-0 h-9 w-[94px]',
        className,
      )}
      metadata={{
        description: story.summaryFull || story.summary,
        thumbnail: story.leadImage,
        title: story.title,
        url,
      }}
      resourceId={story.id}
      resourceType={UserBookmarkResourceType.STORY}
      title="Save"
    />
  );
};

export default SaveButton;
