import { faArrowRight } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeSvgIcon as FontAwesomeIcon } from 'react-fontawesome-svg-icon';

import getRandomImage from 'themes/autumn/components/classifieds/getRandomImage';
import Link from 'themes/autumn/components/generic/Link';
import AdImage from 'themes/autumn/templates/Classifieds/AdImage';
import { sendGtmEvent } from 'themes/autumn/templates/Classifieds/utils';
import InViewEvent from 'util/inViewEvent';

import AdDetail from './AdDetail';

import type { ClassifiedAd } from 'types/Classified';

interface Props {
  classifieds: ClassifiedAd[];
  onClick?: (classified: ClassifiedAd) => void;
  title: string;
}

function TributesFuneralNotices({
  classifieds,
  onClick,
  title,
}: Props): React.ReactElement | null {
  if (!classifieds.length) {
    return null;
  }

  const onEnterEvent = (classified: ClassifiedAd) => {
    sendGtmEvent(classified, 'ad_impression');
  };

  return (
    <div className="mb-5 rounded-md border border-gray-300 px-4 py-6 font-inter shadow-sm @md:px-10 @md:py-7">
      {!!title && (
        <h2 className="mb-6 text-xl font-semibold @md:mb-0">{title}</h2>
      )}
      <div className="mt-2 grid w-full grid-cols-2 items-start gap-4 @md:mt-3 @md:grid-cols-4">
        {classifieds.map((classified) => {
          const imageUrl = classified.images[1] || getRandomImage();
          const image = (
            <div className="size-20 overflow-hidden rounded-full border-1 border-gray-300">
              <AdImage
                alt={classified.title}
                className="size-full object-cover"
                url={imageUrl}
                width={80}
              />
            </div>
          );
          return (
            <InViewEvent
              key={classified.id}
              onEnterEvent={() => onEnterEvent(classified)}
            >
              {({ ref }) => (
                <AdDetail
                  ad={classified}
                  image={image}
                  onClick={onClick && (() => onClick(classified))}
                  ref={ref}
                />
              )}
            </InViewEvent>
          );
        })}
      </div>
      <Link
        className="mt-6 flex items-center gap-x-2 align-middle text-sm font-medium text-gray-900 no-underline visited:text-gray-900 hover:text-gray-900 hover:opacity-70 @md:mt-4"
        href="/tributes-funerals/"
      >
        <span>More {title || 'Tributes & Funerals'}</span>
        <FontAwesomeIcon icon={faArrowRight} />
      </Link>
    </div>
  );
}

export default TributesFuneralNotices;
