import React from 'react';

import Ad, { AdSize } from 'themes/autumn/components/ads/Ad';

import AdImage from '../AdImage';
import { getAdImage } from '../utils';

import type { ClassifiedAd } from 'types/Classified';

interface Props {
  showAds: boolean;
  sideBarAds: ClassifiedAd[];
  slug: string | undefined;
}

export default function TributesAndFuneral({
  showAds,
  sideBarAds,
  slug,
}: Props) {
  return (
    <>
      <h2 className="mb-4 mt-8 text-lg font-semibold">
        Tributes & funeral notices
      </h2>
      <ul>
        {sideBarAds.map((ad) => {
          const url = getAdImage(ad, slug);
          return (
            <li
              className="clear-both border-b border-gray-300 md:border-b-0 lg:border-b"
              key={ad.id}
            >
              <a
                className="my-2 flex h-20 cursor-pointer items-center gap-x-2 font-semibold"
                href={ad.canonicalUrl}
              >
                <span className="flex-1">
                  {ad.title}
                  {!!ad.yearDeceased && (
                    <p className="mt-2 font-normal text-gray-500">
                      {!!ad.yearBorn && `${ad.yearBorn} - `}
                      {ad.yearDeceased}
                    </p>
                  )}
                </span>
                {url && (
                  <div className="w-16 overflow-hidden rounded-md">
                    <AdImage
                      alt={ad.title}
                      className="mx-auto cursor-pointer"
                      height={80}
                      url={url}
                      width={80}
                    />
                  </div>
                )}
              </a>
            </li>
          );
        })}
      </ul>
      {showAds && (
        <Ad
          position={1}
          publiftName="side-2"
          sizes={AdSize.mrec}
          slotId="side-2"
        />
      )}
    </>
  );
}
