import React, {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';

import { fetchBookmarks } from 'components/Bookmark/sepang';
import { useAppSelector } from 'store/hooks';

import type { Bookmark } from 'components/Bookmark/types';
import type { ApiErrorResponse } from 'types/sepang-types/response';

type BookmarksContextType = {
  bookmarks: Bookmark[];
  error: ApiErrorResponse | null;
  isOpen: boolean;
  loading: boolean;
  toggleSideDrawer: () => void;
};

const SideDrawerContext = createContext<BookmarksContextType>({
  bookmarks: [],
  error: null,
  isOpen: false,
  loading: false,
  toggleSideDrawer: () => {},
});

interface SideDrawerProviderProps {
  children: React.ReactNode;
}

export const SideDrawerProvider: React.FC<SideDrawerProviderProps> = ({
  children,
}) => {
  const [bookmarks, setBookmarks] = useState<Bookmark[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<ApiErrorResponse | null>(null);
  const user = useAppSelector((state) => state.piano.user);
  const isMounted = useRef(false);
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    if (!user || isMounted.current) {
      return;
    }
    isMounted.current = true;
    fetchBookmarks({ limit: '2' })
      .then((res) => {
        if (res.success && res.data) {
          setBookmarks(res.data);
        } else {
          setError(res as ApiErrorResponse);
        }
      })
      .catch((err: ApiErrorResponse) => {
        setError(err);
      })
      .finally(() => {
        setLoading(false);
      });
  }, [user]);

  const toggleSideDrawer = useCallback(() => {
    setIsOpen(!isOpen);
  }, [isOpen]);

  const contextValue = useMemo(
    () => ({ bookmarks, error, isOpen, loading, toggleSideDrawer }),
    [bookmarks, error, isOpen, loading, toggleSideDrawer],
  );

  return (
    <SideDrawerContext.Provider value={contextValue}>
      {children}
    </SideDrawerContext.Provider>
  );
};

export function useSideDrawer() {
  const context = useContext(SideDrawerContext);
  if (context === undefined) {
    throw new Error('useSideDrawer must be used within a SideDrawerProvider');
  }
  return context;
}
