import { SocialProfileJsonLd } from 'next-seo';

import { useAppSelector } from 'store/hooks';

export default function SocialProfile() {
  const { domain, name } = useAppSelector((state) => state.conf);
  const facebookUrl = useAppSelector((state) => state.conf.facebookUrl);
  const twitterUsername = useAppSelector(
    (state) => state.conf.twitterUsername,
  );
  const youtubeUrl = useAppSelector((state) => state.conf.youtubeUrl);
  const instagramUsername = useAppSelector(
    (state) => state.conf.instagramUsername,
  );

  const socialProfileUrl = [];
  if (facebookUrl) {
    socialProfileUrl.push(facebookUrl);
  }
  if (twitterUsername) {
    socialProfileUrl.push(`https://twitter.com/${twitterUsername}/`);
  }
  if (youtubeUrl) {
    socialProfileUrl.push(youtubeUrl);
  }
  if (instagramUsername) {
    socialProfileUrl.push(`https://instagram.com/${instagramUsername}/`);
  }

  return (
    <SocialProfileJsonLd
      legalName={name}
      name={name}
      sameAs={socialProfileUrl}
      type="Organization"
      url={`https://${domain}/`}
      useAppDir
    />
  );
}
