import clsx from 'clsx';

import type { PropsWithChildren } from 'react';

interface SkeletonProps extends PropsWithChildren {
  className?: string;
  showContent: boolean;
}

export default function Skeleton({
  children,
  className,
  showContent: show,
}: SkeletonProps) {
  return (
    <div className={clsx('relative', className)}>
      {show ? (
        children
      ) : (
        <>
          <div className="absolute inset-0 animate-pulse bg-gray-300" />
          <div className="pointer-events-none opacity-0">{children}</div>
        </>
      )}
    </div>
  );
}
