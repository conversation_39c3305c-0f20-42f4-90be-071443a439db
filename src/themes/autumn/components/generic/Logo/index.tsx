import clsx from 'clsx';
import React from 'react';

import { useAppSelector } from 'store/hooks';
import Link from 'themes/autumn/components/generic/Link';

export enum LabelLogoType {
  DEFAULT,
  LABEL_WITH_SQUARE_LOGO,
}

export enum LogoType {
  DEFAULT,
  SQUARE,
  UNDERLINED,
  UNDERLINED_BLACK,
}

interface LogoProps {
  className?: string;
  imageClassName?: string;
  type: LogoType;
  url?: string;
  withLink?: boolean;
}

export default function Logo({
  className,
  imageClassName,
  type,
  url = '/',
  withLink = true,
}: LogoProps): React.ReactElement {
  const {
    logoSvgOnly,
    logoSvgSquare,
    logoSvgUnderline,
    logoSvgUnderlineBlack,
    name,
  } = useAppSelector((state) => state.conf);

  const logoUrl = {
    [LogoType.DEFAULT]: logoSvgOnly,
    [LogoType.SQUARE]: logoSvgSquare,
    [LogoType.UNDERLINED]: logoSvgUnderline,
    [LogoType.UNDERLINED_BLACK]: logoSvgUnderlineBlack,
  }[type];

  const img = (
    <img
      alt={name}
      className={clsx(
        'size-full max-h-full max-w-full object-contain',
        imageClassName,
      )}
      src={logoUrl}
    />
  );

  return withLink ? (
    <Link className={clsx('block', className)} data-testid="logo" href={url}>
      {img}
    </Link>
  ) : (
    <div className={className} data-testid="logo">
      {img}
    </div>
  );
}
