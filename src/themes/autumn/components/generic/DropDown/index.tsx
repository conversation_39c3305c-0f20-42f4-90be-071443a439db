'use client';

import { XMarkIcon } from '@heroicons/react/24/outline';
import { useEffect, useRef, useState } from 'react';
import { twMerge } from 'tailwind-merge';

import { stopPropagation } from 'util/events';
import { useWindowSize } from 'util/hooks';

const EDGE_PADDING = 20;

export enum DropDownPosition {
  CENTER,
  LEFT,
  RIGHT,
}

export enum DropDownDirection {
  UP,
  DOWN,
}

interface Props {
  children: React.ReactNode;
  className?: string;
  classNameInner?: string;
  direction?: DropDownDirection;
  label: React.ReactNode;
  position?: DropDownPosition;
  preventAutoClose?: boolean;
  selectionClassName?: string;
  showCloseBtn?: boolean;
  style?: React.CSSProperties;
}

export default function DropDown({
  children,
  className,
  classNameInner,
  direction = DropDownDirection.DOWN,
  label,
  position = DropDownPosition.RIGHT,
  preventAutoClose = false,
  selectionClassName,
  showCloseBtn = false,
  style,
}: Props): React.ReactElement {
  const [isOpen, setOpen] = useState(false);
  const ref = useRef<HTMLDivElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const { width: windowWidth } = useWindowSize();

  const bounding = ref.current?.getBoundingClientRect();
  const width = dropdownRef.current?.getBoundingClientRect().width ?? 0;

  const right = windowWidth - (bounding?.right ?? 0);
  const left = bounding?.left ?? 0;
  const center = left + (bounding?.width ?? 0) / 2;
  const top = bounding?.bottom ?? 0;
  const bottom = dropdownRef.current?.getBoundingClientRect().height ?? 0;

  const directionKeyword =
    direction === DropDownDirection.DOWN ? 'top' : 'bottom';
  const directionValue =
    direction === DropDownDirection.DOWN ? top : Math.round(bottom) + 30;

  const offsetKeyword = position === DropDownPosition.RIGHT ? 'right' : 'left';
  const offsetLeft = Math.max(
    EDGE_PADDING,
    Math.min(windowWidth - EDGE_PADDING - width, left),
  );
  const offsetCenter = Math.max(
    EDGE_PADDING - width / 2,
    Math.min(windowWidth - EDGE_PADDING - width / 2, center),
  );
  const offsetRight = Math.max(
    EDGE_PADDING,
    Math.min(windowWidth - EDGE_PADDING - width, right),
  );
  const offsetValue = {
    [DropDownPosition.CENTER]: offsetCenter,
    [DropDownPosition.LEFT]: offsetLeft,
    [DropDownPosition.RIGHT]: offsetRight,
  }[position];

  // Listen for clicks outside the dropdown
  useEffect(() => {
    function onDocumentClick(e: MouseEvent) {
      if (
        preventAutoClose &&
        dropdownRef.current?.contains(e.target as Node)
      ) {
        return;
      }
      setOpen(false);
    }

    if (isOpen) {
      document.addEventListener('click', onDocumentClick);
    } else {
      document.removeEventListener('click', onDocumentClick);
    }

    return () => {
      document.removeEventListener('click', onDocumentClick);
    };
  }, [isOpen, preventAutoClose]);

  // Re-render on scroll
  const [, setScroll] = useState(0);
  useEffect(() => {
    function onScroll() {
      setScroll(window.scrollY);
    }

    if (isOpen) {
      document.addEventListener('scroll', onScroll);
    }

    return () => {
      document.removeEventListener('scroll', onScroll);
    };
  }, [isOpen]);

  return (
    <div className="relative" ref={ref} style={style}>
      <button
        aria-expanded={isOpen}
        aria-haspopup="true"
        className={className}
        onClick={stopPropagation(() => setOpen(!isOpen))}
        type="button"
      >
        {label}
      </button>
      <div className="pointer-events-none fixed inset-0 z-20">
        <div
          aria-labelledby="menu-button"
          aria-orientation="vertical"
          className={twMerge(
            'pointer-events-auto absolute mt-2 w-11/12 rounded-md md:w-56',
            'bg-white shadow-lg ring-1 ring-black/5',
            'z-20 text-left transition focus:outline-none',
            classNameInner,
            position === DropDownPosition.CENTER && '-translate-x-1/2',
            isOpen ? 'duration-100 ease-out' : 'duration-75 ease-in',
            isOpen
              ? 'visible scale-100 opacity-100'
              : 'invisible scale-95 opacity-0',
          )}
          ref={dropdownRef}
          role="menu"
          style={{
            [directionKeyword]: directionValue,
            [offsetKeyword]: offsetValue,
          }}
          tabIndex={-1}
        >
          {showCloseBtn && (
            <div className="my-2 flex items-center py-1 md:border-0 md:pt-3 lg:mb-0 lg:border-0">
              <button
                aria-label="Close"
                className="ml-1 flex size-10 grow-0 flex-col items-center justify-center rounded bg-white transition-colors duration-600 ease-default hover:bg-gray-100 md:ml-6"
                onClick={() => setOpen(false)}
                type="button"
              >
                <XMarkIcon className="size-6" />
              </button>
            </div>
          )}
          <div
            className={twMerge('whitespace-pre-wrap py-1', selectionClassName)}
            role="none"
          >
            {children}
          </div>
        </div>
      </div>
    </div>
  );
}
