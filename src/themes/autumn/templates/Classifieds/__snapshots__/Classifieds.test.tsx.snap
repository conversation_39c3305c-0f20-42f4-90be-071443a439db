// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`classifieds template render categories page correctly 1`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <script
    type="application/ld+json"
  >
    {"@context":"https://schema.org","@type":"BreadcrumbList","itemListElement":[{"@type":"ListItem","position":1,"item":"https://test.com.au/","name":"Home"}]}
  </script>
  <script
    type="application/ld+json"
  >
    {"@context":"https://schema.org","@type":"Organization","legalName":"","name":"","sameAs":[],"url":"https:///"}
  </script>
  <div
    class="bg-white bonzaiWrapper"
  >
    <div
      class="mx-auto w-full max-w-sm md:max-w-lg px-4 md:px-6 xl:px-0 mt-4 md:mt-10"
    >
      <div
        class="relative max-w-full text-xs font-medium md:border-b-1 border-gray-300 pb-2 md:pb-7 md:mb-7"
        id="nav--secondary"
      >
        <nav
          class="flex h-9 items-center justify-start whitespace-nowrap text-sm font-medium normal-case"
        >
          <div
            class="flex gap-2 overflow-x-auto scrollbar-hide"
          >
            <div
              class="inline-block"
            >
              <a
                class="inline-flex shrink-0 items-center rounded-full p-2.5 leading-4 transition-colors duration-600 ease-default focus:transition-none bg-gray-350 hover:bg-gray-100 text-slate-750 text-sm font-medium normal-case"
                data-testid="nav-secondary-link"
                href="/classifieds/"
              >
                <span>
                  All Classifieds
                </span>
              </a>
            </div>
            <div
              class="inline-block"
            >
              <a
                class="inline-flex shrink-0 items-center rounded-full p-2.5 leading-4 transition-colors duration-600 ease-default focus:transition-none bg-gray-350 hover:bg-gray-100 text-slate-750 text-sm font-medium normal-case"
                data-testid="nav-secondary-link"
                href="/classifieds/adult-services/"
              >
                <span>
                  Adult Services
                </span>
              </a>
            </div>
            <div
              class="inline-block"
            >
              <a
                class="inline-flex shrink-0 items-center rounded-full p-2.5 leading-4 transition-colors duration-600 ease-default focus:transition-none bg-gray-350 hover:bg-gray-100 text-slate-750 text-sm font-medium normal-case"
                data-testid="nav-secondary-link"
                href="/classifieds/automotive/"
              >
                <span>
                  Automotive
                </span>
              </a>
            </div>
          </div>
        </nav>
      </div>
      <div
        class="xl:flex"
      >
        <div
          class="flex-1"
        >
          <div
            class="h-auto"
          >
            <div
              class="mb-2 mt-5 flex flex-row items-center justify-end gap-4 md:mb-4 md:mt-0"
            >
              <div
                class="ml-4"
              >
                <span
                  class="md:hidden"
                >
                  Listing
                </span>
                View
              </div>
              <div
                class="flex cursor-pointer flex-row"
                data-headlessui-state=""
              >
                <div>
                  <button
                    aria-expanded="false"
                    aria-haspopup="menu"
                    class="rounded-l-md p-2 text-sm font-semibold shadow-sm ring-1 ring-inset bg-gray-900 ring-gray-900"
                    data-headlessui-state=""
                    id="headlessui-menu-button-:r6:"
                    type="button"
                  >
                    <svg
                      aria-hidden="true"
                      class="size-5 text-white"
                      data-slot="icon"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="1.5"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M3.75 6A2.25 2.25 0 0 1 6 3.75h2.25A2.25 2.25 0 0 1 10.5 6v2.25a2.25 2.25 0 0 1-2.25 2.25H6a2.25 2.25 0 0 1-2.25-2.25V6ZM3.75 15.75A2.25 2.25 0 0 1 6 13.5h2.25a2.25 2.25 0 0 1 2.25 2.25V18a2.25 2.25 0 0 1-2.25 2.25H6A2.25 2.25 0 0 1 3.75 18v-2.25ZM13.5 6a2.25 2.25 0 0 1 2.25-2.25H18A2.25 2.25 0 0 1 20.25 6v2.25A2.25 2.25 0 0 1 18 10.5h-2.25a2.25 2.25 0 0 1-2.25-2.25V6ZM13.5 15.75a2.25 2.25 0 0 1 2.25-2.25H18a2.25 2.25 0 0 1 2.25 2.25V18A2.25 2.25 0 0 1 18 20.25h-2.25A2.25 2.25 0 0 1 13.5 18v-2.25Z"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                    </svg>
                  </button>
                </div>
                <div>
                  <button
                    aria-expanded="false"
                    aria-haspopup="menu"
                    class="rounded-r-md p-2 pr-3 text-sm font-semibold shadow-sm ring-1 ring-inset bg-white ring-gray-300"
                    data-headlessui-state=""
                    id="headlessui-menu-button-:r7:"
                    type="button"
                  >
                    <svg
                      aria-hidden="true"
                      class="-mr-1 size-5 text-gray-900"
                      data-slot="icon"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        clip-rule="evenodd"
                        d="M6 4.75A.75.75 0 0 1 6.75 4h10.5a.75.75 0 0 1 0 1.5H6.75A.75.75 0 0 1 6 4.75ZM6 10a.75.75 0 0 1 .75-.75h10.5a.75.75 0 0 1 0 1.5H6.75A.75.75 0 0 1 6 10Zm0 5.25a.75.75 0 0 1 .75-.75h10.5a.75.75 0 0 1 0 1.5H6.75a.75.75 0 0 1-.75-.75ZM1.99 4.75a1 1 0 0 1 1-1H3a1 1 0 0 1 1 1v.01a1 1 0 0 1-1 1h-.01a1 1 0 0 1-1-1v-.01ZM1.99 15.25a1 1 0 0 1 1-1H3a1 1 0 0 1 1 1v.01a1 1 0 0 1-1 1h-.01a1 1 0 0 1-1-1v-.01ZM1.99 10a1 1 0 0 1 1-1H3a1 1 0 0 1 1 1v.01a1 1 0 0 1-1 1h-.01a1 1 0 0 1-1-1V10Z"
                        fill-rule="evenodd"
                      />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
            <div
              class="pb-4 md:columns-2 lg:columns-3"
            >
              <div>
                <div
                  class="mb-4 flex h-auto flex-col items-start gap-2 rounded-md border border-gray-300 bg-white p-2 break-inside"
                >
                  <button
                    class="relative"
                    type="button"
                  >
                    <picture>
                      <source
                        srcset="https://transform.dev.newsnow.io/transform/v1/resize/frm/longbeach/1e589a6a-aa1d-47cb-b59c-69589c57a59a/w320_h0.webp?resize_filter=lanczos2&quality=100 1x, https://transform.dev.newsnow.io/transform/v1/resize/frm/longbeach/1e589a6a-aa1d-47cb-b59c-69589c57a59a/w640_h0.webp?resize_filter=lanczos2&quality=100 2x"
                        type="image/webp"
                      />
                      <img
                        alt="31782776733811:RM6733811"
                        aria-hidden="true"
                        class="gtm-hook-classified-ad"
                        src="https://transform.dev.newsnow.io/transform/v1/resize/frm/longbeach/1e589a6a-aa1d-47cb-b59c-69589c57a59a/w320_h0.jpg?resize_filter=lanczos2&quality=100"
                        srcset="https://transform.dev.newsnow.io/transform/v1/resize/frm/longbeach/1e589a6a-aa1d-47cb-b59c-69589c57a59a/w320_h0.jpg?resize_filter=lanczos2&quality=100 1x, https://transform.dev.newsnow.io/transform/v1/resize/frm/longbeach/1e589a6a-aa1d-47cb-b59c-69589c57a59a/w640_h0.jpg?resize_filter=lanczos2&quality=100 2x"
                      />
                    </picture>
                    <span
                      class="absolute bottom-2 right-2 flex items-center justify-center rounded border bg-white text-gray-400"
                    >
                      <i>
                        {"icon":{"prefix":"fas","iconName":"magnifying-glass-plus"}}
                      </i>
                    </span>
                  </button>
                  <span
                    class="inline-block rounded-3xl bg-green-100 px-2 py-1 text-center text-xs text-green-700"
                  >
                    Trades & Services
                  </span>
                  <p
                    class="text-xs"
                  >
                    <span>
                      14/09/2020
                    </span>
                  </p>
                </div>
              </div>
            </div>
            <div
              class="mb-6 flex flex-col items-center gap-4 rounded-lg border border-gray-300 px-6 py-8 text-center font-medium md:px-10 md:text-left lg:flex-row"
            >
              <p
                class="grow content-center"
              >
                Post a classified ad and get in front of the 
                 community today, it’s simple.
              </p>
              <a
                class="w-full rounded-3xl bg-yellow-300 px-14 py-4 text-sm hover:bg-yellow-400 active:bg-yellow-500 md:w-auto"
                href="/contact/#classifieds"
              >
                Post an ad
              </a>
            </div>
          </div>
        </div>
        <div
          class="xl:ml-4 xl:w-[375px]"
        >
          <h2
            class="my-8 text-lg font-semibold"
          >
            Other marketplaces
          </h2>
          <ul
            class="-mx-4 flex flex-wrap"
          >
            <li
              class="w-full md:w-1/2 p-4 xl:w-full"
            >
              <a
                class="flex h-20 items-center"
                href="https://advertisers.com.au/?pub=GAN"
              >
                <span
                  class="grow"
                >
                  <span
                    class="truncate text-base font-medium text-gray-900"
                  >
                    Place a Classifieds Ad
                    <svg
                      aria-hidden="true"
                      class="ml-4 inline-block size-5 shrink-0 text-gray-500"
                      data-slot="icon"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        clip-rule="evenodd"
                        d="M4.25 5.5a.75.75 0 0 0-.75.75v8.5c0 .414.336.75.75.75h8.5a.75.75 0 0 0 .75-.75v-4a.75.75 0 0 1 1.5 0v4A2.25 2.25 0 0 1 12.75 17h-8.5A2.25 2.25 0 0 1 2 14.75v-8.5A2.25 2.25 0 0 1 4.25 4h5a.75.75 0 0 1 0 1.5h-5Z"
                        fill-rule="evenodd"
                      />
                      <path
                        clip-rule="evenodd"
                        d="M6.194 12.753a.75.75 0 0 0 1.06.053L16.5 4.44v2.81a.75.75 0 0 0 1.5 0v-4.5a.75.75 0 0 0-.75-.75h-4.5a.75.75 0 0 0 0 1.5h2.553l-9.056 8.194a.75.75 0 0 0-.053 1.06Z"
                        fill-rule="evenodd"
                      />
                    </svg>
                  </span>
                  <p
                    class="mt-1 text-sm text-gray-500"
                  >
                    The best place to place a classifieds Ad for you business
                  </p>
                </span>
              </a>
            </li>
            <li
              class="w-full md:w-1/2 p-4 xl:w-full"
            >
              <a
                class="flex h-20 items-center"
                href="https://www.legacy.com/obituaries/areanews-au"
                rel="noopener"
                target="_blank"
              >
                <span
                  class="grow"
                >
                  <span
                    class="truncate text-base font-medium text-gray-900"
                  >
                    Tributes
                    <svg
                      aria-hidden="true"
                      class="ml-4 inline-block size-5 shrink-0 text-gray-500"
                      data-slot="icon"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        clip-rule="evenodd"
                        d="M4.25 5.5a.75.75 0 0 0-.75.75v8.5c0 .414.336.75.75.75h8.5a.75.75 0 0 0 .75-.75v-4a.75.75 0 0 1 1.5 0v4A2.25 2.25 0 0 1 12.75 17h-8.5A2.25 2.25 0 0 1 2 14.75v-8.5A2.25 2.25 0 0 1 4.25 4h5a.75.75 0 0 1 0 1.5h-5Z"
                        fill-rule="evenodd"
                      />
                      <path
                        clip-rule="evenodd"
                        d="M6.194 12.753a.75.75 0 0 0 1.06.053L16.5 4.44v2.81a.75.75 0 0 0 1.5 0v-4.5a.75.75 0 0 0-.75-.75h-4.5a.75.75 0 0 0 0 1.5h2.553l-9.056 8.194a.75.75 0 0 0-.053 1.06Z"
                        fill-rule="evenodd"
                      />
                    </svg>
                  </span>
                  <p
                    class="mt-1 text-sm text-gray-500"
                  >
                    Offer Condolences & Explore The Lives of Locals
                  </p>
                </span>
              </a>
            </li>
          </ul>
        </div>
      </div>
    </div>
    <div
      class="mx-auto flex w-full justify-center p-4 md:px-10 lg:px-14 lg:py-7 bg-gray-800 mt-10"
      id="footer"
    >
      <div
        class="w-full max-w-lg"
      >
        <div
          class="flex h-24 flex-row items-center justify-between lg:h-32"
        >
          <a
            href="/"
          >
            <div
              class="flex max-h-14 w-52 md:w-72 lg:w-72"
            >
              <img
                alt=""
                class="object-contain"
                loading="lazy"
              />
            </div>
          </a>
          <div
            class="hidden w-44 flex-row space-x-7 lg:flex"
          />
        </div>
        <div
          class="mb-6 border-b-2 border-gray-600 lg:hidden"
        />
        <div
          class="h-auto md:flex md:flex-col md:overflow-hidden lg:grid lg:grid-cols-4"
        >
          <div
            class="hidden"
          >
            <div
              class="flex w-full flex-row justify-between overflow-hidden lg:flex-col"
            >
              <div
                class="h-7 overflow-hidden lg:h-auto flex flex-col space-y-2 pr-8 pt-1 lg:pt-0"
                data-testid="expanded-area-0"
              />
              <div
                class="absolute right-4 flex w-11/12 cursor-pointer justify-end outline-none md:right-10 lg:hidden"
                data-testid="expanded-button-0"
                role="button"
                tabindex="0"
              >
                <svg
                  aria-hidden="true"
                  class="size-6 text-gray-400"
                  data-slot="icon"
                  data-testid="chevron-down-0"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="m19.5 8.25-7.5 7.5-7.5-7.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
            </div>
            <div
              class="my-6 border-b border-gray-600 lg:hidden mb-9 md:mb-0"
            />
          </div>
          <div
            class="hidden"
          >
            <div
              class="flex w-full flex-row justify-between overflow-hidden lg:flex-col"
            >
              <div
                class="h-7 overflow-hidden lg:h-auto flex flex-col space-y-2 pr-8 pt-1 lg:pt-0"
                data-testid="expanded-area-1"
              />
              <div
                class="absolute right-4 flex w-11/12 cursor-pointer justify-end outline-none md:right-10 lg:hidden"
                data-testid="expanded-button-1"
                role="button"
                tabindex="1"
              >
                <svg
                  aria-hidden="true"
                  class="size-6 text-gray-400"
                  data-slot="icon"
                  data-testid="chevron-down-1"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="m19.5 8.25-7.5 7.5-7.5-7.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
            </div>
            <div
              class="my-6 border-b border-gray-600 lg:hidden mb-9 md:mb-0"
            />
          </div>
          <div
            class="hidden"
          >
            <div
              class="flex w-full flex-row justify-between overflow-hidden lg:flex-col"
            >
              <div
                class="h-7 overflow-hidden lg:h-auto flex flex-col space-y-2 pr-8 pt-1 lg:pt-0"
                data-testid="expanded-area-2"
              />
              <div
                class="absolute right-4 flex w-11/12 cursor-pointer justify-end outline-none md:right-10 lg:hidden"
                data-testid="expanded-button-2"
                role="button"
                tabindex="2"
              >
                <svg
                  aria-hidden="true"
                  class="size-6 text-gray-400"
                  data-slot="icon"
                  data-testid="chevron-down-2"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="m19.5 8.25-7.5 7.5-7.5-7.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
            </div>
            <div
              class="my-6 border-b border-gray-600 lg:hidden mb-9 md:mb-0"
            />
          </div>
          <div
            class="hidden"
          >
            <div
              class="flex w-full flex-row justify-between overflow-hidden lg:flex-col"
            >
              <div
                class="h-7 overflow-hidden lg:h-auto flex flex-col space-y-2 pr-8 pt-1 lg:pt-0"
                data-testid="expanded-area-3"
              />
              <div
                class="absolute right-4 flex w-11/12 cursor-pointer justify-end outline-none md:right-10 lg:hidden"
                data-testid="expanded-button-3"
                role="button"
                tabindex="3"
              >
                <svg
                  aria-hidden="true"
                  class="size-6 text-gray-400"
                  data-slot="icon"
                  data-testid="chevron-down-3"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="m19.5 8.25-7.5 7.5-7.5-7.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
            </div>
            <div
              class="my-6 border-b border-gray-600 lg:hidden mb-9 md:mb-0"
            />
          </div>
        </div>
        <div
          class="mt-6 border-b border-gray-600 md:mt-0 lg:hidden"
        />
        <div
          class="flex w-full flex-row gap-x-7 py-9 lg:hidden"
        />
      </div>
    </div>
  </div>
  <div
    class="sticky bottom-0 z-30 flex flex-col"
  >
    <div
      class="z-10 bg-gray-100 md:hidden"
    >
      <button
        aria-label="Close"
        class="absolute -top-6 right-0 mt-px size-6 cursor-pointer rounded-tl-lg bg-gray-100 text-gray-400 shadow-lg"
        type="button"
      >
        <span
          class="flex size-6 place-items-center"
        >
          <svg
            class="mx-auto"
            fill="none"
            height="11"
            viewBox="0 0 10 10"
            width="11"
          >
            <path
              d="M9.8951 0.997373L8.9551 0.057373L5.22843 3.78404L1.50177 0.057373L0.561768 0.997373L4.28843 4.72404L0.561768 8.45071L1.50177 9.39071L5.22843 5.66404L8.9551 9.39071L9.8951 8.45071L6.16843 4.72404L9.8951 0.997373Z"
              fill="#9CA3AF"
            />
          </svg>
        </span>
      </button>
      <div
        class="relative"
      />
    </div>
  </div>
</div>
`;

exports[`classifieds template render results page correctly 1`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <script
    type="application/ld+json"
  >
    {"@context":"https://schema.org","@type":"BreadcrumbList","itemListElement":[{"@type":"ListItem","position":1,"item":"https://test.com.au/","name":"Home"}]}
  </script>
  <script
    type="application/ld+json"
  >
    {"@context":"https://schema.org","@type":"Organization","legalName":"","name":"","sameAs":[],"url":"https:///"}
  </script>
  <div
    class="bg-white bonzaiWrapper"
  >
    <div
      class="mx-auto w-full max-w-sm md:max-w-lg px-4 md:px-6 xl:px-0 mt-4 md:mt-10"
    >
      <div
        class="relative max-w-full text-xs font-medium md:border-b-1 border-gray-300 pb-2 md:pb-7 md:mb-7"
        id="nav--secondary"
      >
        <nav
          class="flex h-9 items-center justify-start whitespace-nowrap text-sm font-medium normal-case"
        >
          <div
            class="flex gap-2 overflow-x-auto scrollbar-hide"
          >
            <div
              class="inline-block"
            >
              <a
                class="inline-flex shrink-0 items-center rounded-full p-2.5 leading-4 transition-colors duration-600 ease-default focus:transition-none bg-gray-350 hover:bg-gray-100 text-slate-750 text-sm font-medium normal-case"
                data-testid="nav-secondary-link"
                href="/classifieds/"
              >
                <span>
                  All Classifieds
                </span>
              </a>
            </div>
          </div>
        </nav>
      </div>
      <div
        class="xl:flex"
      >
        <div
          class="flex-1"
        >
          <div
            class="h-auto"
          >
            <div
              class="mb-2 mt-5 flex flex-row items-center justify-end gap-4 md:mb-4 md:mt-0"
            >
              <div
                class="ml-4"
              >
                <span
                  class="md:hidden"
                >
                  Listing
                </span>
                View
              </div>
              <div
                class="flex cursor-pointer flex-row"
                data-headlessui-state=""
              >
                <div>
                  <button
                    aria-expanded="false"
                    aria-haspopup="menu"
                    class="rounded-l-md p-2 text-sm font-semibold shadow-sm ring-1 ring-inset bg-gray-900 ring-gray-900"
                    data-headlessui-state=""
                    id="headlessui-menu-button-:r2:"
                    type="button"
                  >
                    <svg
                      aria-hidden="true"
                      class="size-5 text-white"
                      data-slot="icon"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="1.5"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M3.75 6A2.25 2.25 0 0 1 6 3.75h2.25A2.25 2.25 0 0 1 10.5 6v2.25a2.25 2.25 0 0 1-2.25 2.25H6a2.25 2.25 0 0 1-2.25-2.25V6ZM3.75 15.75A2.25 2.25 0 0 1 6 13.5h2.25a2.25 2.25 0 0 1 2.25 2.25V18a2.25 2.25 0 0 1-2.25 2.25H6A2.25 2.25 0 0 1 3.75 18v-2.25ZM13.5 6a2.25 2.25 0 0 1 2.25-2.25H18A2.25 2.25 0 0 1 20.25 6v2.25A2.25 2.25 0 0 1 18 10.5h-2.25a2.25 2.25 0 0 1-2.25-2.25V6ZM13.5 15.75a2.25 2.25 0 0 1 2.25-2.25H18a2.25 2.25 0 0 1 2.25 2.25V18A2.25 2.25 0 0 1 18 20.25h-2.25A2.25 2.25 0 0 1 13.5 18v-2.25Z"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                    </svg>
                  </button>
                </div>
                <div>
                  <button
                    aria-expanded="false"
                    aria-haspopup="menu"
                    class="rounded-r-md p-2 pr-3 text-sm font-semibold shadow-sm ring-1 ring-inset bg-white ring-gray-300"
                    data-headlessui-state=""
                    id="headlessui-menu-button-:r3:"
                    type="button"
                  >
                    <svg
                      aria-hidden="true"
                      class="-mr-1 size-5 text-gray-900"
                      data-slot="icon"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        clip-rule="evenodd"
                        d="M6 4.75A.75.75 0 0 1 6.75 4h10.5a.75.75 0 0 1 0 1.5H6.75A.75.75 0 0 1 6 4.75ZM6 10a.75.75 0 0 1 .75-.75h10.5a.75.75 0 0 1 0 1.5H6.75A.75.75 0 0 1 6 10Zm0 5.25a.75.75 0 0 1 .75-.75h10.5a.75.75 0 0 1 0 1.5H6.75a.75.75 0 0 1-.75-.75ZM1.99 4.75a1 1 0 0 1 1-1H3a1 1 0 0 1 1 1v.01a1 1 0 0 1-1 1h-.01a1 1 0 0 1-1-1v-.01ZM1.99 15.25a1 1 0 0 1 1-1H3a1 1 0 0 1 1 1v.01a1 1 0 0 1-1 1h-.01a1 1 0 0 1-1-1v-.01ZM1.99 10a1 1 0 0 1 1-1H3a1 1 0 0 1 1 1v.01a1 1 0 0 1-1 1h-.01a1 1 0 0 1-1-1V10Z"
                        fill-rule="evenodd"
                      />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
            <div
              class="pb-4 md:columns-2 lg:columns-3"
            >
              <div>
                <div
                  class="mb-4 flex h-auto flex-col items-start gap-2 rounded-md border border-gray-300 bg-white p-2 break-inside"
                >
                  <button
                    class="relative"
                    type="button"
                  >
                    <picture>
                      <source
                        srcset="https://transform.dev.newsnow.io/transform/v1/resize/frm/longbeach/1e589a6a-aa1d-47cb-b59c-69589c57a59a/w320_h0.webp?resize_filter=lanczos2&quality=100 1x, https://transform.dev.newsnow.io/transform/v1/resize/frm/longbeach/1e589a6a-aa1d-47cb-b59c-69589c57a59a/w640_h0.webp?resize_filter=lanczos2&quality=100 2x"
                        type="image/webp"
                      />
                      <img
                        alt="31782776733811:RM6733811"
                        aria-hidden="true"
                        class="gtm-hook-classified-ad"
                        src="https://transform.dev.newsnow.io/transform/v1/resize/frm/longbeach/1e589a6a-aa1d-47cb-b59c-69589c57a59a/w320_h0.jpg?resize_filter=lanczos2&quality=100"
                        srcset="https://transform.dev.newsnow.io/transform/v1/resize/frm/longbeach/1e589a6a-aa1d-47cb-b59c-69589c57a59a/w320_h0.jpg?resize_filter=lanczos2&quality=100 1x, https://transform.dev.newsnow.io/transform/v1/resize/frm/longbeach/1e589a6a-aa1d-47cb-b59c-69589c57a59a/w640_h0.jpg?resize_filter=lanczos2&quality=100 2x"
                      />
                    </picture>
                    <span
                      class="absolute bottom-2 right-2 flex items-center justify-center rounded border bg-white text-gray-400"
                    >
                      <i>
                        {"icon":{"prefix":"fas","iconName":"magnifying-glass-plus"}}
                      </i>
                    </span>
                  </button>
                  <span
                    class="inline-block rounded-3xl bg-green-100 px-2 py-1 text-center text-xs text-green-700"
                  >
                    Trades & Services
                  </span>
                  <p
                    class="text-xs"
                  >
                    <span>
                      14/09/2020
                    </span>
                  </p>
                </div>
              </div>
            </div>
            <div
              class="mb-6 flex flex-col items-center gap-4 rounded-lg border border-gray-300 px-6 py-8 text-center font-medium md:px-10 md:text-left lg:flex-row"
            >
              <p
                class="grow content-center"
              >
                Post a classified ad and get in front of the 
                 community today, it’s simple.
              </p>
              <a
                class="w-full rounded-3xl bg-yellow-300 px-14 py-4 text-sm hover:bg-yellow-400 active:bg-yellow-500 md:w-auto"
                href="/contact/#classifieds"
              >
                Post an ad
              </a>
            </div>
          </div>
        </div>
        <div
          class="xl:ml-4 xl:w-[375px]"
        >
          <h2
            class="my-8 text-lg font-semibold"
          >
            Other marketplaces
          </h2>
          <ul
            class="-mx-4 flex flex-wrap"
          >
            <li
              class="w-full md:w-1/2 p-4 xl:w-full"
            >
              <a
                class="flex h-20 items-center"
                href="https://advertisers.com.au/?pub=GAN"
              >
                <span
                  class="grow"
                >
                  <span
                    class="truncate text-base font-medium text-gray-900"
                  >
                    Place a Classifieds Ad
                    <svg
                      aria-hidden="true"
                      class="ml-4 inline-block size-5 shrink-0 text-gray-500"
                      data-slot="icon"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        clip-rule="evenodd"
                        d="M4.25 5.5a.75.75 0 0 0-.75.75v8.5c0 .414.336.75.75.75h8.5a.75.75 0 0 0 .75-.75v-4a.75.75 0 0 1 1.5 0v4A2.25 2.25 0 0 1 12.75 17h-8.5A2.25 2.25 0 0 1 2 14.75v-8.5A2.25 2.25 0 0 1 4.25 4h5a.75.75 0 0 1 0 1.5h-5Z"
                        fill-rule="evenodd"
                      />
                      <path
                        clip-rule="evenodd"
                        d="M6.194 12.753a.75.75 0 0 0 1.06.053L16.5 4.44v2.81a.75.75 0 0 0 1.5 0v-4.5a.75.75 0 0 0-.75-.75h-4.5a.75.75 0 0 0 0 1.5h2.553l-9.056 8.194a.75.75 0 0 0-.053 1.06Z"
                        fill-rule="evenodd"
                      />
                    </svg>
                  </span>
                  <p
                    class="mt-1 text-sm text-gray-500"
                  >
                    The best place to place a classifieds Ad for you business
                  </p>
                </span>
              </a>
            </li>
            <li
              class="w-full md:w-1/2 p-4 xl:w-full"
            >
              <a
                class="flex h-20 items-center"
                href="https://www.legacy.com/obituaries/areanews-au"
                rel="noopener"
                target="_blank"
              >
                <span
                  class="grow"
                >
                  <span
                    class="truncate text-base font-medium text-gray-900"
                  >
                    Tributes
                    <svg
                      aria-hidden="true"
                      class="ml-4 inline-block size-5 shrink-0 text-gray-500"
                      data-slot="icon"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        clip-rule="evenodd"
                        d="M4.25 5.5a.75.75 0 0 0-.75.75v8.5c0 .414.336.75.75.75h8.5a.75.75 0 0 0 .75-.75v-4a.75.75 0 0 1 1.5 0v4A2.25 2.25 0 0 1 12.75 17h-8.5A2.25 2.25 0 0 1 2 14.75v-8.5A2.25 2.25 0 0 1 4.25 4h5a.75.75 0 0 1 0 1.5h-5Z"
                        fill-rule="evenodd"
                      />
                      <path
                        clip-rule="evenodd"
                        d="M6.194 12.753a.75.75 0 0 0 1.06.053L16.5 4.44v2.81a.75.75 0 0 0 1.5 0v-4.5a.75.75 0 0 0-.75-.75h-4.5a.75.75 0 0 0 0 1.5h2.553l-9.056 8.194a.75.75 0 0 0-.053 1.06Z"
                        fill-rule="evenodd"
                      />
                    </svg>
                  </span>
                  <p
                    class="mt-1 text-sm text-gray-500"
                  >
                    Offer Condolences & Explore The Lives of Locals
                  </p>
                </span>
              </a>
            </li>
          </ul>
        </div>
      </div>
    </div>
    <div
      class="mx-auto flex w-full justify-center p-4 md:px-10 lg:px-14 lg:py-7 bg-gray-800 mt-10"
      id="footer"
    >
      <div
        class="w-full max-w-lg"
      >
        <div
          class="flex h-24 flex-row items-center justify-between lg:h-32"
        >
          <a
            href="/"
          >
            <div
              class="flex max-h-14 w-52 md:w-72 lg:w-72"
            >
              <img
                alt=""
                class="object-contain"
                loading="lazy"
              />
            </div>
          </a>
          <div
            class="hidden w-44 flex-row space-x-7 lg:flex"
          />
        </div>
        <div
          class="mb-6 border-b-2 border-gray-600 lg:hidden"
        />
        <div
          class="h-auto md:flex md:flex-col md:overflow-hidden lg:grid lg:grid-cols-4"
        >
          <div
            class="hidden"
          >
            <div
              class="flex w-full flex-row justify-between overflow-hidden lg:flex-col"
            >
              <div
                class="h-7 overflow-hidden lg:h-auto flex flex-col space-y-2 pr-8 pt-1 lg:pt-0"
                data-testid="expanded-area-0"
              />
              <div
                class="absolute right-4 flex w-11/12 cursor-pointer justify-end outline-none md:right-10 lg:hidden"
                data-testid="expanded-button-0"
                role="button"
                tabindex="0"
              >
                <svg
                  aria-hidden="true"
                  class="size-6 text-gray-400"
                  data-slot="icon"
                  data-testid="chevron-down-0"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="m19.5 8.25-7.5 7.5-7.5-7.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
            </div>
            <div
              class="my-6 border-b border-gray-600 lg:hidden mb-9 md:mb-0"
            />
          </div>
          <div
            class="hidden"
          >
            <div
              class="flex w-full flex-row justify-between overflow-hidden lg:flex-col"
            >
              <div
                class="h-7 overflow-hidden lg:h-auto flex flex-col space-y-2 pr-8 pt-1 lg:pt-0"
                data-testid="expanded-area-1"
              />
              <div
                class="absolute right-4 flex w-11/12 cursor-pointer justify-end outline-none md:right-10 lg:hidden"
                data-testid="expanded-button-1"
                role="button"
                tabindex="1"
              >
                <svg
                  aria-hidden="true"
                  class="size-6 text-gray-400"
                  data-slot="icon"
                  data-testid="chevron-down-1"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="m19.5 8.25-7.5 7.5-7.5-7.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
            </div>
            <div
              class="my-6 border-b border-gray-600 lg:hidden mb-9 md:mb-0"
            />
          </div>
          <div
            class="hidden"
          >
            <div
              class="flex w-full flex-row justify-between overflow-hidden lg:flex-col"
            >
              <div
                class="h-7 overflow-hidden lg:h-auto flex flex-col space-y-2 pr-8 pt-1 lg:pt-0"
                data-testid="expanded-area-2"
              />
              <div
                class="absolute right-4 flex w-11/12 cursor-pointer justify-end outline-none md:right-10 lg:hidden"
                data-testid="expanded-button-2"
                role="button"
                tabindex="2"
              >
                <svg
                  aria-hidden="true"
                  class="size-6 text-gray-400"
                  data-slot="icon"
                  data-testid="chevron-down-2"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="m19.5 8.25-7.5 7.5-7.5-7.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
            </div>
            <div
              class="my-6 border-b border-gray-600 lg:hidden mb-9 md:mb-0"
            />
          </div>
          <div
            class="hidden"
          >
            <div
              class="flex w-full flex-row justify-between overflow-hidden lg:flex-col"
            >
              <div
                class="h-7 overflow-hidden lg:h-auto flex flex-col space-y-2 pr-8 pt-1 lg:pt-0"
                data-testid="expanded-area-3"
              />
              <div
                class="absolute right-4 flex w-11/12 cursor-pointer justify-end outline-none md:right-10 lg:hidden"
                data-testid="expanded-button-3"
                role="button"
                tabindex="3"
              >
                <svg
                  aria-hidden="true"
                  class="size-6 text-gray-400"
                  data-slot="icon"
                  data-testid="chevron-down-3"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="m19.5 8.25-7.5 7.5-7.5-7.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
            </div>
            <div
              class="my-6 border-b border-gray-600 lg:hidden mb-9 md:mb-0"
            />
          </div>
        </div>
        <div
          class="mt-6 border-b border-gray-600 md:mt-0 lg:hidden"
        />
        <div
          class="flex w-full flex-row gap-x-7 py-9 lg:hidden"
        />
      </div>
    </div>
  </div>
  <div
    class="sticky bottom-0 z-30 flex flex-col"
  >
    <div
      class="z-10 bg-gray-100 md:hidden"
    >
      <button
        aria-label="Close"
        class="absolute -top-6 right-0 mt-px size-6 cursor-pointer rounded-tl-lg bg-gray-100 text-gray-400 shadow-lg"
        type="button"
      >
        <span
          class="flex size-6 place-items-center"
        >
          <svg
            class="mx-auto"
            fill="none"
            height="11"
            viewBox="0 0 10 10"
            width="11"
          >
            <path
              d="M9.8951 0.997373L8.9551 0.057373L5.22843 3.78404L1.50177 0.057373L0.561768 0.997373L4.28843 4.72404L0.561768 8.45071L1.50177 9.39071L5.22843 5.66404L8.9551 9.39071L9.8951 8.45071L6.16843 4.72404L9.8951 0.997373Z"
              fill="#9CA3AF"
            />
          </svg>
        </span>
      </button>
      <div
        class="relative"
      />
    </div>
  </div>
</div>
`;
