'use client';

import { createSelector } from '@reduxjs/toolkit';
import { usePathname } from 'next/navigation';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import useSWR from 'swr';

import { useAppSelector } from 'store/hooks';
import { PianoABTestingVariant } from 'store/slices/piano';
import {
  storyPageVariants,
  templatePageVariants,
} from 'templates/templatePageVariants';
import { GlobalZoneName } from 'types/ZoneItems';
import { getCookie } from 'util/cookies';
import { getPageHierarchy, getPageParents } from 'util/page';

import { PREMIUM_COOKIES, PREMIUM_EXTENDED_COOKIES } from './constants';
import {
  DeviceType,
  ResponsiveType,
  getDeviceTypeFromWidth,
  getResponsiveTypeFromWidth,
  isLgBreakpoint,
} from './device';
import { fetchWeather } from './organization/sochi';
import {
  fetchStoriesForStoryList,
  fetchUgcItemsForUgcList,
} from './organization/suzuka';
import { DEFAULT_THEME, PAGE_THEME, THEME } from './theme';
import { splitHash, stripQueryParams } from './url';

import type { WeatherZoneOptions } from './organization/sochi';
import type { DependencyList, RefObject } from 'react';
import type { PageThemeVariant, ThemeVariant } from 'store/slices/conf';
import type { PageState } from 'store/slices/page';
import type { Page as ParentPage } from 'store/slices/pages';
import type { RootState } from 'store/store';
import type { Page } from 'types/Nav';
import type { Story } from 'types/Story';
import type { NavigationZoneItem, ZoneItem, ZoneName } from 'types/ZoneItems';
import type { ComponentType } from 'types/theme';
import type { UGC } from 'types/ugc';
import type { PageHierarchy, PageParents } from 'util/page';

export function useEnterKey(
  callback: () => void,
  deps: DependencyList,
): (e: React.KeyboardEvent<HTMLDivElement>) => void {
  return useCallback(
    (e: React.KeyboardEvent<HTMLDivElement>) => {
      if (e.key === 'Enter') {
        callback();
      }
    },
    /* eslint-disable react-hooks/exhaustive-deps */

    [...deps, callback],
    /* eslint-enable react-hooks/exhaustive-deps */
  );
}

export function useAccessToken(): string {
  const token = useAppSelector((state) => state.accessToken);
  if (!token) {
    throw new Error('No access token provided');
  }

  return token;
}

export function useHashObject(): Record<string, string> {
  const isClientSide = useAppSelector((state) => state.runtime.isClientSide);

  const [obj, setObj] = useState<Record<string, string>>(
    isClientSide ? splitHash(window.location.hash) : {},
  );

  const onHashChange = () => {
    setObj(splitHash(window.location.hash));
  };

  useEffect(() => {
    onHashChange();
    window.addEventListener('hashchange', onHashChange);
    return () => {
      window.removeEventListener('hashchange', onHashChange);
    };
  }, []);

  return obj;
}

interface WindowSize {
  height: number;
  outerHeight: number;
  outerWidth: number;
  width: number;
}

export function useWindowSize(): WindowSize {
  // outerWidth is 0 when page is headless loaded
  // for example when user open new tab in background.
  // To avoid this behavior we set `inner` values if 0
  // https://webmasters.stackexchange.com/a/132614

  const isClientSide = useAppSelector((state) => state.runtime.isClientSide);
  const [size, setSize] = useState<WindowSize>(
    isClientSide
      ? {
          height: window.innerHeight,
          outerHeight: window.outerHeight,
          outerWidth: window.outerWidth,
          width: window.innerWidth,
        }
      : {
          height: 0,
          outerHeight: 0,
          outerWidth: 0,
          width: 0,
        },
  );

  useEffect(() => {
    function onWindowResize() {
      setSize({
        height: window.innerHeight,
        outerHeight: window.outerHeight,
        outerWidth: window.outerWidth,
        width: window.innerWidth,
      });
    }

    onWindowResize();
    window.addEventListener('resize', onWindowResize);
    return () => {
      window.removeEventListener('resize', onWindowResize);
    };
  }, [setSize]);

  return size;
}

export function useDeviceTypeFromWidth(): DeviceType {
  // Cause re-render when device type changes based on width
  useWindowSize();
  const isClientSide = useAppSelector((state) => state.runtime.isClientSide);
  if (isClientSide) {
    return getDeviceTypeFromWidth();
  }

  return DeviceType.DESKTOP;
}

export function useResponsiveTypeFromWidth(): ResponsiveType {
  // Cause re-render when device type changes based on width
  useWindowSize();
  const isClientSide = useAppSelector((state) => state.runtime.isClientSide);
  if (isClientSide) {
    return getResponsiveTypeFromWidth();
  }

  return ResponsiveType.DESKTOP;
}

export function useLgBreakPoint(): boolean {
  // Cause re-render when device type changes based on width
  useWindowSize();
  const isClientSide = useAppSelector((state) => state.runtime.isClientSide);
  if (isClientSide) {
    return isLgBreakpoint();
  }

  return false;
}

export function useRegion(): string {
  const location = useAppSelector((state) => state.conf.location);
  return location.split(',')[0];
}

export function useWeather(options?: WeatherZoneOptions) {
  const location = useAppSelector((state) => state.conf.location);
  const fetcher = () => (location ? fetchWeather(location, options) : null);

  return useSWR<Awaited<ReturnType<typeof fetcher>>, Error>(
    `weather:${location}`,
    fetcher,
    {
      revalidateOnFocus: false,
      shouldRetryOnError: false,
    },
  );
}

export const useYoutubeIframeAPI = (function useYoutubeIframeAPIClosure() {
  type Subscriber = (yt: typeof YT) => void;
  let yt: typeof YT | null = null;
  let subscriptions: Subscriber[] = [];
  let loading = false;

  function load() {
    const tag = document.createElement('script');
    tag.src = 'https://www.youtube.com/iframe_api';
    const firstScriptTag = document.getElementsByTagName('script')[0];
    firstScriptTag?.parentNode?.insertBefore(tag, firstScriptTag);
    window.onYouTubeIframeAPIReady = () => {
      yt = window.YT;
      subscriptions.forEach((subscription) => subscription(yt as typeof YT));
      subscriptions = [];
    };
  }

  function subscribe(subscriber: Subscriber) {
    if (yt) {
      subscriber(yt);
    } else {
      subscriptions.push(subscriber);
      if (!loading) {
        loading = true;
        load();
      }
    }
  }

  return function useYoutubeIframeAPIInternal(): typeof YT | undefined {
    const [ytInstance, setYT] = useState<typeof YT>();
    useEffect(() => {
      subscribe((ytSubscribed) => {
        setYT(ytSubscribed);
      });
    }, []);

    return ytInstance;
  };
})();

export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

interface PaginationOptions<T> {
  fetch: (page: number) => Promise<T[]>;
  goToTop?: boolean;
  initialItems?: T[];
  initialPage?: number;
}

export function usePagination<T>({
  fetch,
  goToTop = true,
  initialItems = [],
  initialPage = 1,
}: PaginationOptions<T>) {
  const [currentPage, setPage] = useState(initialPage);
  const [isLoading, setLoading] = useState(false);
  const [items, setItems] = useState(initialItems);
  const [maxPageFetched, setMaxPageFetched] = useState(initialPage);

  async function pageSetter(newPage: number) {
    // Only load if it's a new page and
    // If it wasn't fetched before.
    if (newPage > currentPage && newPage > maxPageFetched) {
      setLoading(true);
      const pageItems = await fetch(newPage);
      setMaxPageFetched(newPage);
      setItems((current) => [...current, ...pageItems]);
      setLoading(false);
    }

    setPage(newPage);
    if (goToTop) {
      window.scrollTo(0, 0);
    }
  }

  return { currentPage, isLoading, items, setPage: pageSetter };
}

export function useUrlPageNumber() {
  const domain = useAppSelector((state) => state.conf.domain);
  const asPath = usePathname() || '';
  const initialFullUrl = `https://${domain}${asPath}`;
  const initialPage = parseInt(
    new URL(initialFullUrl).searchParams.get('page') || '1',
    10,
  );

  const [fullUrlPage, setFullUrlPage] = useState(initialFullUrl);
  const [currentUrlPage, setCurrentUrlPage] = useState(initialPage);

  const setUrlPage = (page: number) => {
    if (page === 1 || page === currentUrlPage) return;

    const newFullUrl = new URL(fullUrlPage);
    newFullUrl.searchParams.set('page', page.toString());
    setFullUrlPage(newFullUrl.toString());
    setCurrentUrlPage(page);
    window.history.pushState(null, '', newFullUrl);
  };

  return { currentUrlPage, setUrlPage };
}

interface PremiumSubscriptionStatus {
  hasPremiumExtended: boolean;
  hasPremiumSubscription: boolean;
  hasValidatedPremium: boolean;
  isPremiumRequest: boolean;
  supportPremiumExtended: boolean;
  supportPremiumSubscription: boolean;
}

export function usePremiumSubscription(): PremiumSubscriptionStatus {
  const isClientSide = useAppSelector((state) => state.runtime.isClientSide);
  const isPremiumSubscription = useAppSelector(
    (state) => state.piano.isPremiumSubscription,
  );
  const isPremiumExtended = useAppSelector(
    (state) => state.piano.isPremiumExtended,
  );
  const pianoFeature = useAppSelector((state) => state.features.piano);
  const supportPremiumSubscription =
    pianoFeature.enabled && pianoFeature.data.supportPremiumSubscription;
  const supportPremiumExtended =
    pianoFeature.enabled && pianoFeature.data.supportPremiumExtended;
  const isPremiumRequest =
    pianoFeature.enabled && pianoFeature.data.isPremiumRequest;
  const hasValidatedPremium = useAppSelector(
    (state) => state.piano.hasValidatedPremium,
  );

  const hasPremiumExtended =
    isPremiumExtended || (isClientSide && getCookie(PREMIUM_EXTENDED_COOKIES));
  const hasPremiumSubscription =
    isPremiumSubscription || (isClientSide && getCookie(PREMIUM_COOKIES));
  return {
    hasPremiumExtended: !!hasPremiumExtended,
    hasPremiumSubscription: !!hasPremiumSubscription,
    hasValidatedPremium,
    isPremiumRequest,
    supportPremiumExtended,
    supportPremiumSubscription,
  };
}

interface ShowAdsState {
  showComponent: boolean;
  showComponentPlaceholder: boolean;
}

// ensure to init the component after checking the permission
export function useLazyLoadComponentState(
  requestExtended = false,
): ShowAdsState {
  const pianoFeature = useAppSelector((state) => state.features.piano);
  const supportPremiumSubscription =
    pianoFeature.enabled && pianoFeature.data.supportPremiumSubscription;
  const supportPremiumExtended =
    pianoFeature.enabled && pianoFeature.data.supportPremiumExtended;
  const isPremiumRequest =
    pianoFeature.enabled && pianoFeature.data.isPremiumRequest;
  const isPremiumSubscription = useAppSelector(
    (state) => state.piano.isPremiumSubscription,
  );
  const isPremiumExtended = useAppSelector(
    (state) => state.piano.isPremiumExtended,
  );
  const [showComponent, setComponent] = useState(false);
  const [showComponentPlaceholder, setShowComponentPlaceholder] =
    useState(!isPremiumRequest);

  useEffect(() => {
    if (supportPremiumSubscription) {
      const hasPremium = isPremiumSubscription || getCookie(PREMIUM_COOKIES);
      const hasExtended =
        isPremiumExtended || getCookie(PREMIUM_EXTENDED_COOKIES);
      if (hasPremium && (!requestExtended || hasExtended)) {
        setComponent(false);
        setShowComponentPlaceholder(false);
      } else {
        setComponent(true);
        setShowComponentPlaceholder(true);
      }
    }
  }, [
    supportPremiumSubscription,
    requestExtended,
    isPremiumExtended,
    isPremiumSubscription,
  ]);

  if (requestExtended && !supportPremiumExtended) {
    return { showComponent: true, showComponentPlaceholder: true };
  }

  if (!supportPremiumSubscription) {
    return { showComponent: true, showComponentPlaceholder: true };
  }

  return { showComponent, showComponentPlaceholder };
}

interface PianoABTestingState {
  enablePianoABTesting: boolean;
  pianoABTestingVariant: PianoABTestingVariant;
}

export function usePianoABTestingState(): PianoABTestingState {
  const pianoFeature = useAppSelector((state) => state.features.piano);
  const isPianoABTestingEnabled =
    pianoFeature.enabled && pianoFeature.data.isAbTesting;
  const abTestingVariant = useAppSelector(
    (state) => state.piano.pianoABTestingVariant,
  );
  const [pianoABTestingVariant, setPianoABTestingVariant] = useState(
    PianoABTestingVariant.NONE,
  );

  useEffect(() => {
    if (isPianoABTestingEnabled) {
      setPianoABTestingVariant(abTestingVariant);
    }
  }, [abTestingVariant, isPianoABTestingEnabled]);

  if (!isPianoABTestingEnabled) {
    return {
      enablePianoABTesting: false,
      pianoABTestingVariant: PianoABTestingVariant.NONE,
    };
  }

  return {
    enablePianoABTesting: isPianoABTestingEnabled,
    pianoABTestingVariant,
  };
}

interface LoadingMoreProps {
  limitWithOffset: number;
  loadStoryCount: number;
  showCanonicalSite: boolean;
  stories: Story[];
  storyListId: number;
  useCanonicalUrl: boolean;
}

interface LoadingMoreResponse {
  loadMore: () => void;
  loadedStories: Story[];
  loadedStoryLimit: number;
  loading: boolean;
  noMoreStories: boolean;
}

export function useLoadingMoreStories({
  limitWithOffset,
  loadStoryCount,
  showCanonicalSite,
  stories,
  storyListId,
  useCanonicalUrl,
}: LoadingMoreProps): LoadingMoreResponse {
  const siteId = useAppSelector((state) => state.settings.siteId);
  const [loadedStories, setLoadedStories] = useState(
    stories.slice(0, limitWithOffset),
  );
  const [loadedStoryLimit, setLoadedStoryLimit] = useState(limitWithOffset);
  const [loadOffset, setLoadOffset] = useState(limitWithOffset);
  const [loading, setLoading] = useState(false);
  const [noMoreStories, setNoMoreStories] = useState(false);

  const loadMore = useCallback(() => {
    (async () => {
      setLoading(true);

      try {
        const fetched = await fetchStoriesForStoryList({
          limit: loadStoryCount,
          offset: loadOffset,
          showCanonicalSite,
          siteId,
          storyListId,
          useCanonicalUrl,
        });

        if (fetched.length < loadStoryCount) {
          setNoMoreStories(true);
        }

        const filteredStories = fetched.filter(
          (story) =>
            !loadedStories.find((loadedStory) => loadedStory.id === story.id),
        );

        const mergedStories = [...loadedStories, ...filteredStories];

        setLoadedStories(mergedStories);
        setLoadedStoryLimit(mergedStories.length);
        // Add the difference of filtered and fetched stories to
        // account for new stories being at the front of the queue
        setLoadOffset(
          loadOffset +
            loadStoryCount +
            (fetched.length - filteredStories.length),
        );
      } catch {
        // Silently Fail
      }

      setLoading(false);
    })().catch(() => {});
  }, [
    loadedStories,
    loadStoryCount,
    siteId,
    showCanonicalSite,
    storyListId,
    useCanonicalUrl,
    loadOffset,
    setLoadOffset,
    setLoading,
  ]);

  return { loadMore, loadedStories, loadedStoryLimit, loading, noMoreStories };
}

interface LoadingMoreUgcProps {
  category?: string;
  dateRange?: string;
  distance?: string;
  limitWithOffset: number;
  loadUgcCount: number;
  location?: string;
  pinnedUgcOnly?: boolean;
  ugcItems: UGC[];
  ugcListId: number;
}

interface LoadingMoreUgcResponse {
  loadMore: (reset?: boolean) => void;
  loadedUgcItems: UGC[];
  loadedUgcLimit: number;
  loading: boolean;
  noMoreUgcItems: boolean;
}

export function useLoadingMoreUgcItems({
  category,
  dateRange,
  distance,
  limitWithOffset,
  loadUgcCount,
  location,
  pinnedUgcOnly,
  ugcItems,
  ugcListId,
}: LoadingMoreUgcProps): LoadingMoreUgcResponse {
  const siteId = useAppSelector((state) => state.settings.siteId);
  const [loadedUgcItems, setLoadedUgcItems] = useState(
    ugcItems.slice(0, limitWithOffset),
  );
  const [loadedUgcLimit, setLoadedUgcLimit] = useState(limitWithOffset);
  const [loadOffset, setLoadOffset] = useState(limitWithOffset);
  const [loading, setLoading] = useState(false);
  const [noMoreUgcItems, setNoMoreUgcItems] = useState(false);

  const loadMore = useCallback(
    (reset = false) => {
      (async () => {
        setLoading(true);

        try {
          const fetched = await fetchUgcItemsForUgcList({
            category,
            dateRange,
            distance,
            limit: loadUgcCount,
            location,
            offset: reset ? 0 : loadOffset,
            pinnedUgcOnly,
            siteId,
            ugcListId,
          });

          const filteredUgcItems = reset
            ? fetched
            : fetched.filter(
                (ugc) =>
                  !loadedUgcItems.some((loadedUgc) => loadedUgc.id === ugc.id),
              );

          let mergedUgcItems: UGC[];
          if (reset) {
            mergedUgcItems = filteredUgcItems;
          } else {
            mergedUgcItems = [...loadedUgcItems, ...filteredUgcItems];
          }

          setNoMoreUgcItems(fetched.length < loadUgcCount);
          setLoadedUgcItems(mergedUgcItems);
          setLoadedUgcLimit(mergedUgcItems.length);
          // Add the difference of filtered and fetched ugc items to
          // account for new ugc items being at the front of the queue
          setLoadOffset(
            reset
              ? limitWithOffset
              : loadOffset +
                  loadUgcCount +
                  (fetched.length - filteredUgcItems.length),
          );
        } catch {
          // Silently Fail
        }

        setLoading(false);
      })().catch(() => {});
    },
    [
      loadUgcCount,
      loadOffset,
      pinnedUgcOnly,
      siteId,
      ugcListId,
      limitWithOffset,
      loadedUgcItems,
      dateRange,
      category,
      location,
      distance,
    ],
  );

  return { loadMore, loadedUgcItems, loadedUgcLimit, loading, noMoreUgcItems };
}

export function usePageTheme() {
  const theme = useAppSelector((state) => state.themeDir);
  const page = useAppSelector((state) => state.page);
  const template = useAppSelector((state) => state.layoutTemplate);
  const pageTemplate = (page.template !== '' && page.template) || template;
  return templatePageVariants[theme]?.[pageTemplate];
}

export function useStoryPageTheme() {
  const theme = useAppSelector((state) => state.themeDir);
  const viewType = useAppSelector((state) => state.settings.viewType);
  return storyPageVariants[theme]?.[viewType];
}

export function useTheme(): ThemeVariant | PageThemeVariant {
  const themeVariant = useAppSelector((state) => state.conf.themeVariant);
  const pageThemeVariant = usePageTheme();
  const storyPageTheme = useStoryPageTheme();
  return storyPageTheme || pageThemeVariant || themeVariant;
}

export function useFetchStyleByTheme(
  componentType: ComponentType,
  key: string,
): string {
  const themeVariant = useAppSelector((state) => state.conf.themeVariant);
  const pageThemeVariant = usePageTheme();
  return (
    (pageThemeVariant &&
      PAGE_THEME[pageThemeVariant]?.[componentType]?.[key]) ||
    THEME[themeVariant]?.[componentType]?.[key] ||
    DEFAULT_THEME[componentType]?.[key] ||
    ''
  );
}

function showPageChildren(page: PageState): Page[] {
  if (!page.altMenuName) {
    return page.children;
  }
  return [
    {
      menuName: page.altMenuName,
      name: page.name,
      newWindow: false,
      url: page.url,
    },
    ...page.children,
  ];
}

function showPageSiblings(page: PageState, parent: ParentPage | null): Page[] {
  let pages: Page[] = [];
  if (parent?.showSiblingsOnChildPages) {
    pages = [...page.siblings];
  }

  if (parent?.altMenuName) {
    pages.unshift({
      menuName: parent.altMenuName,
      name: parent.name,
      newWindow: false,
      url: parent.url,
    });
  }

  return pages;
}

export function usePageHierarchy(): PageHierarchy {
  return getPageHierarchy(
    useAppSelector((state) => state.conf.topDownAdCatTargeting),
    useAppSelector((state) => state.pages),
  );
}

export function usePageParents(): PageParents {
  return getPageParents(useAppSelector((state) => state.pages));
}

export function usePages(): Page[] {
  const page = useAppSelector((state) => state.page);
  const { parent: parentPage } = usePageParents();

  if (parentPage?.id && !page.children.length) {
    return showPageSiblings(page, parentPage);
  }
  return showPageChildren(page);
}

export function useDetectMobileKeyboard(callback: (open: boolean) => void) {
  const KEYBOARD_MIN_SIZE = 300;
  const [previousSize, setPreviousSize] = useState<WindowSize>({
    height: 0,
    outerHeight: 0,
    outerWidth: 0,
    width: 0,
  });

  const windowSize = useWindowSize();

  useEffect(() => {
    if (
      windowSize.height !== 0 &&
      previousSize.height !== 0 &&
      windowSize.width === previousSize.width
    ) {
      if (previousSize.height - windowSize.height >= KEYBOARD_MIN_SIZE) {
        callback(true);
      } else if (
        windowSize.height - previousSize.height >=
        KEYBOARD_MIN_SIZE
      ) {
        callback(false);
      }
    }

    setPreviousSize(windowSize);
  }, [windowSize, previousSize, setPreviousSize, callback]);
}

function calculateElementBottomWithOffset(
  element: Element,
  offset: number,
): number {
  const refViewportOffset = element.getBoundingClientRect().top;
  const refHeight = element.clientHeight;
  const viewportOffset = window.scrollY;
  const windowHeight = window.innerHeight;
  return (
    viewportOffset + refViewportOffset - windowHeight + refHeight + offset
  );
}

interface UseViewOnMobileKeyboardOptions {
  offset: number;
}

export function useViewOnMobileKeyboard(
  ref: RefObject<HTMLElement | null>,
  { offset }: UseViewOnMobileKeyboardOptions,
) {
  const callback = useCallback(
    (open: boolean) => {
      const refElement = ref.current;
      if (!refElement || !open) {
        return;
      }

      window.scrollTo({
        behavior: 'smooth',
        top: calculateElementBottomWithOffset(refElement, offset),
      });
    },
    [ref, offset],
  );

  useDetectMobileKeyboard(callback);
}

interface UseFocusOnMobileKeyboardOptions {
  offset: number;
}

export function useFocusOnMobileKeyboard({
  offset,
}: UseFocusOnMobileKeyboardOptions) {
  const callback = useCallback(
    (open: boolean) => {
      const element = document.activeElement;
      if (!element || !open) {
        return;
      }

      window.scrollTo({
        behavior: 'smooth',
        top: calculateElementBottomWithOffset(element, offset),
      });
    },
    [offset],
  );

  useDetectMobileKeyboard(callback);
}

export function useOnce(effect: () => boolean, deps: DependencyList) {
  const hasResolvedRef = useRef(false);
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const callback = useCallback(effect, deps);

  useEffect(() => {
    if (hasResolvedRef.current) {
      return;
    }

    if (callback()) {
      hasResolvedRef.current = true;
    }
  }, [callback]);
}

export function usePageZoneItems(zone: ZoneName) {
  // Memoize the selector so that calling `filter` does not generate a new
  // array every time, causing re-renders
  const selectZoneItems = createSelector(
    (state: RootState['zoneItems']['page']) => state,
    (items) =>
      items.filter((zoneItem: ZoneItem) => zoneItem.zoneName === zone),
  );

  return selectZoneItems(useAppSelector((state) => state.zoneItems.page));
}

export function useWebPCheck(): boolean {
  // Only to be used for cases where <picture> elements are not able to
  // be used. Edge cases for Firefox 64, Edge 18, Safari <15 are
  // irrelevant for the usecase.

  const isClientSide = useAppSelector((state) => state.runtime.isClientSide);

  if (!isClientSide) {
    return true;
  }

  const elem = document.createElement('canvas');

  if (elem.getContext && elem.getContext('2d')) {
    return elem.toDataURL('image/webp').indexOf('data:image/webp') === 0;
  }

  return false;
}

export function usePageMap(): Record<string, string> | null {
  const { global: zoneItems } = useAppSelector((state) => state.zoneItems);
  const navsZoneItem = zoneItems.find(
    (zoneItem: ZoneItem) => zoneItem.zoneName === GlobalZoneName.SITE_WIDE_NAV,
  );
  return useMemo(() => {
    if (!navsZoneItem?.zoneItemData) {
      return null;
    }
    const { menuTree } = (navsZoneItem as NavigationZoneItem).zoneItemData;

    const pageMap: Record<string, string> = {};
    Object.keys(menuTree).forEach((key) => {
      if (menuTree[key].length > 0) {
        menuTree[key].forEach((item) => {
          if (item.absoluteUrl) {
            pageMap[item.absoluteUrl] = item.name;
          }
        });
      }
    });
    return pageMap;
  }, [navsZoneItem]);
}

interface UseWindowHrefOptions {
  strip?: boolean;
}

export function useWindowHref({ strip = true }: UseWindowHrefOptions = {}) {
  const [href, setHref] = useState('');

  useEffect(() => {
    setHref(
      strip ? stripQueryParams(window.location.href) : window.location.href,
    );
  }, [strip]);

  return href;
}
