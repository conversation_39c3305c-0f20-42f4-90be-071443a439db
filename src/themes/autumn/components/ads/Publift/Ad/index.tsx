'use client';

import { useEffect, useRef } from 'react';

// eslint-disable-next-line import/no-named-as-default
import AdPlaceholder from 'themes/autumn/components/ads/AdPlaceholder';
import { type AdSizeType } from 'util/ads';
import { ResponsiveType } from 'util/device';
import { useResponsiveTypeFromWidth } from 'util/hooks';
import { hasAdSize, useScrollX } from 'util/scrollx';

export interface Props {
  className?: string;
  forwardedRef?: React.Ref<HTMLDivElement>;
  isBonzaiTruSkin?: boolean;
  isScrollXAdSlot?: boolean;
  labelPosition?: string;
  // Tailwind spacing classes - e.g. space-y-2
  labelSpace?: string;
  lgSizes?: AdSizeType;
  mdSizes?: AdSizeType;
  moveWhenGutterAdPresent?: boolean;
  // Tailwind padding classes - e.g. py-2
  placeholderPadding?: string;
  position: number;
  publiftName: string | string[];
  sizes: AdSizeType;
  slotId: string;
  // `true` or `top-n` classname for custom top offset
  sticky?: boolean | string;
  stickyOnTablet?: boolean;
  supportPortal?: boolean;
  targetingArguments?: Record<string, string>;
  withLabel?: boolean;
  withPlaceholder?: boolean;
  withPlaceholderBackground?: boolean;
  xlSizes?: AdSizeType;
}

interface InnerAdProps extends Pick<Props, 'position' | 'targetingArguments'> {
  name: string;
  slotId?: string;
}

// Separate the innermost element so that the effect is triggered when it is
// mounted, not just when the parent has mounted
function InnerAd({
  name,
  position,
  slotId,
  targetingArguments,
}: InnerAdProps) {
  useEffect(() => {
    // Register the ad if Fuse has already finished loading. Ads in the DOM
    // before it has loaded do not need to be registered manually
    if (
      !slotId ||
      (window.fusetag && 'loading' in window.fusetag && window.fusetag.loading)
    ) {
      return;
    }

    window.fusetag?.que.push(() => {
      if (!document.getElementById(slotId)) {
        throw new Error(`Missing ad ${slotId}`);
      }
      (window.fusetag as Fuse).registerZone(slotId);
    });
  }, [slotId]);

  const props: Props['targetingArguments'] = {};

  if (targetingArguments) {
    Object.entries(targetingArguments).forEach(([key, value]) => {
      props[`data-targeting-${key}`] = value;
    });
  }

  return (
    <div
      data-fuse={name}
      data-targeting-pos={position}
      id={slotId}
      // eslint-disable-next-line react/jsx-props-no-spreading
      {...props}
    />
  );
}

// Device types in order of size
const devices = [
  ResponsiveType.MOBILE,
  ResponsiveType.TABLET_NARROW,
  ResponsiveType.TABLET_WIDE,
  ResponsiveType.DESKTOP,
];

export function deviceShouldHaveAd(
  device: ResponsiveType,
  props: {
    lgSizes?: AdSizeType;
    mdSizes?: AdSizeType;
    sizes?: AdSizeType;
    xlSizes?: AdSizeType;
  },
) {
  const deviceIndex = devices.indexOf(device);
  const sizes = [props.sizes, props.mdSizes, props.lgSizes, props.xlSizes].map(
    hasAdSize,
  );

  // If a breakpoint has an explicit ad, or any of the sizes before it do, it
  // should display an ad
  if (sizes[deviceIndex] !== null) {
    return sizes[deviceIndex];
  }

  const sizeSpec = sizes.slice(0, deviceIndex).filter((s) => s !== null);
  return sizeSpec[sizeSpec.length - 1];
}

export default function Ad({
  publiftName: name,
  targetingArguments,
  ...props
}: Props) {
  const responsiveType = useResponsiveTypeFromWidth();
  const ref = useRef<HTMLDivElement>(null);
  const isScrollX = useScrollX(ref, props.sizes);

  // Remove the ad from the DOM if it should not display for a given size.
  // This is required because some ad slots have different behaviour depending
  // on the page, and Publift does not support that
  if (!deviceShouldHaveAd(responsiveType, props)) {
    return null;
  }

  return (
    <AdPlaceholder
      // eslint-disable-next-line react/jsx-props-no-spreading
      {...props}
      isScrollX={isScrollX}
      ref={ref}
      sizeRendered={[0, 0]}
      sizeRenderedPlaceholder={[0, 0]}
    >
      {Array.isArray(name) ? (
        name.map((n) => (
          <InnerAd
            key={n}
            name={n}
            position={props.position}
            targetingArguments={targetingArguments}
          />
        ))
      ) : (
        <InnerAd
          name={name}
          position={props.position}
          slotId={props.slotId}
          targetingArguments={targetingArguments}
        />
      )}
    </AdPlaceholder>
  );
}
