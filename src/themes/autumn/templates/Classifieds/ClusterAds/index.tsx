import clsx from 'clsx';

import { useAppSelector } from 'store/hooks';
import InViewEvent from 'util/inViewEvent';
import { plural } from 'util/text';

import { sendGtmEvent, useSearch } from '../utils';

import type { ClassifiedAd } from 'types/Classified';

interface Props<T> {
  ad: React.ComponentType<{
    isMasonryLayout?: boolean;
    item: ClassifiedAd;
    ref?: React.Ref<T> | undefined;
    updateModalData: (item: ClassifiedAd | undefined) => void;
  }>;
  className?: string;
  isMasonryLayout: boolean;
  updateModalData: (item: ClassifiedAd | undefined) => void;
}

export default function ClusterAds<T extends HTMLElement>({
  ad: Ad,
  className,
  isMasonryLayout,
  updateModalData,
}: Props<T>) {
  const { isSearching } = useSearch();
  const numPerPage =
    useAppSelector((state) => state.classifieds.clusterAds?.numPerPage) || 0;
  const currentPage = useAppSelector(
    (state) => state.classifieds.clusterAds?.currentPage || 1,
  );
  const adsTotalNum =
    useAppSelector((state) => state.classifieds.clusterAds?.totalNum) || 0;
  const adsNum = Math.min(currentPage * numPerPage, adsTotalNum);
  const ads = useAppSelector((state) => state.classifieds.clusterAds?.data);
  const clusterName = useAppSelector((state) => state.cluster.clusterName);

  const title =
    isSearching && adsNum > 0
      ? // eslint-disable-next-line @stylistic/max-len
        `${adsNum} result${plural(adsNum)} from ${clusterName || 'region'} (${adsTotalNum} total)`
      : 'Listings from further away';

  if (!ads?.length) {
    return null;
  }

  const onEnterEvent = (classified: ClassifiedAd) => {
    sendGtmEvent(classified, 'ad_impression');
  };

  return (
    <>
      {title && <p className="mb-4 mt-12 text-lg font-medium">{title}</p>}
      <div className={clsx('mb-4', className)}>
        {ads?.map((ad) => (
          <InViewEvent key={ad.id} onEnterEvent={() => onEnterEvent(ad)}>
            {({ ref }) => (
              <Ad
                isMasonryLayout={isMasonryLayout}
                item={ad}
                ref={ref}
                updateModalData={updateModalData}
              />
            )}
          </InViewEvent>
        ))}
      </div>
    </>
  );
}
