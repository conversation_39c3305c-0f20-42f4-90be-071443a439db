'use client';

import { format } from 'date-fns';
import { useEffect, useState } from 'react';

import { useAppSelector } from 'store/hooks';
import getRandomImage from 'themes/autumn/components/classifieds/getRandomImage';
import AdImage from 'themes/autumn/templates/Classifieds/AdImage';
import {
  getAdImage,
  getTributeAgeDetails,
  sendGtmEvent,
  useSearch,
} from 'themes/autumn/templates/Classifieds/utils';
import { usePageHierarchy } from 'util/hooks';
import InViewEvent from 'util/inViewEvent';
import { mathMaxOrZero } from 'util/number';
import { formatLocationShort } from 'util/string';
import { plural } from 'util/text';

import Pagination from '../CardLayout/Pagination';
import ClusterAds from '../ClusterAds';
import FilterController from '../Controller/FilterController';
import NoAds from '../NoAds';
import PostAnAd from '../PostAnAd';
import SearchWidgetBottom from '../SearchWidgetBottom';
import StorySearchResults from '../StorySearchResults';

import type { LayoutProps } from '../theme';
import type { ClassifiedAd } from 'types/Classified';

interface AdProps {
  item: ClassifiedAd;
  ref?: React.Ref<HTMLAnchorElement> | undefined;
}

export const Ad = ({ item, ref }: AdProps) => {
  const primaryPageUrl = usePageHierarchy().primaryPage?.url;
  const slug = primaryPageUrl ? primaryPageUrl.split('/')[0] : '';
  const url = item.canonicalUrl;

  function onClick() {
    sendGtmEvent(item, 'ad_click');
  }

  const imageUrl = getAdImage(item, slug) || getRandomImage();
  const ageDetails = getTributeAgeDetails(item);

  return (
    <a
      className="flex w-[calc(50%-0.5rem)] flex-col items-center gap-4 rounded-md border border-gray-300 p-4 text-center text-sm sm:w-[calc(33.33%-0.666rem)]"
      href={url}
      onClick={onClick}
      ref={ref}
    >
      <div className="mx-auto flex aspect-square w-full items-center justify-center overflow-hidden rounded-md">
        <AdImage alt={item.title} height={374} url={imageUrl} width={374} />
      </div>
      <span className="inline-block rounded-3xl bg-gray-100 px-2 py-1 text-xs text-gray-800">
        {item.categoryText || item.category.name}
      </span>
      <p className="break-words text-base font-semibold sm:text-sm">
        {item.title}
      </p>
      {ageDetails.length !== 0 && (
        <p className="text-gray-600">
          {ageDetails[0]}
          {ageDetails[1] && (
            <>
              <span className="mx-1 sm:hidden">|</span>
              <br className="hidden sm:block" />
            </>
          )}
          {ageDetails[1]}
        </p>
      )}
      <div className="flex grow flex-col items-center justify-end gap-4">
        {item.quoteText && (
          <p className="font-serif text-lg italic sm:text-sm sm:text-gray-600">
            {item.quoteText}
          </p>
        )}
        <p className="flex gap-2 text-xs text-gray-600">
          {item.location && (
            <>
              {formatLocationShort(item.location)}
              <span className="font-medium text-gray-500">•</span>
            </>
          )}
          {format(item.publicationDate, 'd MMM yy')}
        </p>
      </div>
    </a>
  );
};
Ad.displayName = 'Ad';

export default function TributesLayout({ ads }: LayoutProps) {
  const siteName = useAppSelector((state) => state.conf.name);
  const { isSearching } = useSearch();
  const classifiedsAds = useAppSelector((state) => state.classifieds.ads);
  const clusterAds = useAppSelector((state) => state.classifieds.clusterAds);
  const numPerPage = classifiedsAds?.numPerPage ?? 0;
  const currentAdsPage = classifiedsAds?.currentPage ?? 1;
  const adsTotalNum = classifiedsAds?.totalNum ?? 0;
  const [queryString, setQueryString] = useState('');
  const adsNum = Math.min(currentAdsPage * numPerPage, adsTotalNum);
  const paginationNumPage = mathMaxOrZero(ads.numPages, clusterAds?.numPages);
  const paginationCurrentPage = mathMaxOrZero(
    currentAdsPage,
    clusterAds?.currentPage,
  );
  const paginationNextPage = mathMaxOrZero(ads.nextPage, clusterAds?.nextPage);
  const paginationPreviousPage = mathMaxOrZero(
    ads.previousPage,
    clusterAds?.previousPage,
  );

  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    urlParams.set('page', '');
    setQueryString(`?${urlParams.toString()}`);
  }, []);

  const onEnterEvent = (classified: ClassifiedAd) => {
    sendGtmEvent(classified, 'ad_impression');
  };

  return (
    <>
      <FilterController isCardLayout={false} />
      {isSearching && adsNum > 0 && (
        <div className="mb-4 text-lg font-medium sm:text-xl">
          {adsNum} result{plural(adsNum)} from {siteName} ({adsTotalNum} total)
        </div>
      )}
      {!ads?.data?.length ? (
        <>
          <PostAnAd />
          <NoAds />
        </>
      ) : (
        <div className="my-4 flex flex-wrap gap-4">
          {ads.data.map((item) => (
            <InViewEvent key={item.id} onEnterEvent={() => onEnterEvent(item)}>
              {({ ref }) => <Ad item={item} ref={ref} />}
            </InViewEvent>
          ))}
        </div>
      )}
      <ClusterAds
        ad={Ad}
        className="flex flex-wrap gap-4"
        isMasonryLayout={false}
        updateModalData={() => {}}
      />
      <StorySearchResults />
      {!isSearching && !!ads?.data?.length && <SearchWidgetBottom />}
      <Pagination
        currentPage={paginationCurrentPage}
        nextPage={paginationNextPage}
        numPages={paginationNumPage}
        previousPage={paginationPreviousPage}
        queryString={queryString}
      />
    </>
  );
}
