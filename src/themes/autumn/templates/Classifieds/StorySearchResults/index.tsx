import { useAppSelector } from 'store/hooks';
import { StrapListView } from 'util/ZoneItems/storylist';
import { plural } from 'util/text';

export default function StorySearchResults() {
  const stories = useAppSelector((state) => state.classifieds.stories);

  if (!stories || stories.length === 0) {
    return null;
  }

  return (
    <>
      <p className="mt-12 text-lg font-medium">
        {stories.length} result{plural(stories.length)} from News &amp;
        Tributes
      </p>
      <StrapListView
        isHeroImage={false}
        largeLeadStory={false}
        limit={20}
        // @ts-expect-error Using only a minimal subset of story attributes
        stories={stories.map((story, index) => ({ index, story }))}
      />
    </>
  );
}
