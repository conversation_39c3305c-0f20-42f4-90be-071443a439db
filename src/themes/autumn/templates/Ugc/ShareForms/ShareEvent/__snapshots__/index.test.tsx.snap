// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<ShareEvent /> renders 1`] = `
<div>
  <div
    class="z-50"
    id="modal-portal"
  />
  <script
    type="application/ld+json"
  >
    {"@context":"https://schema.org","@type":"BreadcrumbList","itemListElement":[{"@type":"ListItem","position":1,"item":"https:///","name":"Home"}]}
  </script>
  <script
    type="application/ld+json"
  >
    {"@context":"https://schema.org","@type":"Organization","legalName":"","name":"","sameAs":[],"url":"https:///"}
  </script>
  <div
    class="h-0 w-full"
  />
  <div
    class="top-0 z-30 w-full bg-white px-6 md:shadow-md sticky shadow-md"
  >
    <div
      class="relative mx-auto flex h-16 w-full max-w-container items-center justify-between"
    >
      <div
        class="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2"
      >
        <a
          class="block h-9 w-56 text-blue-600 underline decoration-blue-400 hover:text-blue-600 hover:decoration-blue-600 visited:text-gray-500 visited:decoration-gray-500"
          data-testid="logo"
          href="/"
        >
          <img
            alt=""
            class="size-full max-h-full max-w-full object-contain"
          />
        </a>
      </div>
      <div
        class="hidden flex-row md:flex"
      />
    </div>
  </div>
  <div
    class="bg-white bonzaiWrapper"
  >
    <div
      class="flex flex-col items-center border-t border-gray-300 p-4"
    >
      <div
        class="flex max-w-[600px] flex-col items-center font-inter text-gray-900"
      >
        <div
          class="m-4 flex w-full flex-col space-y-4 rounded border border-gray-300 p-[23px] shadow-lg opacity-25"
        >
          <div
            class="absolute inset-0 top-2/3 z-10 flex items-center justify-center"
          >
            <svg
              aria-hidden="true"
              class="svg-inline--fa fa-circle-notch fa-spin text-5xl text-slate-800"
              data-icon="circle-notch"
              data-prefix="fas"
              focusable="false"
              role="img"
              viewBox="0 0 512 512"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M222.7 32.1c5 16.9-4.6 34.8-21.5 39.8C121.8 95.6 64 169.1 64 256c0 106 86 192 192 192s192-86 192-192c0-86.9-57.8-160.4-137.1-184.1c-16.9-5-26.6-22.9-21.5-39.8s22.9-26.6 39.8-21.5C434.9 42.1 512 140 512 256c0 141.4-114.6 256-256 256S0 397.4 0 256C0 140 77.1 42.1 182.9 10.6c16.9-5 34.8 4.6 39.8 21.5z"
                fill="currentColor"
              />
            </svg>
          </div>
          <h2
            class="font-inter text-3xl font-semibold leading-9 text-slate-900"
          >
            Share an Event
          </h2>
          <p
            class="font-inter text-sm text-slate-600"
          >
            You can now contribute content to our Noticeboard.
             
            <br
              class="hidden md:block"
            />
            All content
             
            <span
              class="font-semibold"
            >
              will be moderated
            </span>
             
            before being published.
          </p>
          <form
            novalidate=""
          >
            <div
              class="space-y-6"
            >
              <div>
                <div
                  class="mb-1.5 text-sm font-medium leading-tight text-slate-900"
                >
                  Your Name
                   
                  *
                </div>
                <div
                  class="relative"
                >
                  <input
                    class="h-10 w-full text-ellipsis rounded border py-3 pl-5 text-sm leading-tight placeholder:leading-tight placeholder:text-slate-400 border-slate-300 pr-5 opacity-100"
                    maxlength="100"
                    name="user_name"
                    type="text"
                    value=""
                  />
                </div>
              </div>
              <div>
                <div
                  class="mb-1.5 text-sm font-medium leading-tight text-slate-900"
                >
                  Organiser / Display Name
                   
                  *
                </div>
                <div
                  class="relative"
                >
                  <input
                    class="h-10 w-full text-ellipsis rounded border py-3 pl-5 text-sm leading-tight placeholder:leading-tight placeholder:text-slate-400 border-slate-300 pr-5 opacity-100"
                    maxlength="100"
                    name="organiser_name"
                    type="text"
                    value=""
                  />
                </div>
              </div>
              <div
                class="flex items-center space-x-8"
              >
                <div>
                  <div
                    class="text-sm font-medium leading-tight text-slate-900"
                  >
                    Organiser Logo
                     
                  </div>
                  <div
                    class="flex md:flex-row md:justify-end lg:flex-row lg:justify-end w-full !w-fit"
                  >
                    <button
                      class="flex items-center justify-center rounded-md bg-white disabled:bg-gray-300 md:h-10 lg:h-10 hover:bg-slate-100 disabled:hover:bg-gray-300 shadow-md md:w-auto w-full"
                      type="button"
                    >
                      <span
                        class="py-2 font-medium leading-6 text-sm px-4 text-gray-900"
                      >
                        Upload
                      </span>
                    </button>
                  </div>
                  <input
                    accept="image/png, image/jpeg"
                    class="hidden"
                    name="organiser_logo"
                    type="file"
                  />
                  <ul
                    class="mt-2 list-inside list-disc text-sm text-slate-600"
                  />
                </div>
              </div>
              <div>
                <div
                  class="mb-1.5 text-sm font-medium leading-tight text-slate-900"
                >
                  Contact Email
                   
                  *
                </div>
                <div
                  class="relative"
                >
                  <input
                    class="h-10 w-full text-ellipsis rounded border py-3 pl-5 text-sm leading-tight placeholder:leading-tight placeholder:text-slate-400 border-slate-300 pr-5 opacity-100"
                    maxlength="100"
                    name="organiser_email"
                    type="email"
                    value=""
                  />
                </div>
              </div>
              <div>
                <div
                  class="mb-1.5 text-sm font-medium leading-tight text-slate-900"
                >
                  Contact Number
                   
                  *
                </div>
                <div
                  class="relative"
                >
                  <input
                    class="h-10 w-full text-ellipsis rounded border py-3 pl-5 text-sm leading-tight placeholder:leading-tight placeholder:text-slate-400 border-slate-300 pr-5 opacity-100"
                    maxlength="100"
                    name="organiser_contact_number"
                    type="text"
                    value=""
                  />
                </div>
              </div>
              <div>
                <div
                  class="mb-1.5 text-sm font-medium leading-tight text-slate-900"
                >
                  Contact Website
                   
                </div>
                <div
                  class="relative"
                >
                  <input
                    class="h-10 w-full text-ellipsis rounded border py-3 pl-5 text-sm leading-tight placeholder:leading-tight placeholder:text-slate-400 border-slate-300 pr-5 opacity-100"
                    maxlength="100"
                    name="organiser_website_url"
                    type="url"
                    value=""
                  />
                </div>
              </div>
              <div>
                <div
                  class="mb-1.5 text-sm font-medium leading-tight text-slate-900"
                >
                  Event Title
                   
                  *
                </div>
                <div
                  class="relative"
                >
                  <input
                    class="h-10 w-full text-ellipsis rounded border py-3 pl-5 text-sm leading-tight placeholder:leading-tight placeholder:text-slate-400 border-slate-300 pr-5 opacity-100"
                    maxlength="100"
                    name="title"
                    type="text"
                    value=""
                  />
                </div>
              </div>
              <div>
                <div
                  class="mb-1.5 text-sm font-medium leading-tight text-slate-900"
                >
                  Event Category
                   
                  *
                </div>
                <select
                  class="h-10 rounded-md text-sm border-slate-300"
                  name="category"
                >
                  <option
                    disabled=""
                    value=""
                  >
                    Select a category
                  </option>
                </select>
              </div>
              <div
                class="w-full max-w-md"
              >
                <div
                  class="mb-1.5 text-sm font-medium leading-tight text-slate-900"
                >
                  Event Location
                  *
                </div>
                <div
                  class="relative"
                  data-headlessui-state=""
                >
                  <input
                    aria-autocomplete="list"
                    aria-expanded="false"
                    class="h-10 w-full text-ellipsis rounded border py-3 pl-5 text-sm leading-tight placeholder:leading-tight placeholder:text-slate-400 border-slate-300 pr-5"
                    data-headlessui-state=""
                    id="headlessui-combobox-input-:r2:"
                    role="combobox"
                    type="text"
                    value=""
                  />
                </div>
              </div>
              <div>
                <div
                  class="mb-1.5 text-sm font-medium leading-tight text-slate-900"
                >
                  Select date or date range *
                </div>
                <div>
                  <input
                    class="h-[38px] border-r-0 border-gray-300 text-sm shadow-sm focus:border-sky-500 focus:ring-sky-500 md:text-xs lg:text-sm w-1/2 rounded-l-full lg:w-36"
                    min="1970-01-01"
                    name="start_datetime"
                    type="date"
                  />
                  <input
                    class="h-[38px] w-1/2 rounded-r-full border-gray-300 text-sm shadow-sm focus:border-sky-500 focus:ring-sky-500 md:text-xs lg:w-36 lg:text-sm"
                    max="1971-01-01"
                    min="1970-01-01"
                    name="end_datetime"
                    type="date"
                  />
                </div>
              </div>
              <div
                class="flex flex-col gap-2 text-sm font-medium"
              >
                Does the event repeat?
                <button
                  aria-label="Toggle"
                  class="relative h-5.5 w-11 rounded-full transition-all !w-10 bg-gray-200 duration-100"
                  type="button"
                >
                  <div
                    class="absolute top-1/2 size-7 -translate-y-1/2 rounded-full border bg-white shadow transition-all !size-5 left-0 border-gray-200"
                  />
                </button>
              </div>
              <div>
                <div
                  class="mb-1.5 text-sm font-medium leading-tight text-slate-900"
                >
                  Event Start Time
                   
                </div>
                <div
                  class="relative"
                >
                  <input
                    class="h-10 w-full text-ellipsis rounded border py-3 pl-5 text-sm leading-tight placeholder:leading-tight placeholder:text-slate-400 border-slate-300 pr-5 opacity-100"
                    maxlength="50"
                    name="start_time_text"
                    type="text"
                    value=""
                  />
                  <div
                    class="mt-1 text-sm text-gray-500"
                  >
                    0 characters (max 50)
                  </div>
                </div>
              </div>
              <div>
                <div
                  class="mb-1.5 text-sm font-medium leading-tight text-slate-900"
                >
                  Event Price
                   
                </div>
                <div
                  class="relative"
                >
                  <input
                    class="h-10 w-full text-ellipsis rounded border py-3 pl-5 text-sm leading-tight placeholder:leading-tight placeholder:text-slate-400 border-slate-300 pr-5 opacity-100"
                    maxlength="100"
                    name="price_text"
                    type="text"
                    value=""
                  />
                </div>
              </div>
              <div>
                <div
                  class="mb-1.5 text-sm font-medium leading-tight text-slate-900"
                >
                  Event Description
                   
                  *
                </div>
                <div
                  class="relative"
                >
                  <div
                    class="relative mt-2 w-full rounded-md border-1 border-slate-300 text-sm text-gray-600"
                  >
                    <div
                      class="rounded-t-md border-b-1 border-slate-300 bg-white"
                    >
                      <div
                        class="mx-2 flex flex-wrap items-stretch gap-x-3"
                      >
                        <button
                          aria-label="Bold"
                          class="flex size-10 items-center justify-center"
                          title="Bold"
                          type="button"
                        >
                          <div
                            class="flex size-8 items-center justify-center rounded-md text-lg font-medium text-black"
                          >
                            <svg
                              fill="none"
                              height="16"
                              stroke="currentColor"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="2"
                              viewBox="0 0 24 24"
                              width="16"
                            >
                              <path
                                d="M6 4h8a4 4 0 0 1 4 4 4 4 0 0 1-4 4H6z"
                              />
                              <path
                                d="M6 12h9a4 4 0 0 1 4 4 4 4 0 0 1-4 4H6z"
                              />
                            </svg>
                          </div>
                        </button>
                        <button
                          aria-label="Italic"
                          class="flex size-10 items-center justify-center"
                          title="Italic"
                          type="button"
                        >
                          <div
                            class="flex size-8 items-center justify-center rounded-md text-lg font-medium text-black"
                          >
                            <svg
                              fill="none"
                              height="16"
                              stroke="currentColor"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="2"
                              viewBox="0 0 24 24"
                              width="16"
                            >
                              <path
                                d="M19 4h-9M14 20H5M14.7 4.7L9.2 19.4"
                              />
                            </svg>
                          </div>
                        </button>
                        <button
                          aria-label="Bullet list"
                          class="flex size-10 items-center justify-center"
                          title="Bullet list"
                          type="button"
                        >
                          <div
                            class="flex size-8 items-center justify-center rounded-md text-lg font-medium text-black"
                          >
                            <svg
                              class="size-4"
                              fill="none"
                              height="16"
                              stroke="currentColor"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="2.5"
                              viewBox="0 0 24 24"
                              width="16"
                            >
                              <line
                                x1="8"
                                x2="21"
                                y1="6"
                                y2="6"
                              />
                              <line
                                x1="8"
                                x2="21"
                                y1="12"
                                y2="12"
                              />
                              <line
                                x1="8"
                                x2="21"
                                y1="18"
                                y2="18"
                              />
                              <line
                                x1="3"
                                x2="3.01"
                                y1="6"
                                y2="6"
                              />
                              <line
                                x1="3"
                                x2="3.01"
                                y1="12"
                                y2="12"
                              />
                              <line
                                x1="3"
                                x2="3.01"
                                y1="18"
                                y2="18"
                              />
                            </svg>
                          </div>
                        </button>
                        <button
                          aria-label="Link"
                          class="flex size-10 items-center justify-center"
                          title="Link"
                          type="button"
                        >
                          <div
                            class="flex size-8 items-center justify-center rounded-md text-lg font-medium text-black"
                          >
                            <svg
                              fill="none"
                              height="16"
                              stroke="currentColor"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="2"
                              viewBox="0 0 24 24"
                              width="16"
                            >
                              <path
                                d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71"
                              />
                              <path
                                d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71"
                              />
                               
                            </svg>
                          </div>
                        </button>
                      </div>
                    </div>
                    <div
                      class="prose prose-sm mx-auto min-h-20 px-4 py-2 focus:outline-none prose-p:my-1 prose-p:text-gray-900 prose-strong:text-gray-900"
                    >
                      <div
                        class="tiptap ProseMirror focus:outline-none min-h-20"
                        contenteditable="true"
                        tabindex="0"
                        translate="no"
                      >
                        <p>
                          <br
                            class="ProseMirror-trailingBreak"
                          />
                        </p>
                      </div>
                    </div>
                  </div>
                  <div
                    class="text-sm text-gray-500"
                  >
                    0 words
                  </div>
                </div>
              </div>
              <div>
                <div
                  class="text-sm font-medium leading-tight text-slate-900"
                >
                  Pictures (max 5)
                   
                  *
                </div>
                <div
                  class="flex md:flex-row md:justify-end lg:flex-row lg:justify-end w-full !w-fit"
                >
                  <button
                    class="flex items-center justify-center rounded-md bg-white disabled:bg-gray-300 md:h-10 lg:h-10 hover:bg-slate-100 disabled:hover:bg-gray-300 shadow-md md:w-auto w-full"
                    type="button"
                  >
                    <span
                      class="py-2 font-medium leading-6 text-sm px-4 text-gray-900"
                    >
                      Upload pictures
                    </span>
                  </button>
                </div>
                <input
                  accept="image/png, image/jpeg"
                  class="hidden"
                  multiple=""
                  name="images"
                  type="file"
                />
                <ul
                  class="mt-2 list-inside list-disc text-sm text-slate-600"
                />
              </div>
              <div
                class="text-xs text-gray-500 md:text-sm"
              >
                Please upload at least 1 photo. The first one will be used as lead image. 
                <br
                  class="hidden md:block"
                />
                Accepted formats: PNG, JPG, JPEG
              </div>
              <div
                class="flex gap-x-2"
              >
                <div
                  class="flex md:flex-row md:justify-end lg:flex-row lg:justify-end md:w-full w-full mt-4"
                >
                  <button
                    class="flex items-center justify-center rounded-md bg-white disabled:bg-gray-300 h-10.5 hover:bg-gray-300 disabled:hover:bg-gray-300 shadow-md md:w-full w-full"
                    type="button"
                  >
                    <span
                      class="py-2 font-medium leading-6 text-sm px-4 text-gray"
                    >
                      Preview
                    </span>
                  </button>
                </div>
                <div
                  class="flex md:flex-row md:justify-end lg:flex-row lg:justify-end md:w-full w-full mt-4"
                >
                  <button
                    class="flex items-center justify-center rounded-md bg-red-600 disabled:bg-gray-300 h-10.5 hover:bg-red-700 disabled:hover:bg-gray-300 shadow-md md:w-full w-full"
                    type="submit"
                  >
                    <span
                      class="py-2 font-medium leading-6 text-sm px-4 text-white"
                    >
                      Submit
                    </span>
                  </button>
                </div>
              </div>
            </div>
          </form>
          <p
            class="font-inter text-sm text-slate-600"
          >
            All content will be moderated before being published. You will be notified when your story is published.
          </p>
        </div>
      </div>
    </div>
  </div>
  <div
    class="sticky bottom-0 z-30 flex flex-col"
  />
</div>
`;
