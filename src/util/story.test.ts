import { getCategorySlugFromStoryTags, getTopicsFromStoryTags } from './story';

describe('story util', () => {
  describe('#getTopicsFromStoryTags', () => {
    it('should return empty array if no topic tags', () => {
      const tags = ['tag-1', 'tag-2', 'tag-3'];

      const result = getTopicsFromStoryTags(tags);

      expect(result).toEqual([]);
    });

    it('should return topics from tags', () => {
      const tags = ['tag-1', 'topic-tag-2', 'topic-tag-3'];

      const result = getTopicsFromStoryTags(tags);

      expect(result).toEqual(['Tag 2', 'Tag 3']);
    });
  });

  describe('#getCategorySlugFromStoryTags', () => {
    it('should return undefined if no category or signpost tags', () => {
      const tags = ['tag-1', 'tag-2', 'tag-3'];

      const result = getCategorySlugFromStoryTags(tags);

      expect(result).toBeUndefined();
    });

    it('should return category from category tag', () => {
      const tags = ['tag-1', 'category-tag-2', 'tag-3'];

      const result = getCategorySlugFromStoryTags(tags);

      expect(result).toBe('tag-2');
    });

    it('should return category from signpost tag', () => {
      const tags = ['tag-1', 'signpost-tag-2', 'tag-3'];

      const result = getCategorySlugFromStoryTags(tags);

      expect(result).toBe('tag-2');
    });

    // eslint-disable-next-line @stylistic/max-len
    it('should get category first if both category and signpost tags exist', () => {
      const tags = ['tag-1', 'category-tag-2', 'signpost-tag-3', 'tag-4'];

      const result = getCategorySlugFromStoryTags(tags);

      expect(result).toBe('tag-2');
    });
  });
});
