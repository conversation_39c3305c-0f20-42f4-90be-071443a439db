import { faBath, faBed, faGavel } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import Link from 'themes/autumn/components/generic/Link';
import { type FarmbuyProperty, ListingType } from 'types/Farmbuy';
import { sendToGtm } from 'util/gtm';

interface FarmbuyPropertyCardProps {
  farmbuyProperty: FarmbuyProperty;
}

function FarmbuyPropertyCard({
  farmbuyProperty: {
    address1,
    address2,
    bathroom,
    bedroom,
    description = '',
    finalUrl,
    imageUrl,
    listingType,
    priceOrDate,
  },
}: FarmbuyPropertyCardProps) {
  return (
    <div className="w-72 rounded-lg border border-solid border-gray-300 bg-white pb-6 shadow-sm">
      <Link
        href={finalUrl}
        noStyle
        onClick={() => {
          sendToGtm({
            label: 'farmbuy_property_click',
            trigger: 'farmbuy_property_trigger',
          });
        }}
        target="_blank"
      >
        <img
          alt="Farmbuy Property"
          className="h-40 w-full rounded-t-lg object-fill"
          src={imageUrl}
        />
        <div className="px-3 pt-3 text-sm text-gray-600">
          <p className="text-base font-bold text-gray-800">{address1}</p>
          <p className="">{address2}</p>
          <div className="mt-3 flex gap-6 font-semibold">
            {!!bedroom && (
              <div>
                <FontAwesomeIcon className="mr-2" icon={faBed} />
                {bedroom}
              </div>
            )}
            {!!bathroom && (
              <div>
                <FontAwesomeIcon className="mr-2" icon={faBath} />
                {bathroom}
              </div>
            )}
            <span>{description}</span>
          </div>
          {listingType === ListingType.FOR_SALE ? (
            <>
              <div className="text-sm">For Sale</div>
              <div className="text-xs">
                <span className="font-bold">$</span> {priceOrDate}
              </div>
            </>
          ) : (
            <>
              <div className="text-sm">Auction</div>
              <div className="align-bottom text-xs">
                <FontAwesomeIcon className="mr-1" icon={faGavel} />{' '}
                {priceOrDate}
              </div>
            </>
          )}
        </div>
      </Link>
    </div>
  );
}

export default FarmbuyPropertyCard;
