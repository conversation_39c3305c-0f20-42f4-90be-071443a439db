import clsx from 'clsx';
import { twMerge } from 'tailwind-merge';

import { useAppSelector } from 'store/hooks';
import Link from 'themes/autumn/components/generic/Link';
import { anchorProps } from 'themes/autumn/components/page/PageNavigation';
import { trimSlashes } from 'util/page';

import type { Page } from 'types/Nav';

interface LogoProps {
  containerClassName?: string;
  imageClassName?: string;
}

export function ExploreTravelLogo({
  containerClassName,
  imageClassName,
}: LogoProps) {
  const { staticUrl } = useAppSelector((state) => state.settings);
  return (
    <div
      className={twMerge(
        'flex-stretch flex w-24 shrink-0 grow-0 items-center justify-center justify-self-start md:mt-0 md:w-28',
        containerClassName,
      )}
    >
      <Link data-testid="logo" href="/travel/">
        <img
          alt="Explore Travel"
          className={twMerge(
            'size-full h-11 max-h-full w-28 max-w-full object-contain',
            imageClassName,
          )}
          // eslint-disable-next-line @stylistic/max-len
          src={`${staticUrl}/sites/explore-travel/images/masthead/masthead-underline.svg`}
        />
      </Link>
    </div>
  );
}

interface ExploreTravelSubNavProps {
  pages: Page[];
}

export default function ExploreTravelSubNav({
  pages,
}: ExploreTravelSubNavProps) {
  const { url } = useAppSelector((state) => state.page);
  const isExploreTravelHome = trimSlashes(url) === 'travel';

  return (
    <nav
      aria-label="Main Navigation"
      className="md:flex md:h-full md:items-center md:justify-center"
      role="navigation"
    >
      {isExploreTravelHome && (
        // eslint-disable-next-line @stylistic/max-len
        <ExploreTravelLogo containerClassName="mx-auto md:hidden mt-1 w-[108px]" />
      )}
      <div className="flex h-full items-stretch gap-x-4 overflow-x-auto whitespace-nowrap rounded-sm px-4 font-inter text-sm font-medium leading-3 text-gray-900 scrollbar-hide md:gap-x-3 md:font-normal">
        <ExploreTravelLogo
          containerClassName={clsx({
            'hidden md:flex': isExploreTravelHome,
          })}
        />
        {pages.map((page) => (
          <Link
            className={twMerge(
              'flex flex-col items-center justify-center rounded-sm border-b-2 border-transparent py-5 hover:border-gray-900 md:px-3',
              clsx({
                'border-gray-900': trimSlashes(page.url) === trimSlashes(url),
                'pb-4 pt-2 md:py-5': isExploreTravelHome,
              }),
            )}
            key={page.url}
            noStyle
            // eslint-disable-next-line react/jsx-props-no-spreading
            {...anchorProps(page)}
          >
            {page.name}
          </Link>
        ))}
      </div>
    </nav>
  );
}
