import clsx from 'clsx';

import { useAppSelector } from 'store/hooks';
import Link from 'themes/autumn/components/generic/Link';
import { sendToGtm } from 'util/gtm';
import { useOnce } from 'util/hooks';
import useSportSponsorData from 'util/sportsHubSponsor';

interface Props {
  className?: string;
}

export default function SportSponsorBanner({
  className,
}: Props): React.ReactElement | null {
  const sponsorData = useSportSponsorData();

  const pagesLength = useAppSelector((state) => state.pages.length);

  const hasBanner =
    sponsorData &&
    sponsorData.bannerDesktopUrl &&
    sponsorData.bannerMobileUrl &&
    pagesLength < 3; // Only show banners up 2nd level index only

  useOnce(() => {
    if (hasBanner) {
      sendToGtm({
        action: 'impression',
        label: sponsorData.name.toLowerCase(),
        section: 'others',
        trigger: 'widget_sponsorship',
      });
      return true;
    }
    return false;
  }, [hasBanner, sponsorData]);

  if (!hasBanner) {
    return null;
  }

  return (
    <Link
      href={sponsorData.url}
      noStyle
      onClick={() => {
        sendToGtm({
          action: 'click',
          label: sponsorData.name.toLowerCase(),
          section: 'others',
          trigger: 'widget_sponsorship',
        });
      }}
      target={sponsorData.url.startsWith('http') ? '_blank' : '_self'}
    >
      <div className={clsx('mt-8 md:mt-0 lg:pr-7', className)}>
        <picture>
          <source
            media="(max-width: 1023px)" // Tablet
            srcSet={sponsorData.bannerMobileUrl}
          />
          <img
            alt="Mobile App"
            className="w-full rounded-lg"
            src={sponsorData.bannerDesktopUrl}
          />
        </picture>
      </div>
    </Link>
  );
}
