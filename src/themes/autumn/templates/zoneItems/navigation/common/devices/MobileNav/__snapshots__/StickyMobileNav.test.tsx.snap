// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`StickyMobileNav closes anchor box when clicking an anchor item 1`] = `
<div>
  <div
    class="flex h-12 w-full items-center justify-between bg-white py-1.5 pl-3 shadow-md min-[390px]:pr-4"
  >
    <div
      class="mr-3 pr-3 border-r border-gray-200"
    >
      <button
        aria-label="Open menu"
        class="size-10 rounded bg-white px-2 transition-colors duration-600 ease-default hover:bg-gray-100 focus:transition-none"
        data-testid="megamenu-button"
        type="button"
      >
        <svg
          class="mx-auto fill-current text-gray-900"
          height="16"
          viewBox="0 0 24 16"
          width="24"
        >
          <path
            clip-rule="evenodd"
            d="M0.644302 3.16345V0.663452H23.1443V3.16345H0.644302ZM0.644287 9.41339H23.1443V6.91339H0.644287V9.41339ZM0.644287 15.6634H23.1443V13.1634H0.644287V15.6634Z"
            fill-rule="evenodd"
          />
        </svg>
      </button>
    </div>
    <div
      class="flex grow flex-row items-center justify-between"
    >
      <div
        class="flex grow"
      >
        <div
          class="group-focus-within:opacity-0 lg:ml-4 xl:relative"
        >
          <button
            aria-label="In this article"
            class="pl-2 font-inter text-base font-bold xl:pl-4"
            type="button"
          >
            In this article
            <svg
              aria-hidden="true"
              class="svg-inline--fa fa-chevron-up ml-2 text-sm"
              data-icon="chevron-up"
              data-prefix="fas"
              focusable="false"
              role="img"
              viewBox="0 0 512 512"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M233.4 105.4c12.5-12.5 32.8-12.5 45.3 0l192 192c12.5 12.5 12.5 32.8 0 45.3s-32.8 12.5-45.3 0L256 173.3 86.6 342.6c-12.5 12.5-32.8 12.5-45.3 0s-12.5-32.8 0-45.3l192-192z"
                fill="currentColor"
              />
            </svg>
          </button>
          <div
            class="absolute left-0 z-[-1] w-full xl:left-auto xl:z-0"
          >
            <div
              class="grid grid-cols-1 divide-y divide-gray-200 bg-white px-4 shadow-md md:place-items-center xl:w-[375px] xl:place-items-start xl:rounded-lg "
            >
              <div
                class="w-full py-3 md:w-[375px] xl:w-full mt-3"
              >
                <button
                  aria-label="First Heading"
                  class="line-clamp-2 w-full text-left font-inter text-base font-medium leading-9 text-gray-800 focus:outline-none md:text-center xl:text-left"
                  type="button"
                >
                  First Heading
                </button>
              </div>
              <div
                class="w-full py-3 md:w-[375px] xl:w-full"
              >
                <button
                  aria-label="Second Heading"
                  class="line-clamp-2 w-full text-left font-inter text-base font-medium leading-9 text-gray-800 focus:outline-none md:text-center xl:text-left"
                  type="button"
                >
                  Second Heading
                </button>
              </div>
            </div>
          </div>
        </div>
        <div
          class="grow"
        />
      </div>
      <div
        class="relative"
      >
        <button
          aria-expanded="false"
          aria-haspopup="true"
          class="font-semibold"
          type="button"
        >
          <div
            class="mr-2.5 flex flex-row items-center rounded bg-white px-2.5 py-2 font-inter text-sm font-medium text-gray-900 transition-colors duration-600 ease-default hover:bg-gray-100 focus:transition-none"
          >
            <svg
              class="mr-2.5 fill-current"
              fill="none"
              height="14"
              viewBox="0 0 14 14"
              width="14"
            >
              <path
                d="M11 5.39998C12.3255 5.39998 13.4 4.32546 13.4 2.99998C13.4 1.67449 12.3255 0.599976 11 0.599976C9.67449 0.599976 8.59997 1.67449 8.59997 2.99998C8.59997 3.10036 8.60613 3.19931 8.6181 3.29646L4.66605 5.27249C4.2344 4.85609 3.6471 4.59998 2.99997 4.59998C1.67449 4.59998 0.599976 5.67449 0.599976 6.99998C0.599976 8.32546 1.67449 9.39997 2.99997 9.39997C3.64711 9.39997 4.23442 9.14385 4.66608 8.72743L8.61811 10.7035C8.60614 10.8006 8.59997 10.8996 8.59997 11C8.59997 12.3255 9.67449 13.4 11 13.4C12.3255 13.4 13.4 12.3255 13.4 11C13.4 9.67449 12.3255 8.59998 11 8.59998C10.3528 8.59998 9.76554 8.85609 9.33389 9.27249L5.38184 7.29646C5.39381 7.19931 5.39997 7.10036 5.39997 6.99998C5.39997 6.89957 5.39381 6.80061 5.38183 6.70345L9.33387 4.72743C9.76552 5.14385 10.3528 5.39998 11 5.39998Z"
              />
            </svg>
            Share
          </div>
        </button>
        <div
          class="pointer-events-none fixed inset-0 z-20"
        >
          <div
            aria-labelledby="menu-button"
            aria-orientation="vertical"
            class="pointer-events-auto absolute mt-2 w-11/12 rounded-md md:w-56 bg-white shadow-lg ring-1 ring-black/5 z-20 text-left transition focus:outline-none duration-75 ease-in invisible scale-95 opacity-0"
            role="menu"
            style="top: 0px; right: 1004px;"
            tabindex="-1"
          >
            <div
              class="whitespace-pre-wrap py-1"
              role="none"
            >
              <a
                class="flex items-center text-sm text-gray-700 hover:text-gray-900 gtm-hook-share-facebook appearance-none px-4 py-2 hover:bg-gray-100"
                href="https://www.facebook.com/sharer/sharer.php?u=http%3A%2F%2Flocalhost%2F"
                rel="noopener"
                role="menuitem"
                tabindex="-1"
                target="_blank"
              >
                <span
                  class="fa-stack fa-fw transition-opacity hover:opacity-70 "
                  data-testid="share-item-facebook"
                >
                  <i>
                    {"icon":{"prefix":"fas","iconName":"circle"},"className":"fa-stack-2x text-blue-500"}
                  </i>
                  <i>
                    {"icon":{"prefix":"fab","iconName":"facebook"},"className":"fa-stack-1x text-white","inverse":true}
                  </i>
                </span>
                <span
                  class="pl-2"
                >
                  Facebook
                </span>
              </a>
              <a
                class="flex items-center text-sm text-gray-700 hover:text-gray-900 gtm-hook-share-twitter appearance-none px-4 py-2 hover:bg-gray-100"
                href="https://twitter.com/share?url=http%3A%2F%2Flocalhost%2F"
                rel="noopener"
                role="menuitem"
                tabindex="-1"
                target="_blank"
              >
                <span
                  class="fa-stack fa-fw transition-opacity hover:opacity-70 "
                  data-testid="share-item-twitter"
                >
                  <i>
                    {"icon":{"prefix":"fas","iconName":"circle"},"className":"fa-stack-2x text-black"}
                  </i>
                  <i>
                    {"icon":{"prefix":"fab","iconName":"x-twitter"},"className":"fa-stack-1x text-white","inverse":true}
                  </i>
                </span>
                <span
                  class="pl-2"
                >
                  Twitter
                </span>
              </a>
              <a
                class="flex items-center text-sm text-gray-700 hover:text-gray-900 gtm-hook-share-whatsapp appearance-none px-4 py-2 hover:bg-gray-100"
                href="https://wa.me/?text=http%3A%2F%2Flocalhost%2F"
                rel="noopener"
                role="menuitem"
                tabindex="-1"
                target="_blank"
              >
                <span
                  class="fa-stack fa-fw transition-opacity hover:opacity-70 "
                  data-testid="share-item-whatsapp"
                >
                  <i>
                    {"icon":{"prefix":"fas","iconName":"circle"},"className":"fa-stack-2x text-green-400"}
                  </i>
                  <i>
                    {"icon":{"prefix":"fab","iconName":"whatsapp"},"className":"fa-stack-1x text-white","inverse":true}
                  </i>
                </span>
                <span
                  class="pl-2"
                >
                  Whatsapp
                </span>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`StickyMobileNav closes anchor box when clicking outside 1`] = `
<div>
  <div
    class="flex h-12 w-full items-center justify-between bg-white py-1.5 pl-3 shadow-md min-[390px]:pr-4"
  >
    <div
      class="mr-3 pr-3 border-r border-gray-200"
    >
      <button
        aria-label="Open menu"
        class="size-10 rounded bg-white px-2 transition-colors duration-600 ease-default hover:bg-gray-100 focus:transition-none"
        data-testid="megamenu-button"
        type="button"
      >
        <svg
          class="mx-auto fill-current text-gray-900"
          height="16"
          viewBox="0 0 24 16"
          width="24"
        >
          <path
            clip-rule="evenodd"
            d="M0.644302 3.16345V0.663452H23.1443V3.16345H0.644302ZM0.644287 9.41339H23.1443V6.91339H0.644287V9.41339ZM0.644287 15.6634H23.1443V13.1634H0.644287V15.6634Z"
            fill-rule="evenodd"
          />
        </svg>
      </button>
    </div>
    <div
      class="flex grow flex-row items-center justify-between"
    >
      <div
        class="flex grow"
      >
        <div
          class="group-focus-within:opacity-0 lg:ml-4 xl:relative"
        >
          <button
            aria-label="In this article"
            class="pl-2 font-inter text-base font-bold xl:pl-4"
            type="button"
          >
            In this article
            <svg
              aria-hidden="true"
              class="svg-inline--fa fa-chevron-up ml-2 text-sm"
              data-icon="chevron-up"
              data-prefix="fas"
              focusable="false"
              role="img"
              viewBox="0 0 512 512"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M233.4 105.4c12.5-12.5 32.8-12.5 45.3 0l192 192c12.5 12.5 12.5 32.8 0 45.3s-32.8 12.5-45.3 0L256 173.3 86.6 342.6c-12.5 12.5-32.8 12.5-45.3 0s-12.5-32.8 0-45.3l192-192z"
                fill="currentColor"
              />
            </svg>
          </button>
          <div
            class="absolute left-0 z-[-1] w-full xl:left-auto xl:z-0"
          >
            <div
              class="grid grid-cols-1 divide-y divide-gray-200 bg-white px-4 shadow-md md:place-items-center xl:w-[375px] xl:place-items-start xl:rounded-lg "
            >
              <div
                class="w-full py-3 md:w-[375px] xl:w-full mt-3"
              >
                <button
                  aria-label="First Heading"
                  class="line-clamp-2 w-full text-left font-inter text-base font-medium leading-9 text-gray-800 focus:outline-none md:text-center xl:text-left"
                  type="button"
                >
                  First Heading
                </button>
              </div>
              <div
                class="w-full py-3 md:w-[375px] xl:w-full"
              >
                <button
                  aria-label="Second Heading"
                  class="line-clamp-2 w-full text-left font-inter text-base font-medium leading-9 text-gray-800 focus:outline-none md:text-center xl:text-left"
                  type="button"
                >
                  Second Heading
                </button>
              </div>
            </div>
          </div>
        </div>
        <div
          class="grow"
        />
      </div>
      <div
        class="relative"
      >
        <button
          aria-expanded="false"
          aria-haspopup="true"
          class="font-semibold"
          type="button"
        >
          <div
            class="mr-2.5 flex flex-row items-center rounded bg-white px-2.5 py-2 font-inter text-sm font-medium text-gray-900 transition-colors duration-600 ease-default hover:bg-gray-100 focus:transition-none"
          >
            <svg
              class="mr-2.5 fill-current"
              fill="none"
              height="14"
              viewBox="0 0 14 14"
              width="14"
            >
              <path
                d="M11 5.39998C12.3255 5.39998 13.4 4.32546 13.4 2.99998C13.4 1.67449 12.3255 0.599976 11 0.599976C9.67449 0.599976 8.59997 1.67449 8.59997 2.99998C8.59997 3.10036 8.60613 3.19931 8.6181 3.29646L4.66605 5.27249C4.2344 4.85609 3.6471 4.59998 2.99997 4.59998C1.67449 4.59998 0.599976 5.67449 0.599976 6.99998C0.599976 8.32546 1.67449 9.39997 2.99997 9.39997C3.64711 9.39997 4.23442 9.14385 4.66608 8.72743L8.61811 10.7035C8.60614 10.8006 8.59997 10.8996 8.59997 11C8.59997 12.3255 9.67449 13.4 11 13.4C12.3255 13.4 13.4 12.3255 13.4 11C13.4 9.67449 12.3255 8.59998 11 8.59998C10.3528 8.59998 9.76554 8.85609 9.33389 9.27249L5.38184 7.29646C5.39381 7.19931 5.39997 7.10036 5.39997 6.99998C5.39997 6.89957 5.39381 6.80061 5.38183 6.70345L9.33387 4.72743C9.76552 5.14385 10.3528 5.39998 11 5.39998Z"
              />
            </svg>
            Share
          </div>
        </button>
        <div
          class="pointer-events-none fixed inset-0 z-20"
        >
          <div
            aria-labelledby="menu-button"
            aria-orientation="vertical"
            class="pointer-events-auto absolute mt-2 w-11/12 rounded-md md:w-56 bg-white shadow-lg ring-1 ring-black/5 z-20 text-left transition focus:outline-none duration-75 ease-in invisible scale-95 opacity-0"
            role="menu"
            style="top: 0px; right: 1004px;"
            tabindex="-1"
          >
            <div
              class="whitespace-pre-wrap py-1"
              role="none"
            >
              <a
                class="flex items-center text-sm text-gray-700 hover:text-gray-900 gtm-hook-share-facebook appearance-none px-4 py-2 hover:bg-gray-100"
                href="https://www.facebook.com/sharer/sharer.php?u=http%3A%2F%2Flocalhost%2F"
                rel="noopener"
                role="menuitem"
                tabindex="-1"
                target="_blank"
              >
                <span
                  class="fa-stack fa-fw transition-opacity hover:opacity-70 "
                  data-testid="share-item-facebook"
                >
                  <i>
                    {"icon":{"prefix":"fas","iconName":"circle"},"className":"fa-stack-2x text-blue-500"}
                  </i>
                  <i>
                    {"icon":{"prefix":"fab","iconName":"facebook"},"className":"fa-stack-1x text-white","inverse":true}
                  </i>
                </span>
                <span
                  class="pl-2"
                >
                  Facebook
                </span>
              </a>
              <a
                class="flex items-center text-sm text-gray-700 hover:text-gray-900 gtm-hook-share-twitter appearance-none px-4 py-2 hover:bg-gray-100"
                href="https://twitter.com/share?url=http%3A%2F%2Flocalhost%2F"
                rel="noopener"
                role="menuitem"
                tabindex="-1"
                target="_blank"
              >
                <span
                  class="fa-stack fa-fw transition-opacity hover:opacity-70 "
                  data-testid="share-item-twitter"
                >
                  <i>
                    {"icon":{"prefix":"fas","iconName":"circle"},"className":"fa-stack-2x text-black"}
                  </i>
                  <i>
                    {"icon":{"prefix":"fab","iconName":"x-twitter"},"className":"fa-stack-1x text-white","inverse":true}
                  </i>
                </span>
                <span
                  class="pl-2"
                >
                  Twitter
                </span>
              </a>
              <a
                class="flex items-center text-sm text-gray-700 hover:text-gray-900 gtm-hook-share-whatsapp appearance-none px-4 py-2 hover:bg-gray-100"
                href="https://wa.me/?text=http%3A%2F%2Flocalhost%2F"
                rel="noopener"
                role="menuitem"
                tabindex="-1"
                target="_blank"
              >
                <span
                  class="fa-stack fa-fw transition-opacity hover:opacity-70 "
                  data-testid="share-item-whatsapp"
                >
                  <i>
                    {"icon":{"prefix":"fas","iconName":"circle"},"className":"fa-stack-2x text-green-400"}
                  </i>
                  <i>
                    {"icon":{"prefix":"fab","iconName":"whatsapp"},"className":"fa-stack-1x text-white","inverse":true}
                  </i>
                </span>
                <span
                  class="pl-2"
                >
                  Whatsapp
                </span>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`StickyMobileNav renders back link when no heading elements are available 1`] = `
<div
  class="flex h-12 w-full items-center justify-between bg-white py-1.5 pl-3 shadow-md min-[390px]:pr-4"
>
  <div
    class="mr-3 pr-3 border-r border-gray-200"
  >
    <button
      aria-label="Open menu"
      class="size-10 rounded bg-white px-2 transition-colors duration-600 ease-default hover:bg-gray-100 focus:transition-none"
      data-testid="megamenu-button"
      type="button"
    >
      <svg
        class="mx-auto fill-current text-gray-900"
        height="16"
        viewBox="0 0 24 16"
        width="24"
      >
        <path
          clip-rule="evenodd"
          d="M0.644302 3.16345V0.663452H23.1443V3.16345H0.644302ZM0.644287 9.41339H23.1443V6.91339H0.644287V9.41339ZM0.644287 15.6634H23.1443V13.1634H0.644287V15.6634Z"
          fill-rule="evenodd"
        />
      </svg>
    </button>
  </div>
  <div
    class="flex grow flex-row items-center justify-between"
  >
    <a
      class="flex flex-none flex-row items-center justify-start rounded bg-white px-2.5 py-2 text-sm font-medium transition-colors duration-600 ease-default hover:bg-gray-100 focus:transition-none"
      href="/news"
    >
      <svg
        class="fill-current text-gray-900"
        fill="none"
        height="11"
        viewBox="0 0 17 11"
        width="17"
      >
        <path
          clip-rule="evenodd"
          d="M6.50716 10.2071C6.11663 10.5976 5.48347 10.5976 5.09294 10.2071L1.09294 6.2071C0.702418 5.81658 0.702418 5.18342 1.09294 4.79289L5.09294 0.792894C5.48347 0.402369 6.11663 0.402369 6.50716 0.792893C6.89768 1.18342 6.89768 1.81658 6.50716 2.20711L4.21426 4.5L15.8 4.5C16.3523 4.5 16.8 4.94771 16.8 5.5C16.8 6.05228 16.3523 6.5 15.8 6.5L4.21426 6.5L6.50716 8.79289C6.89768 9.18341 6.89768 9.81658 6.50716 10.2071Z"
          fill-rule="evenodd"
        />
      </svg>
      <span
        class="ml-2.5"
      >
        Back to News
      </span>
    </a>
    <div
      data-testid="viafoura-tray"
    >
      Viafoura Tray
    </div>
    <div
      class="relative"
    >
      <button
        aria-expanded="false"
        aria-haspopup="true"
        class="font-semibold"
        type="button"
      >
        <div
          class="mr-2.5 flex flex-row items-center rounded bg-white px-2.5 py-2 font-inter text-sm font-medium text-gray-900 transition-colors duration-600 ease-default hover:bg-gray-100 focus:transition-none"
        >
          <svg
            class="mr-2.5 fill-current"
            fill="none"
            height="14"
            viewBox="0 0 14 14"
            width="14"
          >
            <path
              d="M11 5.39998C12.3255 5.39998 13.4 4.32546 13.4 2.99998C13.4 1.67449 12.3255 0.599976 11 0.599976C9.67449 0.599976 8.59997 1.67449 8.59997 2.99998C8.59997 3.10036 8.60613 3.19931 8.6181 3.29646L4.66605 5.27249C4.2344 4.85609 3.6471 4.59998 2.99997 4.59998C1.67449 4.59998 0.599976 5.67449 0.599976 6.99998C0.599976 8.32546 1.67449 9.39997 2.99997 9.39997C3.64711 9.39997 4.23442 9.14385 4.66608 8.72743L8.61811 10.7035C8.60614 10.8006 8.59997 10.8996 8.59997 11C8.59997 12.3255 9.67449 13.4 11 13.4C12.3255 13.4 13.4 12.3255 13.4 11C13.4 9.67449 12.3255 8.59998 11 8.59998C10.3528 8.59998 9.76554 8.85609 9.33389 9.27249L5.38184 7.29646C5.39381 7.19931 5.39997 7.10036 5.39997 6.99998C5.39997 6.89957 5.39381 6.80061 5.38183 6.70345L9.33387 4.72743C9.76552 5.14385 10.3528 5.39998 11 5.39998Z"
            />
          </svg>
          Share
        </div>
      </button>
      <div
        class="pointer-events-none fixed inset-0 z-20"
      >
        <div
          aria-labelledby="menu-button"
          aria-orientation="vertical"
          class="pointer-events-auto absolute mt-2 w-11/12 rounded-md md:w-56 bg-white shadow-lg ring-1 ring-black/5 z-20 text-left transition focus:outline-none duration-75 ease-in invisible scale-95 opacity-0"
          role="menu"
          style="top: 0px; right: 1004px;"
          tabindex="-1"
        >
          <div
            class="whitespace-pre-wrap py-1"
            role="none"
          >
            <a
              class="flex items-center text-sm text-gray-700 hover:text-gray-900 gtm-hook-share-facebook appearance-none px-4 py-2 hover:bg-gray-100"
              href="https://www.facebook.com/sharer/sharer.php?u=http%3A%2F%2Flocalhost%2F"
              rel="noopener"
              role="menuitem"
              tabindex="-1"
              target="_blank"
            >
              <span
                class="fa-stack fa-fw transition-opacity hover:opacity-70 "
                data-testid="share-item-facebook"
              >
                <i>
                  {"icon":{"prefix":"fas","iconName":"circle"},"className":"fa-stack-2x text-blue-500"}
                </i>
                <i>
                  {"icon":{"prefix":"fab","iconName":"facebook"},"className":"fa-stack-1x text-white","inverse":true}
                </i>
              </span>
              <span
                class="pl-2"
              >
                Facebook
              </span>
            </a>
            <a
              class="flex items-center text-sm text-gray-700 hover:text-gray-900 gtm-hook-share-twitter appearance-none px-4 py-2 hover:bg-gray-100"
              href="https://twitter.com/share?url=http%3A%2F%2Flocalhost%2F"
              rel="noopener"
              role="menuitem"
              tabindex="-1"
              target="_blank"
            >
              <span
                class="fa-stack fa-fw transition-opacity hover:opacity-70 "
                data-testid="share-item-twitter"
              >
                <i>
                  {"icon":{"prefix":"fas","iconName":"circle"},"className":"fa-stack-2x text-black"}
                </i>
                <i>
                  {"icon":{"prefix":"fab","iconName":"x-twitter"},"className":"fa-stack-1x text-white","inverse":true}
                </i>
              </span>
              <span
                class="pl-2"
              >
                Twitter
              </span>
            </a>
            <a
              class="flex items-center text-sm text-gray-700 hover:text-gray-900 gtm-hook-share-whatsapp appearance-none px-4 py-2 hover:bg-gray-100"
              href="https://wa.me/?text=http%3A%2F%2Flocalhost%2F"
              rel="noopener"
              role="menuitem"
              tabindex="-1"
              target="_blank"
            >
              <span
                class="fa-stack fa-fw transition-opacity hover:opacity-70 "
                data-testid="share-item-whatsapp"
              >
                <i>
                  {"icon":{"prefix":"fas","iconName":"circle"},"className":"fa-stack-2x text-green-400"}
                </i>
                <i>
                  {"icon":{"prefix":"fab","iconName":"whatsapp"},"className":"fa-stack-1x text-white","inverse":true}
                </i>
              </span>
              <span
                class="pl-2"
              >
                Whatsapp
              </span>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`StickyMobileNav renders correctly in non-story view 1`] = `
<div
  class="flex h-12 w-full items-center justify-between bg-white py-1.5 pl-3 shadow-md min-[390px]:pr-4"
>
  <div
    class="mr-3 pr-3 border-r border-gray-200"
  >
    <button
      aria-label="Open menu"
      class="size-10 rounded bg-white px-2 transition-colors duration-600 ease-default hover:bg-gray-100 focus:transition-none"
      data-testid="megamenu-button"
      type="button"
    >
      <svg
        class="mx-auto fill-current text-gray-900"
        height="16"
        viewBox="0 0 24 16"
        width="24"
      >
        <path
          clip-rule="evenodd"
          d="M0.644302 3.16345V0.663452H23.1443V3.16345H0.644302ZM0.644287 9.41339H23.1443V6.91339H0.644287V9.41339ZM0.644287 15.6634H23.1443V13.1634H0.644287V15.6634Z"
          fill-rule="evenodd"
        />
      </svg>
    </button>
  </div>
  <div
    class="flex grow flex-row items-center justify-between"
  >
    <div
      class="flex grow"
    >
      <div
        class="group-focus-within:opacity-0 lg:ml-4 xl:relative"
      >
        <button
          aria-label="In this article"
          class="pl-2 font-inter text-base font-bold xl:pl-4"
          type="button"
        >
          In this article
          <svg
            aria-hidden="true"
            class="svg-inline--fa fa-chevron-down ml-2 text-sm"
            data-icon="chevron-down"
            data-prefix="fas"
            focusable="false"
            role="img"
            viewBox="0 0 512 512"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M233.4 406.6c12.5 12.5 32.8 12.5 45.3 0l192-192c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L256 338.7 86.6 169.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3l192 192z"
              fill="currentColor"
            />
          </svg>
        </button>
      </div>
      <div
        class="grow"
      />
    </div>
    <div
      class="relative"
    >
      <button
        aria-expanded="false"
        aria-haspopup="true"
        class="font-semibold"
        type="button"
      >
        <div
          class="mr-2.5 flex flex-row items-center rounded bg-white px-2.5 py-2 font-inter text-sm font-medium text-gray-900 transition-colors duration-600 ease-default hover:bg-gray-100 focus:transition-none"
        >
          <svg
            class="mr-2.5 fill-current"
            fill="none"
            height="14"
            viewBox="0 0 14 14"
            width="14"
          >
            <path
              d="M11 5.39998C12.3255 5.39998 13.4 4.32546 13.4 2.99998C13.4 1.67449 12.3255 0.599976 11 0.599976C9.67449 0.599976 8.59997 1.67449 8.59997 2.99998C8.59997 3.10036 8.60613 3.19931 8.6181 3.29646L4.66605 5.27249C4.2344 4.85609 3.6471 4.59998 2.99997 4.59998C1.67449 4.59998 0.599976 5.67449 0.599976 6.99998C0.599976 8.32546 1.67449 9.39997 2.99997 9.39997C3.64711 9.39997 4.23442 9.14385 4.66608 8.72743L8.61811 10.7035C8.60614 10.8006 8.59997 10.8996 8.59997 11C8.59997 12.3255 9.67449 13.4 11 13.4C12.3255 13.4 13.4 12.3255 13.4 11C13.4 9.67449 12.3255 8.59998 11 8.59998C10.3528 8.59998 9.76554 8.85609 9.33389 9.27249L5.38184 7.29646C5.39381 7.19931 5.39997 7.10036 5.39997 6.99998C5.39997 6.89957 5.39381 6.80061 5.38183 6.70345L9.33387 4.72743C9.76552 5.14385 10.3528 5.39998 11 5.39998Z"
            />
          </svg>
          Share
        </div>
      </button>
      <div
        class="pointer-events-none fixed inset-0 z-20"
      >
        <div
          aria-labelledby="menu-button"
          aria-orientation="vertical"
          class="pointer-events-auto absolute mt-2 w-11/12 rounded-md md:w-56 bg-white shadow-lg ring-1 ring-black/5 z-20 text-left transition focus:outline-none duration-75 ease-in invisible scale-95 opacity-0"
          role="menu"
          style="top: 0px; right: 1004px;"
          tabindex="-1"
        >
          <div
            class="whitespace-pre-wrap py-1"
            role="none"
          >
            <a
              class="flex items-center text-sm text-gray-700 hover:text-gray-900 gtm-hook-share-facebook appearance-none px-4 py-2 hover:bg-gray-100"
              href="https://www.facebook.com/sharer/sharer.php?u=http%3A%2F%2Flocalhost%2F"
              rel="noopener"
              role="menuitem"
              tabindex="-1"
              target="_blank"
            >
              <span
                class="fa-stack fa-fw transition-opacity hover:opacity-70 "
                data-testid="share-item-facebook"
              >
                <i>
                  {"icon":{"prefix":"fas","iconName":"circle"},"className":"fa-stack-2x text-blue-500"}
                </i>
                <i>
                  {"icon":{"prefix":"fab","iconName":"facebook"},"className":"fa-stack-1x text-white","inverse":true}
                </i>
              </span>
              <span
                class="pl-2"
              >
                Facebook
              </span>
            </a>
            <a
              class="flex items-center text-sm text-gray-700 hover:text-gray-900 gtm-hook-share-twitter appearance-none px-4 py-2 hover:bg-gray-100"
              href="https://twitter.com/share?url=http%3A%2F%2Flocalhost%2F"
              rel="noopener"
              role="menuitem"
              tabindex="-1"
              target="_blank"
            >
              <span
                class="fa-stack fa-fw transition-opacity hover:opacity-70 "
                data-testid="share-item-twitter"
              >
                <i>
                  {"icon":{"prefix":"fas","iconName":"circle"},"className":"fa-stack-2x text-black"}
                </i>
                <i>
                  {"icon":{"prefix":"fab","iconName":"x-twitter"},"className":"fa-stack-1x text-white","inverse":true}
                </i>
              </span>
              <span
                class="pl-2"
              >
                Twitter
              </span>
            </a>
            <a
              class="flex items-center text-sm text-gray-700 hover:text-gray-900 gtm-hook-share-whatsapp appearance-none px-4 py-2 hover:bg-gray-100"
              href="https://wa.me/?text=http%3A%2F%2Flocalhost%2F"
              rel="noopener"
              role="menuitem"
              tabindex="-1"
              target="_blank"
            >
              <span
                class="fa-stack fa-fw transition-opacity hover:opacity-70 "
                data-testid="share-item-whatsapp"
              >
                <i>
                  {"icon":{"prefix":"fas","iconName":"circle"},"className":"fa-stack-2x text-green-400"}
                </i>
                <i>
                  {"icon":{"prefix":"fab","iconName":"whatsapp"},"className":"fa-stack-1x text-white","inverse":true}
                </i>
              </span>
              <span
                class="pl-2"
              >
                Whatsapp
              </span>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`StickyMobileNav renders correctly in story view with anchor links 1`] = `
<div
  class="flex h-12 w-full items-center justify-between bg-white py-1.5 pl-3 shadow-md min-[390px]:pr-4"
>
  <div
    class="mr-3 pr-3 border-r border-gray-200"
  >
    <button
      aria-label="Open menu"
      class="size-10 rounded bg-white px-2 transition-colors duration-600 ease-default hover:bg-gray-100 focus:transition-none"
      data-testid="megamenu-button"
      type="button"
    >
      <svg
        class="mx-auto fill-current text-gray-900"
        height="16"
        viewBox="0 0 24 16"
        width="24"
      >
        <path
          clip-rule="evenodd"
          d="M0.644302 3.16345V0.663452H23.1443V3.16345H0.644302ZM0.644287 9.41339H23.1443V6.91339H0.644287V9.41339ZM0.644287 15.6634H23.1443V13.1634H0.644287V15.6634Z"
          fill-rule="evenodd"
        />
      </svg>
    </button>
  </div>
  <div
    class="flex grow flex-row items-center justify-between"
  >
    <div
      class="flex grow"
    >
      <div
        class="group-focus-within:opacity-0 lg:ml-4 xl:relative"
      >
        <button
          aria-label="In this article"
          class="pl-2 font-inter text-base font-bold xl:pl-4"
          type="button"
        >
          In this article
          <svg
            aria-hidden="true"
            class="svg-inline--fa fa-chevron-down ml-2 text-sm"
            data-icon="chevron-down"
            data-prefix="fas"
            focusable="false"
            role="img"
            viewBox="0 0 512 512"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M233.4 406.6c12.5 12.5 32.8 12.5 45.3 0l192-192c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L256 338.7 86.6 169.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3l192 192z"
              fill="currentColor"
            />
          </svg>
        </button>
      </div>
      <div
        class="grow"
      />
    </div>
    <div
      class="relative"
    >
      <button
        aria-expanded="false"
        aria-haspopup="true"
        class="font-semibold"
        type="button"
      >
        <div
          class="mr-2.5 flex flex-row items-center rounded bg-white px-2.5 py-2 font-inter text-sm font-medium text-gray-900 transition-colors duration-600 ease-default hover:bg-gray-100 focus:transition-none"
        >
          <svg
            class="mr-2.5 fill-current"
            fill="none"
            height="14"
            viewBox="0 0 14 14"
            width="14"
          >
            <path
              d="M11 5.39998C12.3255 5.39998 13.4 4.32546 13.4 2.99998C13.4 1.67449 12.3255 0.599976 11 0.599976C9.67449 0.599976 8.59997 1.67449 8.59997 2.99998C8.59997 3.10036 8.60613 3.19931 8.6181 3.29646L4.66605 5.27249C4.2344 4.85609 3.6471 4.59998 2.99997 4.59998C1.67449 4.59998 0.599976 5.67449 0.599976 6.99998C0.599976 8.32546 1.67449 9.39997 2.99997 9.39997C3.64711 9.39997 4.23442 9.14385 4.66608 8.72743L8.61811 10.7035C8.60614 10.8006 8.59997 10.8996 8.59997 11C8.59997 12.3255 9.67449 13.4 11 13.4C12.3255 13.4 13.4 12.3255 13.4 11C13.4 9.67449 12.3255 8.59998 11 8.59998C10.3528 8.59998 9.76554 8.85609 9.33389 9.27249L5.38184 7.29646C5.39381 7.19931 5.39997 7.10036 5.39997 6.99998C5.39997 6.89957 5.39381 6.80061 5.38183 6.70345L9.33387 4.72743C9.76552 5.14385 10.3528 5.39998 11 5.39998Z"
            />
          </svg>
          Share
        </div>
      </button>
      <div
        class="pointer-events-none fixed inset-0 z-20"
      >
        <div
          aria-labelledby="menu-button"
          aria-orientation="vertical"
          class="pointer-events-auto absolute mt-2 w-11/12 rounded-md md:w-56 bg-white shadow-lg ring-1 ring-black/5 z-20 text-left transition focus:outline-none duration-75 ease-in invisible scale-95 opacity-0"
          role="menu"
          style="top: 0px; right: 1004px;"
          tabindex="-1"
        >
          <div
            class="whitespace-pre-wrap py-1"
            role="none"
          >
            <a
              class="flex items-center text-sm text-gray-700 hover:text-gray-900 gtm-hook-share-facebook appearance-none px-4 py-2 hover:bg-gray-100"
              href="https://www.facebook.com/sharer/sharer.php?u=http%3A%2F%2Flocalhost%2F"
              rel="noopener"
              role="menuitem"
              tabindex="-1"
              target="_blank"
            >
              <span
                class="fa-stack fa-fw transition-opacity hover:opacity-70 "
                data-testid="share-item-facebook"
              >
                <i>
                  {"icon":{"prefix":"fas","iconName":"circle"},"className":"fa-stack-2x text-blue-500"}
                </i>
                <i>
                  {"icon":{"prefix":"fab","iconName":"facebook"},"className":"fa-stack-1x text-white","inverse":true}
                </i>
              </span>
              <span
                class="pl-2"
              >
                Facebook
              </span>
            </a>
            <a
              class="flex items-center text-sm text-gray-700 hover:text-gray-900 gtm-hook-share-twitter appearance-none px-4 py-2 hover:bg-gray-100"
              href="https://twitter.com/share?url=http%3A%2F%2Flocalhost%2F"
              rel="noopener"
              role="menuitem"
              tabindex="-1"
              target="_blank"
            >
              <span
                class="fa-stack fa-fw transition-opacity hover:opacity-70 "
                data-testid="share-item-twitter"
              >
                <i>
                  {"icon":{"prefix":"fas","iconName":"circle"},"className":"fa-stack-2x text-black"}
                </i>
                <i>
                  {"icon":{"prefix":"fab","iconName":"x-twitter"},"className":"fa-stack-1x text-white","inverse":true}
                </i>
              </span>
              <span
                class="pl-2"
              >
                Twitter
              </span>
            </a>
            <a
              class="flex items-center text-sm text-gray-700 hover:text-gray-900 gtm-hook-share-whatsapp appearance-none px-4 py-2 hover:bg-gray-100"
              href="https://wa.me/?text=http%3A%2F%2Flocalhost%2F"
              rel="noopener"
              role="menuitem"
              tabindex="-1"
              target="_blank"
            >
              <span
                class="fa-stack fa-fw transition-opacity hover:opacity-70 "
                data-testid="share-item-whatsapp"
              >
                <i>
                  {"icon":{"prefix":"fas","iconName":"circle"},"className":"fa-stack-2x text-green-400"}
                </i>
                <i>
                  {"icon":{"prefix":"fab","iconName":"whatsapp"},"className":"fa-stack-1x text-white","inverse":true}
                </i>
              </span>
              <span
                class="pl-2"
              >
                Whatsapp
              </span>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`StickyMobileNav renders with custom logo type 1`] = `
<div
  class="flex h-12 w-full items-center justify-between bg-white py-1.5 pl-3 shadow-md min-[390px]:pr-4"
>
  <div
    class="mr-3 pr-3"
  >
    <button
      aria-label="Open menu"
      class="size-10 rounded bg-white px-2 transition-colors duration-600 ease-default hover:bg-gray-100 focus:transition-none"
      data-testid="megamenu-button"
      type="button"
    >
      <svg
        class="mx-auto fill-current text-gray-900"
        height="16"
        viewBox="0 0 24 16"
        width="24"
      >
        <path
          clip-rule="evenodd"
          d="M0.644302 3.16345V0.663452H23.1443V3.16345H0.644302ZM0.644287 9.41339H23.1443V6.91339H0.644287V9.41339ZM0.644287 15.6634H23.1443V13.1634H0.644287V15.6634Z"
          fill-rule="evenodd"
        />
      </svg>
    </button>
  </div>
  <div
    class="flex grow"
  >
    <div
      class="group-focus-within:opacity-0 lg:ml-4 xl:relative"
    >
      <button
        aria-label="In this article"
        class="pl-2 font-inter text-base font-bold xl:pl-4"
        type="button"
      >
        In this article
        <svg
          aria-hidden="true"
          class="svg-inline--fa fa-chevron-down ml-2 text-sm"
          data-icon="chevron-down"
          data-prefix="fas"
          focusable="false"
          role="img"
          viewBox="0 0 512 512"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M233.4 406.6c12.5 12.5 32.8 12.5 45.3 0l192-192c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L256 338.7 86.6 169.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3l192 192z"
            fill="currentColor"
          />
        </svg>
      </button>
    </div>
    <div
      class="grow"
    />
  </div>
</div>
`;

exports[`StickyMobileNav renders with viafoura tray when showViafoura is true 1`] = `
<div
  class="flex h-12 w-full items-center justify-between bg-white py-1.5 pl-3 shadow-md min-[390px]:pr-4"
>
  <div
    class="mr-3 pr-3 border-r border-gray-200"
  >
    <button
      aria-label="Open menu"
      class="size-10 rounded bg-white px-2 transition-colors duration-600 ease-default hover:bg-gray-100 focus:transition-none"
      data-testid="megamenu-button"
      type="button"
    >
      <svg
        class="mx-auto fill-current text-gray-900"
        height="16"
        viewBox="0 0 24 16"
        width="24"
      >
        <path
          clip-rule="evenodd"
          d="M0.644302 3.16345V0.663452H23.1443V3.16345H0.644302ZM0.644287 9.41339H23.1443V6.91339H0.644287V9.41339ZM0.644287 15.6634H23.1443V13.1634H0.644287V15.6634Z"
          fill-rule="evenodd"
        />
      </svg>
    </button>
  </div>
  <div
    class="flex grow flex-row items-center justify-between"
  >
    <div
      class="flex grow"
    >
      <div
        class="group-focus-within:opacity-0 lg:ml-4 xl:relative"
      >
        <button
          aria-label="In this article"
          class="pl-2 font-inter text-base font-bold xl:pl-4"
          type="button"
        >
          In this article
          <svg
            aria-hidden="true"
            class="svg-inline--fa fa-chevron-down ml-2 text-sm"
            data-icon="chevron-down"
            data-prefix="fas"
            focusable="false"
            role="img"
            viewBox="0 0 512 512"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M233.4 406.6c12.5 12.5 32.8 12.5 45.3 0l192-192c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L256 338.7 86.6 169.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3l192 192z"
              fill="currentColor"
            />
          </svg>
        </button>
      </div>
      <div
        class="grow"
      />
    </div>
    <div
      class="relative"
    >
      <button
        aria-expanded="false"
        aria-haspopup="true"
        class="font-semibold"
        type="button"
      >
        <div
          class="mr-2.5 flex flex-row items-center rounded bg-white px-2.5 py-2 font-inter text-sm font-medium text-gray-900 transition-colors duration-600 ease-default hover:bg-gray-100 focus:transition-none"
        >
          <svg
            class="mr-2.5 fill-current"
            fill="none"
            height="14"
            viewBox="0 0 14 14"
            width="14"
          >
            <path
              d="M11 5.39998C12.3255 5.39998 13.4 4.32546 13.4 2.99998C13.4 1.67449 12.3255 0.599976 11 0.599976C9.67449 0.599976 8.59997 1.67449 8.59997 2.99998C8.59997 3.10036 8.60613 3.19931 8.6181 3.29646L4.66605 5.27249C4.2344 4.85609 3.6471 4.59998 2.99997 4.59998C1.67449 4.59998 0.599976 5.67449 0.599976 6.99998C0.599976 8.32546 1.67449 9.39997 2.99997 9.39997C3.64711 9.39997 4.23442 9.14385 4.66608 8.72743L8.61811 10.7035C8.60614 10.8006 8.59997 10.8996 8.59997 11C8.59997 12.3255 9.67449 13.4 11 13.4C12.3255 13.4 13.4 12.3255 13.4 11C13.4 9.67449 12.3255 8.59998 11 8.59998C10.3528 8.59998 9.76554 8.85609 9.33389 9.27249L5.38184 7.29646C5.39381 7.19931 5.39997 7.10036 5.39997 6.99998C5.39997 6.89957 5.39381 6.80061 5.38183 6.70345L9.33387 4.72743C9.76552 5.14385 10.3528 5.39998 11 5.39998Z"
            />
          </svg>
          Share
        </div>
      </button>
      <div
        class="pointer-events-none fixed inset-0 z-20"
      >
        <div
          aria-labelledby="menu-button"
          aria-orientation="vertical"
          class="pointer-events-auto absolute mt-2 w-11/12 rounded-md md:w-56 bg-white shadow-lg ring-1 ring-black/5 z-20 text-left transition focus:outline-none duration-75 ease-in invisible scale-95 opacity-0"
          role="menu"
          style="top: 0px; right: 1004px;"
          tabindex="-1"
        >
          <div
            class="whitespace-pre-wrap py-1"
            role="none"
          >
            <a
              class="flex items-center text-sm text-gray-700 hover:text-gray-900 gtm-hook-share-facebook appearance-none px-4 py-2 hover:bg-gray-100"
              href="https://www.facebook.com/sharer/sharer.php?u=http%3A%2F%2Flocalhost%2F"
              rel="noopener"
              role="menuitem"
              tabindex="-1"
              target="_blank"
            >
              <span
                class="fa-stack fa-fw transition-opacity hover:opacity-70 "
                data-testid="share-item-facebook"
              >
                <i>
                  {"icon":{"prefix":"fas","iconName":"circle"},"className":"fa-stack-2x text-blue-500"}
                </i>
                <i>
                  {"icon":{"prefix":"fab","iconName":"facebook"},"className":"fa-stack-1x text-white","inverse":true}
                </i>
              </span>
              <span
                class="pl-2"
              >
                Facebook
              </span>
            </a>
            <a
              class="flex items-center text-sm text-gray-700 hover:text-gray-900 gtm-hook-share-twitter appearance-none px-4 py-2 hover:bg-gray-100"
              href="https://twitter.com/share?url=http%3A%2F%2Flocalhost%2F"
              rel="noopener"
              role="menuitem"
              tabindex="-1"
              target="_blank"
            >
              <span
                class="fa-stack fa-fw transition-opacity hover:opacity-70 "
                data-testid="share-item-twitter"
              >
                <i>
                  {"icon":{"prefix":"fas","iconName":"circle"},"className":"fa-stack-2x text-black"}
                </i>
                <i>
                  {"icon":{"prefix":"fab","iconName":"x-twitter"},"className":"fa-stack-1x text-white","inverse":true}
                </i>
              </span>
              <span
                class="pl-2"
              >
                Twitter
              </span>
            </a>
            <a
              class="flex items-center text-sm text-gray-700 hover:text-gray-900 gtm-hook-share-whatsapp appearance-none px-4 py-2 hover:bg-gray-100"
              href="https://wa.me/?text=http%3A%2F%2Flocalhost%2F"
              rel="noopener"
              role="menuitem"
              tabindex="-1"
              target="_blank"
            >
              <span
                class="fa-stack fa-fw transition-opacity hover:opacity-70 "
                data-testid="share-item-whatsapp"
              >
                <i>
                  {"icon":{"prefix":"fas","iconName":"circle"},"className":"fa-stack-2x text-green-400"}
                </i>
                <i>
                  {"icon":{"prefix":"fab","iconName":"whatsapp"},"className":"fa-stack-1x text-white","inverse":true}
                </i>
              </span>
              <span
                class="pl-2"
              >
                Whatsapp
              </span>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`StickyMobileNav toggles anchor box when clicking the dropdown button 1`] = `
<div>
  <div
    class="flex h-12 w-full items-center justify-between bg-white py-1.5 pl-3 shadow-md min-[390px]:pr-4"
  >
    <div
      class="mr-3 pr-3 border-r border-gray-200"
    >
      <button
        aria-label="Open menu"
        class="size-10 rounded bg-white px-2 transition-colors duration-600 ease-default hover:bg-gray-100 focus:transition-none"
        data-testid="megamenu-button"
        type="button"
      >
        <svg
          class="mx-auto fill-current text-gray-900"
          height="16"
          viewBox="0 0 24 16"
          width="24"
        >
          <path
            clip-rule="evenodd"
            d="M0.644302 3.16345V0.663452H23.1443V3.16345H0.644302ZM0.644287 9.41339H23.1443V6.91339H0.644287V9.41339ZM0.644287 15.6634H23.1443V13.1634H0.644287V15.6634Z"
            fill-rule="evenodd"
          />
        </svg>
      </button>
    </div>
    <div
      class="flex grow flex-row items-center justify-between"
    >
      <div
        class="flex grow"
      >
        <div
          class="group-focus-within:opacity-0 lg:ml-4 xl:relative"
        >
          <button
            aria-label="In this article"
            class="pl-2 font-inter text-base font-bold xl:pl-4"
            type="button"
          >
            In this article
            <svg
              aria-hidden="true"
              class="svg-inline--fa fa-chevron-up ml-2 text-sm"
              data-icon="chevron-up"
              data-prefix="fas"
              focusable="false"
              role="img"
              viewBox="0 0 512 512"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M233.4 105.4c12.5-12.5 32.8-12.5 45.3 0l192 192c12.5 12.5 12.5 32.8 0 45.3s-32.8 12.5-45.3 0L256 173.3 86.6 342.6c-12.5 12.5-32.8 12.5-45.3 0s-12.5-32.8 0-45.3l192-192z"
                fill="currentColor"
              />
            </svg>
          </button>
          <div
            class="absolute left-0 z-[-1] w-full xl:left-auto xl:z-0"
          >
            <div
              class="grid grid-cols-1 divide-y divide-gray-200 bg-white px-4 shadow-md md:place-items-center xl:w-[375px] xl:place-items-start xl:rounded-lg "
            >
              <div
                class="w-full py-3 md:w-[375px] xl:w-full mt-3"
              >
                <button
                  aria-label="First Heading"
                  class="line-clamp-2 w-full text-left font-inter text-base font-medium leading-9 text-gray-800 focus:outline-none md:text-center xl:text-left"
                  type="button"
                >
                  First Heading
                </button>
              </div>
              <div
                class="w-full py-3 md:w-[375px] xl:w-full"
              >
                <button
                  aria-label="Second Heading"
                  class="line-clamp-2 w-full text-left font-inter text-base font-medium leading-9 text-gray-800 focus:outline-none md:text-center xl:text-left"
                  type="button"
                >
                  Second Heading
                </button>
              </div>
            </div>
          </div>
        </div>
        <div
          class="grow"
        />
      </div>
      <div
        class="relative"
      >
        <button
          aria-expanded="false"
          aria-haspopup="true"
          class="font-semibold"
          type="button"
        >
          <div
            class="mr-2.5 flex flex-row items-center rounded bg-white px-2.5 py-2 font-inter text-sm font-medium text-gray-900 transition-colors duration-600 ease-default hover:bg-gray-100 focus:transition-none"
          >
            <svg
              class="mr-2.5 fill-current"
              fill="none"
              height="14"
              viewBox="0 0 14 14"
              width="14"
            >
              <path
                d="M11 5.39998C12.3255 5.39998 13.4 4.32546 13.4 2.99998C13.4 1.67449 12.3255 0.599976 11 0.599976C9.67449 0.599976 8.59997 1.67449 8.59997 2.99998C8.59997 3.10036 8.60613 3.19931 8.6181 3.29646L4.66605 5.27249C4.2344 4.85609 3.6471 4.59998 2.99997 4.59998C1.67449 4.59998 0.599976 5.67449 0.599976 6.99998C0.599976 8.32546 1.67449 9.39997 2.99997 9.39997C3.64711 9.39997 4.23442 9.14385 4.66608 8.72743L8.61811 10.7035C8.60614 10.8006 8.59997 10.8996 8.59997 11C8.59997 12.3255 9.67449 13.4 11 13.4C12.3255 13.4 13.4 12.3255 13.4 11C13.4 9.67449 12.3255 8.59998 11 8.59998C10.3528 8.59998 9.76554 8.85609 9.33389 9.27249L5.38184 7.29646C5.39381 7.19931 5.39997 7.10036 5.39997 6.99998C5.39997 6.89957 5.39381 6.80061 5.38183 6.70345L9.33387 4.72743C9.76552 5.14385 10.3528 5.39998 11 5.39998Z"
              />
            </svg>
            Share
          </div>
        </button>
        <div
          class="pointer-events-none fixed inset-0 z-20"
        >
          <div
            aria-labelledby="menu-button"
            aria-orientation="vertical"
            class="pointer-events-auto absolute mt-2 w-11/12 rounded-md md:w-56 bg-white shadow-lg ring-1 ring-black/5 z-20 text-left transition focus:outline-none duration-75 ease-in invisible scale-95 opacity-0"
            role="menu"
            style="top: 0px; right: 1004px;"
            tabindex="-1"
          >
            <div
              class="whitespace-pre-wrap py-1"
              role="none"
            >
              <a
                class="flex items-center text-sm text-gray-700 hover:text-gray-900 gtm-hook-share-facebook appearance-none px-4 py-2 hover:bg-gray-100"
                href="https://www.facebook.com/sharer/sharer.php?u=http%3A%2F%2Flocalhost%2F"
                rel="noopener"
                role="menuitem"
                tabindex="-1"
                target="_blank"
              >
                <span
                  class="fa-stack fa-fw transition-opacity hover:opacity-70 "
                  data-testid="share-item-facebook"
                >
                  <i>
                    {"icon":{"prefix":"fas","iconName":"circle"},"className":"fa-stack-2x text-blue-500"}
                  </i>
                  <i>
                    {"icon":{"prefix":"fab","iconName":"facebook"},"className":"fa-stack-1x text-white","inverse":true}
                  </i>
                </span>
                <span
                  class="pl-2"
                >
                  Facebook
                </span>
              </a>
              <a
                class="flex items-center text-sm text-gray-700 hover:text-gray-900 gtm-hook-share-twitter appearance-none px-4 py-2 hover:bg-gray-100"
                href="https://twitter.com/share?url=http%3A%2F%2Flocalhost%2F"
                rel="noopener"
                role="menuitem"
                tabindex="-1"
                target="_blank"
              >
                <span
                  class="fa-stack fa-fw transition-opacity hover:opacity-70 "
                  data-testid="share-item-twitter"
                >
                  <i>
                    {"icon":{"prefix":"fas","iconName":"circle"},"className":"fa-stack-2x text-black"}
                  </i>
                  <i>
                    {"icon":{"prefix":"fab","iconName":"x-twitter"},"className":"fa-stack-1x text-white","inverse":true}
                  </i>
                </span>
                <span
                  class="pl-2"
                >
                  Twitter
                </span>
              </a>
              <a
                class="flex items-center text-sm text-gray-700 hover:text-gray-900 gtm-hook-share-whatsapp appearance-none px-4 py-2 hover:bg-gray-100"
                href="https://wa.me/?text=http%3A%2F%2Flocalhost%2F"
                rel="noopener"
                role="menuitem"
                tabindex="-1"
                target="_blank"
              >
                <span
                  class="fa-stack fa-fw transition-opacity hover:opacity-70 "
                  data-testid="share-item-whatsapp"
                >
                  <i>
                    {"icon":{"prefix":"fas","iconName":"circle"},"className":"fa-stack-2x text-green-400"}
                  </i>
                  <i>
                    {"icon":{"prefix":"fab","iconName":"whatsapp"},"className":"fa-stack-1x text-white","inverse":true}
                  </i>
                </span>
                <span
                  class="pl-2"
                >
                  Whatsapp
                </span>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;
