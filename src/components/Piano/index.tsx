'use client';

import Script from 'next/script';
import React, { useEffect, useMemo } from 'react';
import TagManager from 'react-gtm-module';
import { useDispatch } from 'react-redux';

import { debugGaaLog as debugLog } from 'components/GoogleExtendAccess/utils';
import { useAppSelector } from 'store/hooks';
import pageSlice from 'store/slices/page';
import pianoSlice from 'store/slices/piano';
import { PaymentStatus } from 'types/Payments';
import { MEMBER_TYPE_DATA, PianoSubscriberType } from 'types/Piano';
import { ChartBeatSubscriberType } from 'types/chartbeat';
import { isValidReturnUrl, linkSocial, ssoLogin, ssoLogout } from 'util/auth';
import { PREMIUM_COOKIES, PREMIUM_EXTENDED_COOKIES } from 'util/constants';
import { getCookie, removeCookie, setCookie } from 'util/cookies';
import { openDigitalPrintEdition } from 'util/dpe';
import { sendToGtm, setGtmDataLayer } from 'util/gtm';
import { useOnce } from 'util/hooks';
import {
  fetchAdPublisherProvidedId,
  fetchValidatePayments,
  fetchValidatePremiumSubscription,
} from 'util/organization/suzuka';
import { getPageHierarchy, navigateTo } from 'util/page';

import { chartBeatSubscriberEngagement } from '../Chartbeat';

import {
  fetchAndSetUserProfile,
  initialize as initializeGoogleExtendedExcess,
} from './googleExtendedAccess';
import { gtmRemoveFromCart, onLogoutSuccess, setupPipeline } from './pipeline';
import {
  addPianoReadyAction,
  getPianoReady,
  onPianoReady,
  pushPianoAction,
} from './ready';
import showCompleteProfileReminder from './showCompleteProfileReminder';

import type { MouseEvent } from 'react';
import type { PianoABTestingVariant } from 'store/slices/piano';
import type {
  CheckoutCloseEvent,
  CheckoutCustomEvent,
  LoginSuccessEvent,
  MemberTypeData,
  OriginalPublicationResponse,
  PianoApiConversion,
  PianoHandler,
  PianoHandlerCallback,
  PianoHandlerKey,
  PianoIdParameters,
  PianoLoginCallbackData,
  PianoLoginUserData,
  PianoTinypassInterface,
} from 'types/Piano';

const PREMIUM_SUBSCRIPTION_KEY = 'premiumSubscription';
const PUBLISHER_PROVIDED_ID_KEY = 'publisher_provided_id';

type ExpectedCheckoutCustomEvents =
  | {
      eventName: 'logout' | 'go_subscribe' | 'go_home' | 'open_digital_print';
    }
  | {
      eventName: 'meterActive' | 'meterExpired';
    }
  | {
      eventName: 'go_url_path';
      params?: string | { path?: string };
    }
  | {
      eventName: 'gtm_event' | 'gtm_event_payment';
      params?: string | { id?: string };
    }
  | {
      eventName: 'open_template';
      params?:
        | string
        | {
            offer?: string;
            template?: string;
          };
    };

const REGISTERED_HOOK_ID = 'registered-hook';

function addCheckoutCustomEventHandler() {
  pushPianoAction([
    'addHandler',
    'checkoutCustomEvent',
    (rawEvent: CheckoutCustomEvent) => {
      const event = rawEvent as ExpectedCheckoutCustomEvents;
      switch (event.eventName) {
        case 'logout':
          onLogoutSuccess();
          break;
        case 'go_subscribe':
          navigateTo('/subscribe/');
          break;
        case 'go_home':
          navigateTo('/');
          break;
        case 'go_url_path':
          if (
            event.params &&
            typeof event.params !== 'string' &&
            event.params.path
          ) {
            navigateTo(event.params.path);
          }
          break;
        case 'gtm_event': {
          const gtmId =
            typeof event.params === 'string'
              ? event.params
              : event.params && (event.params.id as string);
          if (gtmId) {
            sendToGtm({ label: gtmId });
          }
          break;
        }
        case 'gtm_event_payment': {
          const gtmId =
            typeof event.params === 'string'
              ? event.params
              : event.params && event.params.id;
          let label;
          switch (gtmId) {
            case 'credit':
              label = 'creditCard';
              break;

            case 'paypalbt':
              label = 'payPal';
              break;

            case 'applepaybt':
              label = 'apple_pay';
              break;

            default:
              console.error('Unexpected label for gtm_event_payment');
          }

          if (label) {
            sendToGtm({ label });
          }
          break;
        }
        case 'open_template':
          if (
            typeof event.params === 'object' &&
            event.params?.offer &&
            event.params?.template
          ) {
            const offerId = event.params.offer;
            const templateId = event.params.template;
            onPianoReady((tp) =>
              tp.offer.show({
                displayMode: 'modal',
                offerId,
                templateId,
              }),
            );
          }
          break;
        case 'open_digital_print':
          openDigitalPrintEdition();
          break;

        default:
          console.error(`Unknown event: ${event.eventName}`);
      }
    },
  ]);
}

function removePremiumSubscription() {
  window.localStorage.removeItem(PREMIUM_SUBSCRIPTION_KEY);
  const store = window.getStore();
  store.dispatch(pianoSlice.actions.setPremiumSubscription(false));
  store.dispatch(pianoSlice.actions.setPremiumExtended(false));
  removeCookie(PREMIUM_COOKIES);
  removeCookie(PREMIUM_EXTENDED_COOKIES);
}

async function validatePremiumSubscription(hasAccess: boolean) {
  const store = window.getStore();
  const state = store.getState();
  const pianoFeature = state.features.piano;

  if (
    !pianoFeature.enabled ||
    !hasAccess ||
    !pianoFeature.data.supportPremiumSubscription
  ) {
    removePremiumSubscription();
    return;
  }

  if (getCookie(PREMIUM_COOKIES)) return;

  const premiumToken =
    window.localStorage.getItem(PREMIUM_SUBSCRIPTION_KEY) || undefined;

  const token = (await getPianoReady()).pianoId.getToken();

  try {
    if (!token) {
      removePremiumSubscription();
      return;
    }

    const response = await fetchValidatePremiumSubscription({
      premiumToken,
      token,
    });
    if (response.data.token) {
      store.dispatch(pianoSlice.actions.setPremiumSubscription(true));
      store.dispatch(
        pianoSlice.actions.setPremiumExtended(response.data.extended),
      );
      window.localStorage.setItem(
        PREMIUM_SUBSCRIPTION_KEY,
        response.data.token,
      );
      setCookie({
        cookieName: PREMIUM_COOKIES,
        cookiePath: '/',
        cookieValue: 'true',
        expireSeconds: 30 * 24 * 60 * 60,
      });
      if (response.data.extended) {
        setCookie({
          cookieName: PREMIUM_EXTENDED_COOKIES,
          cookiePath: '/',
          cookieValue: 'true',
          expireSeconds: 30 * 24 * 60 * 60,
        });
      } else {
        removeCookie(PREMIUM_EXTENDED_COOKIES);
      }
    } else {
      removePremiumSubscription();
    }
  } catch {
    removePremiumSubscription();
  }
}

function fetchConversions(): void {
  onPianoReady((tp) => {
    if (tp.pianoId.getUser()) {
      tp.api.callApi<PianoApiConversion>(
        '/conversion/list',
        {
          cross_app: true,
        },
        (response): void => {
          const conversions = response.code === 0 ? response.conversions : [];
          const store = window.getStore();
          store.dispatch(pianoSlice.actions.setConversions(conversions));
        },
      );
    }
  });
}

function checkPromoCode(): void {
  const promoMatch = /promo_code=([A-Za-z\d\-_]+)/.exec(
    window.location.search,
  );

  if (promoMatch) {
    const code = promoMatch[1];
    pushPianoAction(['setCustomVariable', 'promo_code', code]);
  }
}

// In milliseconds
const ResetTokenConfig = {
  BONZAI_REQUEST_DURATION: 1000,
  KEY: 'CheckResetToken',
  REQUEST_DURATION: 3000,
  YES: 'yes',
};

function checkResetToken(initialized = false, forcedCheck = false): void {
  const tokenMatch = /reset_token=([A-Za-z\d]+)/.exec(window.location.search);

  if (tokenMatch) {
    const token = tokenMatch[1];

    if (initialized) {
      window.localStorage.setItem(ResetTokenConfig.KEY, ResetTokenConfig.YES);
      setTimeout(() => {
        checkResetToken();
      }, ResetTokenConfig.REQUEST_DURATION);
      return;
    }

    const isCheckResetTokenActive = window.localStorage.getItem(
      ResetTokenConfig.KEY,
    );

    if (isCheckResetTokenActive === ResetTokenConfig.YES || forcedCheck) {
      window.localStorage.removeItem(ResetTokenConfig.KEY);
      onPianoReady((tp) => {
        tp.pianoId.show({
          loggedIn() {
            window.location.assign('/');
          },
          resetPasswordToken: token,
        });
      });
    }
  }
}

function setGenericVariables() {
  const store = window.getStore();
  const state = store.getState();
  pushPianoAction([
    'setCustomVariable',
    'site_logo_url',
    state.conf.logoSvgOnly,
  ]);
  pushPianoAction([
    'setCustomVariable',
    'site_logo_alt_text',
    state.conf.name,
  ]);
  pushPianoAction(['setCustomVariable', 'page_name', state.page.name]);
  pushPianoAction([
    'setCustomVariable',
    'publication',
    state.conf.publication,
  ]);
  pushPianoAction([
    'setCustomVariable',
    'facebook_url',
    state.conf.facebookUrl,
  ]);
  pushPianoAction([
    'setCustomVariable',
    'twitter_url',
    `https://twitter.com/${state.conf.twitterUsername}`,
  ]);
  pushPianoAction([
    'setCustomVariable',
    'has_app',
    state.features.mobileApp.enabled,
  ]);
  if (state.features.piano.enabled) {
    // show or hide social media screen in the onboarding workflow
    pushPianoAction([
      'setCustomVariable',
      'has_social_screen',
      state.features.piano.data.hasSocialScreen,
    ]);
  }
  pushPianoAction(['setCustomVariable', 'has_dpe', state.conf.hasDpe]);
  pushPianoAction(['setCustomVariable', 'has_gift', state.conf.hasGift]);
  pushPianoAction([
    'setCustomVariable',
    'has_group_subscriptions',
    state.conf.hasGroupSubscriptions,
  ]);
  pushPianoAction(['setCustomVariable', 'has_puzzles', state.conf.hasPuzzles]);
  if (state.features.dpe.enabled && state.features.dpe.data.dpeLatestDate) {
    pushPianoAction([
      'setCustomVariable',
      'dpe_latest_date',
      state.features.dpe.data.dpeLatestDate,
    ]);
  }
  if (state.features.mobileApp.enabled) {
    pushPianoAction([
      'setCustomVariable',
      'app_store_id',
      state.features.mobileApp.data.appStoreId,
    ]);
    pushPianoAction([
      'setCustomVariable',
      'google_play_id',
      state.features.mobileApp.data.googlePlayId,
    ]);
  }
  pushPianoAction([
    'setCustomVariable',
    'registration_only',
    state.features.piano.enabled && state.features.piano.data.registrationOnly,
  ]);
  pushPianoAction(['setCustomVariable', 'support_auth_server', true]);
  const { abTest } = state.settings;
  pushPianoAction([
    'setCustomVariable',
    'ab_test',
    abTest ? `${abTest.id}_${abTest.variant}` : '',
  ]);
  const mailFeature = state.features.mail;
  pushPianoAction([
    'setCustomVariable',
    'support_newsletter_landing_page',
    mailFeature.enabled
      ? mailFeature.data.supportNewslettersLandingPage
      : false,
  ]);
}

interface PreviousUrl {
  time: number;
  url: string;
}

function setReturnUrl(preventReturnUrl: boolean): void {
  const { pathname } = window.location;
  if (
    (/^\/?(subscribe|gift|upgrade)/i.test(pathname) || preventReturnUrl) &&
    window.localStorage !== undefined
  ) {
    try {
      const previousUrl = window.localStorage.getItem('previousUrl');
      const previousUrlObj = previousUrl
        ? (JSON.parse(previousUrl) as PreviousUrl)
        : null;
      if (
        previousUrlObj &&
        // eslint-disable-next-line rulesdir/prefer-use-date
        previousUrlObj.time > Date.now() - 2 * 60 * 1000 &&
        previousUrlObj.url !== null
      ) {
        pushPianoAction([
          'setCustomVariable',
          'return_url',
          previousUrlObj.url,
        ]);
      } else {
        // Default to the home page if the user has not visited any
        // non-subscribe pages yet
        pushPianoAction([
          'setCustomVariable',
          'return_url',
          window.location.origin,
        ]);
      }
    } catch (e) {
      console.error(e);
    }
  } else {
    pushPianoAction(['setCustomVariable', 'return_url', window.location.href]);
  }

  if (
    window.localStorage !== undefined &&
    !/^\/?(subscribe|gift|upgrade)/i.test(pathname) &&
    !preventReturnUrl
  ) {
    localStorage.setItem(
      'previousUrl',
      JSON.stringify({
        // eslint-disable-next-line rulesdir/prefer-use-date
        time: Date.now(),
        url: window.location.href,
      } as PreviousUrl),
    );
  }
}

function setStory(): void {
  const store = window.getStore();
  const state = store.getState();
  const { story } = state;
  const { primaryPage, secondaryPage } = getPageHierarchy(
    state.conf.topDownAdCatTargeting,
    state.pages,
  );

  // state.story is always truthy, so check id instead to validate story exists
  if (story.id) {
    pushPianoAction(['setZone', 'story']);
    pushPianoAction(['setContentCreated', story.publishFrom]);
    pushPianoAction(['setTags', story.tags]);

    if (story.byline) {
      pushPianoAction(['setContentAuthor', story.byline]);
    }

    if (primaryPage) {
      pushPianoAction(['setContentSection', primaryPage.name]);
    }

    if (secondaryPage) {
      pushPianoAction([
        'setCustomVariable',
        'secondary_keyword',
        secondaryPage.name,
      ]);
    }

    if (primaryPage?.name) {
      pushPianoAction([
        'setCustomVariable',
        'canonicalPrimaryPageName',
        primaryPage.name,
      ]);
    }

    if (secondaryPage?.name) {
      pushPianoAction([
        'setCustomVariable',
        'canonicalSecondaryPageName',
        secondaryPage.name,
      ]);
    }
  }
}

function setEmailDomain(user: PianoLoginUserData) {
  const emailDomainIdx = user.email.indexOf('@');
  if (emailDomainIdx !== -1) {
    const emailDomain = user.email.substring(emailDomainIdx + 1);
    pushPianoAction(['setCustomVariable', 'domain', emailDomain]);
  }
}

function checkAbandonedCart() {
  if (!window.sessionStorage.getItem('abandoned_cart.set_this_session')) {
    const id = window.localStorage.getItem('abandoned_cart.id');
    const timestamp = window.localStorage.getItem('abandoned_cart.timestamp');
    // eslint-disable-next-line rulesdir/prefer-use-date
    const timesince = Date.now() - Number(timestamp || 0);

    if (
      id &&
      timestamp &&
      timesince > 1000 * 60 * 5 &&
      timesince < 1000 * 60 * 60 * 24 * 14
    ) {
      pushPianoAction(['setCustomVariable', 'abandoned_cart_show', true]);
      pushPianoAction(['setCustomVariable', 'abandoned_cart_term_id', id]);
    }
  }
}

function checkCorporateAccess(): void {
  const store = window.getStore();
  const state = store.getState();
  const pianoFeature = state.features.piano;
  if (!pianoFeature.enabled) {
    return;
  }
  store.dispatch(
    pianoSlice.actions.setHasAccess({
      hasAccess: true,
      hasCorporateAccess: true,
    }),
  );
  store.dispatch(pianoSlice.actions.setInitialized());
}

function validatePayments(): void {
  onPianoReady((tp) => {
    const token = tp.pianoId.getToken();

    if (!token) {
      return;
    }

    const store = window.getStore();
    fetchValidatePayments({ token }).then(
      (response) => {
        if (response.success) {
          const status: PaymentStatus =
            typeof response.data === 'object'
              ? response.data.name
              : response.data;
          const id: string | undefined =
            typeof response.data === 'object' ? response.data.id : undefined;
          store.dispatch(
            pianoSlice.actions.setPaymentStatus({
              id,
              status,
            }),
          );
        }
      },
      () => {
        store.dispatch(
          pianoSlice.actions.setPaymentStatus({
            status: PaymentStatus.UNKNOWN,
          }),
        );
      },
    );
  });
}

function afterPaywallRemoved() {
  const store = window.getStore();
  store.dispatch(pianoSlice.actions.setPaywall(false));
}

function afterPaywallKept() {
  const store = window.getStore();
  store.dispatch(pianoSlice.actions.setPaywall(true));
}

// Note: this function can be removed when all the new paywall roll out
function enableFeaturesBehindPaywall() {
  const store = window.getStore();
  store.dispatch(pianoSlice.actions.setEnableFeaturesBehindPaywall(true));
}

export function pianoLoginByToken(token: string): void {
  onPianoReady((tp) => {
    debugLog(`pianoLoginByToken ${token}`);
    pushPianoAction(['setCustomVariable', 'loginByToken', true]);
    tp.pianoId.loginByToken(token).catch(console.error);
  });
}

function setAdPublisherProvidedId(id: string): void {
  const store = window.getStore();
  const { googleTagManager } = store.getState().features;
  if (googleTagManager.enabled) {
    window.googletag = window.googletag || { cmd: [] };
    googletag.cmd.push(() => {
      googletag.pubads().setPublisherProvidedId(id);
    });
    setGtmDataLayer({
      ppid: id,
    });
  }
}

function checkIfUserIsChanged(ppid: string, id: string): boolean {
  return (
    (ppid.startsWith('anonymous') && id !== 'anonymous') ||
    (!ppid.startsWith('anonymous') && id === 'anonymous')
  );
}

function fetchAndSetAdPublisherProvidedId(id: string) {
  fetchAdPublisherProvidedId(id)
    .then((res) => {
      let { ppid } = res;
      if (id === 'anonymous') {
        ppid = `anonymous${ppid}`;
      }
      setAdPublisherProvidedId(ppid);
      setCookie({
        cookieName: PUBLISHER_PROVIDED_ID_KEY,
        cookiePath: '/',
        cookieValue: ppid,
        expireSeconds: 7 * 24 * 60 * 60,
      });
    })
    .catch(console.error);
}

export function fetchAndSetAdPublisherProvidedIdProc(id: string): void {
  const ppid = getCookie(PUBLISHER_PROVIDED_ID_KEY);
  if (ppid) {
    if (checkIfUserIsChanged(ppid, id)) {
      removeCookie(PUBLISHER_PROVIDED_ID_KEY);
      fetchAndSetAdPublisherProvidedId(id);
    } else {
      setAdPublisherProvidedId(ppid);
    }
  } else {
    fetchAndSetAdPublisherProvidedId(id);
  }
}

export function getAdPublisherProvidedId(): string | null {
  return getCookie(PUBLISHER_PROVIDED_ID_KEY);
}

function removeGtmMemberTypeData() {
  window.localStorage.removeItem(MEMBER_TYPE_DATA);
}

function setupGTM(
  memberID: string,
  memberType: PianoSubscriberType,
  memberRole?: string,
  memberOrigin?: string,
): void {
  TagManager.dataLayer({
    dataLayer: {
      subs: {
        memberID,
        memberOrigin: memberOrigin || '',
        memberRole: memberRole || '',
        memberType,
        meterCount: '',
        meterPeriodEnd: '',
        meterPeriodStart: '',
      },
    },
  });
}

function setGtmMemberTypeData(hasAccess = false): void {
  onPianoReady((tp) => {
    const store = window.getStore();
    const state = store.getState();
    const { memberOrigin, memberRole } = state.piano;
    const gtmFeature = state.features.googleTagManager;
    const user = tp.pianoId.getUser();

    if (!gtmFeature.enabled || !user) {
      removeGtmMemberTypeData();
      return;
    }

    let memberType = user?.uid
      ? PianoSubscriberType.MEMBER
      : PianoSubscriberType.VISITOR;
    const memberID = user?.uid || '';

    if (memberRole) {
      memberType = PianoSubscriberType.ENTERPRISE;
    } else if (hasAccess) {
      memberType = PianoSubscriberType.SUBSCRIBER;
    }

    const memberTypeData = window.localStorage.getItem(MEMBER_TYPE_DATA);

    let memberTypeObj: MemberTypeData | null = null;

    try {
      memberTypeObj = memberTypeData
        ? (JSON.parse(memberTypeData) as MemberTypeData)
        : null;
    } catch (e) {
      console.error(e);
    }

    if (
      !memberTypeData ||
      (hasAccess && memberTypeObj?.memberType !== memberType)
    ) {
      window.localStorage.setItem(
        MEMBER_TYPE_DATA,
        JSON.stringify({
          memberID,
          memberOrigin,
          memberRole,
          memberType,
        } as MemberTypeData),
      );
      setupGTM(memberID, memberType, memberRole, memberOrigin);
    }

    setGtmDataLayer({
      city: undefined, // TODO - The city a user is from
      region: undefined, // TODO - The region a user is from
      weather: undefined, // TODO - Approximate of user's current weather
    });
  });
}

function handleUserUpdate(user: PianoLoginUserData | null): void {
  const store = window.getStore();
  store.dispatch(pianoSlice.actions.login(user));
  fetchConversions();
  if (user) {
    setEmailDomain(user);
    fetchAndSetAdPublisherProvidedIdProc(user.uid);
    setGtmMemberTypeData();
  } else {
    removePremiumSubscription();
    fetchAndSetAdPublisherProvidedIdProc('anonymous');
    removeGtmMemberTypeData();
  }
  if (store.getState().story.isGoogleExtendedAccessArticle) {
    fetchAndSetUserProfile(user);
  }
}

function isFromFbSubscriptions() {
  // Detect whether the user has come from Facebook Instant Articles
  const searchParams = new URLSearchParams(window.location.search);
  const redirectUri = searchParams.get('redirect_uri');
  return (
    redirectUri &&
    redirectUri.startsWith(
      'https://www.facebook.com/subscriptions/account-linking/confirm/',
    )
  );
}

function onCheckoutClose(event: CheckoutCloseEvent) {
  // If we get here without piano having initialized
  // We have larger issues than accessing properties on undefined
  const tp = window.tp as PianoTinypassInterface;

  switch (event.state) {
    case 'checkoutCompleted':
      // User completed the purchase and now has access
      // Usually it's a good practice to reload the page
      // Do not reload if coming from Facebook as this is handled by Piano
      if (!isFromFbSubscriptions()) window.location.reload();
      break;
    case 'alreadyHasAccess':
      // User already has access
      // This state could be a result of user logging in during checkout process
      // Usually it's a good practice to reload the page as well
      // NOTE: This only triggers if user logged in and had a subscription.
      // Do not reload if coming from Facebook as this is handled by Piano
      if (!isFromFbSubscriptions()) window.location.reload();
      break;
    case 'close':
      if (window.tracking?.cart) {
        gtmRemoveFromCart();
      }
      // User did not complete the purchase and simply closed the modal
      if (tp.hasVisitedLogin && tp.pianoId.isUserValid()) {
        // TODO: Work-around to update page render in case
        // user logged in during modal but not yet subscribed.
        // Do not reload if coming from Facebook as this is handled by Piano
        if (!isFromFbSubscriptions()) window.location.reload();
      }

      if (tp.modalDetails) {
        const { modalParams } = tp.modalDetails;
        switch (modalParams.state) {
          case 'show':
            if (modalParams.eventLabel && modalParams.eventName) {
              sendToGtm({
                label: modalParams.eventLabel,
                trigger: modalParams.eventName,
              });
            }
            break;
          case 'registered':
            window.location.reload();
            break;
          default:
            // No default behaviour
            break;
        }
      }

      break;

    default:
      // No default behaviour
      break;
  }
}

function onLoginRequired() {
  // TODO
}

function onLoginSuccess(data: LoginSuccessEvent) {
  const store = window.getStore();
  const state = store.getState();
  const { piano } = state.features;
  const { isMagicLink } = state.piano;
  // Reload if logging in via login/register screen
  if (data.source === 'PIANOID' && !isMagicLink) {
    // Purpose: For Social login, Set Original Publication
    // On Registration, the flag is set by Piano
    // TODO: Identify Social login only and the company
    if (data.registration) {
      const formData = new FormData();
      formData.append('uid', data.params.uid);
      fetch('/subscriptions/set/original/publication/', {
        body: formData,
        method: 'POST',
      })
        .then((response) => response.json())
        .then((res: OriginalPublicationResponse) => {
          if (!res.success) {
            console.error(res.message);
          } else if (piano.enabled && piano.data.registrationOnly) {
            window.location.reload();
          }
        })
        .catch(console.error);
    }
    // Only if we're logging in (not registering),
    // or we're registering on a non-rego site
    else if (
      !data.registration ||
      !(piano.enabled && piano.data.registrationOnly)
    ) {
      window.location.reload();
    }
  }

  window.atsenvelopemodule?.setAdditionalData({
    id: data.params.email,
    type: 'email',
  });
  debugLog(`JS Callback @piano file : ${JSON.stringify(data.params)}`);
}

function onLogout() {
  ssoLogout().catch(console.error);
}

function onRegistrationSuccess() {
  const store = window.getStore();
  const state = store.getState();
  const { piano } = state.features;
  if (piano.enabled && piano.data.registrationOnly) {
    // Triggers interaction in piano experience to start onboarding process
    document.getElementById(REGISTERED_HOOK_ID)?.click();
  }

  setGtmDataLayer({
    event: 'nav_user_newaccount_created_event',
    method: 'Email',
  });
}

interface FbHandlerProps<T extends PianoHandlerKey> {
  // Checks Piano event data for condition to send Facebook event data.
  condition?: (...data: Parameters<PianoHandlerCallback<T>>) => boolean;
  fbEvent: string;
  pianoEvent: T;
  // Transforms Piano event data into Facebook event data.

  transform?: (
    ...data: Parameters<PianoHandlerCallback<T>>
  ) => Record<string, unknown>;
}

function addFbHandler<T extends PianoHandlerKey>(props: FbHandlerProps<T>) {
  pushPianoAction([
    'addHandler',
    props.pianoEvent,
    (...data: Parameters<PianoHandlerCallback<T>>) => {
      if (props.condition && !props.condition(...data)) {
        // TODO: needed when rest fixed.
        // return;
      }

      // TODO
      // window.fbq &&
      //   fbq(
      //     fbStandardEvents.includes(props.fbEvent) ? 'track' : 'trackCustom',
      //     props.fbEvent,
      //     props.transform?(props),
      //   );
    },
  ] as ['addHandler', ...PianoHandler]);
}

interface InitializeOptions {
  idParams?: PianoIdParameters;
  preventReturnUrl?: boolean;
}

function initialize({
  idParams = {},
  preventReturnUrl = false,
}: InitializeOptions | undefined = {}): void {
  const store = window.getStore();
  const state = store.getState();
  const { pianoApiUrl, pianoCdnUrl } = state.settings;
  const isSandbox = (pianoCdnUrl ?? '').toLowerCase().includes('sandbox');
  const pianoFeature = state.features.piano;
  const { isGoogleExtendedAccessArticle } = state.story;
  const googleTagManagerFeature = state.features.googleTagManager;

  if (!pianoFeature.enabled) return;

  const { aid } = pianoFeature.data;

  const hasMagicLinkParam = new URLSearchParams(window.location.search).has(
    'passwordless_token',
  );

  if (hasMagicLinkParam) {
    window.getStore().dispatch(pianoSlice.actions.setIsMagicLink(true));
  }

  window.checkCorporateAccess = checkCorporateAccess;
  window.validatePayments = validatePayments;
  window.showCompleteProfileReminder = showCompleteProfileReminder;
  window.afterPaywallRemoved = afterPaywallRemoved;
  window.afterPaywallKept = afterPaywallKept;
  window.enableFeaturesBehindPaywall = enableFeaturesBehindPaywall;
  window.setPianoABTestingVariant = (variant: string) => {
    store.dispatch(
      pianoSlice.actions.setABTestingVariant(variant as PianoABTestingVariant),
    );
  };
  window.setHasAccess = (hasAccess) => {
    store.dispatch(
      pianoSlice.actions.setHasAccess({
        hasAccess,
      }),
    );

    // Set GTM member type as Subscriber
    setGtmMemberTypeData(hasAccess);

    // Validate if user has premium access
    validatePremiumSubscription(hasAccess)
      .finally(() => {
        store.dispatch(pianoSlice.actions.setInitialized());
        store.dispatch(pianoSlice.actions.setHasValidatedPremium(true));
      })
      .catch(() => {});
  };
  window.setHasDPEAccess = (hasDPEAccess) => {
    store.dispatch(pianoSlice.actions.setHasDPEAccess(hasDPEAccess));
  };
  window.setHasSubscription = (hasSubscription) => {
    store.dispatch(pianoSlice.actions.setHasSubscription(hasSubscription));
  };
  window.setHasPuzzlesAccess = (hasPuzzlesAccess) => {
    store.dispatch(pianoSlice.actions.setHasPuzzlesAccess(hasPuzzlesAccess));
  };
  window.setMemberRole = (memberRole) => {
    store.dispatch(
      pianoSlice.actions.setMemberRole({
        memberRole,
      }),
    );
  };
  window.setMemberOrigin = (memberOrigin) => {
    store.dispatch(
      pianoSlice.actions.setMemberOrigin({
        memberOrigin,
      }),
    );
  };

  pushPianoAction(['setAid', aid]);
  pushPianoAction(['setEndpoint', pianoApiUrl]);
  pushPianoAction(['setSandbox', isSandbox]);
  pushPianoAction(['setDebug', isSandbox]);
  pushPianoAction(['setUseTinypassAccounts', false]);
  pushPianoAction(['setUsePianoIdUserProvider', true]);
  pushPianoAction(['setFbPixelId', '****************']);
  pushPianoAction([
    'setPianoIdUrl',
    isSandbox ? 'https://sandbox.tinypass.com' : 'https://id-au.piano.io/',
  ]);

  if (pianoFeature.data.siteId) {
    pushPianoAction(['setCxenseSiteId', pianoFeature.data.siteId]);
  }

  pushPianoAction(['addHandler', 'checkoutClose', onCheckoutClose]);
  pushPianoAction(['addHandler', 'loginRequired', onLoginRequired]);
  pushPianoAction([
    'addHandler',
    'registrationSuccess',
    () => onRegistrationSuccess(),
  ]);
  pushPianoAction([
    'addHandler',
    'loginSuccess',
    (data: LoginSuccessEvent) => onLoginSuccess(data),
  ]);
  pushPianoAction(['addHandler', 'logout', () => onLogout()]);
  if (isGoogleExtendedAccessArticle) {
    initializeGoogleExtendedExcess();
  }

  pushPianoAction([
    'setPageURL',
    [
      window.location.protocol,
      '//',
      window.location.host,
      window.location.pathname,
    ].join(''),
  ]);

  setupPipeline();
  addCheckoutCustomEventHandler();

  if (!isFromFbSubscriptions()) {
    addFbHandler({
      fbEvent: 'InitiateCheckout',
      pianoEvent: 'startCheckout',
    });

    addFbHandler<'checkoutComplete'>({
      fbEvent: 'Purchase',
      pianoEvent: 'checkoutComplete',
      transform: (data) => ({
        currency: data.chargeCurrency,
        offer_code: data.termId,
        value: data.chargeAmount,
      }),
    });

    addFbHandler<'submitPayment'>({
      condition: (data) => data.term.isSubscription,
      fbEvent: 'Subscribe',
      pianoEvent: 'submitPayment',
      transform: (data) => ({
        currency: data.term.chargeCurrency,
        value: data.term.chargeAmount,
      }),
    });

    addFbHandler<'submitPayment'>({
      condition: (data) =>
        data.term.isSubscription && data.term.isFreeTrial === 'true',
      fbEvent: 'StartTrial',
      pianoEvent: 'submitPayment',
      transform: (data) => ({
        currency: data.term.chargeCurrency,
        value: data.term.chargeAmount,
      }),
    });
  }

  onPianoReady((tp) => {
    window.tracking = window.tracking ?? {};
    // Do not reuse the `tp` variable from above as Piano may not have been
    // loaded when the var was initialised

    if (tp.isInitialized) {
      return;
    }

    const options: PianoIdParameters = {
      iframeUrl: isSandbox
        ? 'https://sandbox.tinypass.com'
        : 'https://id-au.piano.io',
      loggedIn: (data: PianoLoginCallbackData): void => {
        const { isMagicLink } = window.getStore().getState().piano;
        debugLog(`loggedIn ${JSON.stringify(data)}`);
        if (
          state.features.piano.enabled &&
          data.source === 'PIANOID' &&
          !isMagicLink
        ) {
          debugLog('loggedIn - PIANOID');
          window.location.reload();
          return;
        }
        // Piano doesn't provide source for login by token
        // Setting custom variable to reload on login by token
        if (
          state.features.piano.enabled &&
          tp.customVariables !== undefined &&
          'loginByToken' in tp.customVariables &&
          // eslint-disable-next-line @typescript-eslint/dot-notation
          tp.customVariables['loginByToken']
        ) {
          debugLog('loggedIn - ByToken');
          window.location.reload();
          return;
        }

        debugLog('After loggedIn');
        handleUserUpdate(data.user);
        setGtmDataLayer({
          event: 'nav_user_login_success_event',
          method: 'Email',
        });

        const linkingState = window.sessionStorage.getItem(
          `linking_state_${data.user.email}`,
        );
        const returnUrl = window.sessionStorage.getItem('magicLinkReturnUrl');
        const shouldUseReturnUrl =
          isMagicLink && returnUrl && isValidReturnUrl(returnUrl);

        function redirectIfShouldUseReturnUrl() {
          if (shouldUseReturnUrl) {
            window.sessionStorage.removeItem('magicLinkReturnUrl');
            window.location.href = returnUrl;
          }
        }

        if (linkingState) {
          window.sessionStorage.removeItem(`linking_state_${data.user.email}`);
          linkSocial({
            email: data.user.email,
            linkingState,
          })
            .catch((e) => {
              console.error(e);
            })
            .finally(() => {
              redirectIfShouldUseReturnUrl();
            });
        } else {
          redirectIfShouldUseReturnUrl();
        }
      },
      loggedOut: (): void => {
        handleUserUpdate(null);
        removeCookie(PREMIUM_COOKIES);
        removeCookie(PREMIUM_EXTENDED_COOKIES);
        store.dispatch(pianoSlice.actions.logout());
      },
      loginDisplayed: (): void => {
        store.dispatch(pageSlice.actions.setShowHelp());
      },
      screen: 'register',
      ...idParams,
    };

    tp.pianoId.init(options);

    tp.experience.init();

    tp.push(['setCustomVariable', 'beta', true]);

    const userValid = tp.pianoId.isUserValid();
    const user = tp.pianoId.getUser();

    if (window.location.hash === '#forgotten-password') {
      tp.pianoId.show({ screen: 'restore' });
    }

    const searchParams = new URLSearchParams(window.location.search);
    if (searchParams.get('shared_account_code')) {
      tp.pianoId.show({
        registrationSuccess: () => {
          window.location.href = '/my-account/#library';
        },
        screen: 'register',
      });
    }

    setGenericVariables();
    setStory();
    checkAbandonedCart();
    checkPromoCode();
    checkResetToken(true);
    setReturnUrl(preventReturnUrl);

    if (userValid) {
      handleUserUpdate(user);
    } else {
      handleUserUpdate(null);
      store.dispatch(pianoSlice.actions.setInitialized());
    }

    if (
      googleTagManagerFeature.enabled &&
      googleTagManagerFeature.data?.measurementId
    ) {
      tp.setGA4Config({
        eventParameters: {
          page_location: window.location.href,
          page_title: document.title,
          send_page_view: false,
          user_id: user?.uid,
        },
        measurementId: googleTagManagerFeature.data.measurementId,
      });
    }
  });

  addPianoReadyAction();
}

export function pianoLogin(arg: MouseEvent): void;
export function pianoLogin(arg: boolean): void;
export function pianoLogin(): void;

export function pianoLogin(arg?: boolean | MouseEvent): void {
  let disableSignUp = false;
  if (typeof arg === 'boolean') {
    disableSignUp = arg;
  }
  onPianoReady((tp) => {
    tp.pianoId.show({
      disableSignUp,
      screen: 'login',
    });
  });
}

export function authServerLogin() {
  window.location.href = `/login?return=${encodeURIComponent(
    window.location.href,
  )}`;
}

export function pianoLogout(): void {
  onPianoReady((tp) => {
    tp.pianoId.logout().catch(console.error);
  });
  window.location.reload();
}

export function pianoRegister(): void {
  onPianoReady((tp) => {
    tp.pianoId.show({
      screen: 'register',
    });
  });
}

export function authServerRegister(url?: string) {
  window.location.href = `/register?return=${encodeURIComponent(
    url ?? window.location.href,
  )}`;
}

interface PianoHookProps {
  className?: string;
  id?: string;
}

export const PianoHook = React.memo(
  ({ className, id }: PianoHookProps) => <div className={className} id={id} />,
  () => true,
);

PianoHook.displayName = 'PianoHook';

interface PianoProps {
  options?: InitializeOptions;
}

export default function Piano({
  options,
}: PianoProps): React.ReactElement | null {
  const piano = useAppSelector((state) => state.piano);
  const { pianoCdnUrl } = useAppSelector((state) => state.settings);
  const pianoFeature = useAppSelector((state) => state.features.piano);
  const hasBonzaiAd = useAppSelector((state) => state.page.hasBonzaiAd);
  const enablePianoABTesting =
    pianoFeature.enabled && pianoFeature.data.isAbTesting;
  const pianoABTestingVariant = useAppSelector(
    (state) => state.piano.pianoABTestingVariant,
  );
  const dispatch = useDispatch();

  const pianoOptions = useMemo(() => {
    const pianoSsoDisableOptions: InitializeOptions = {
      idParams: { ...options?.idParams, confirmation: 'none' },
      ...options,
    };
    return pianoSsoDisableOptions;
  }, [options]);

  useOnce(() => {
    initialize(pianoOptions);
    return true;
  }, [pianoOptions]);

  useEffect(() => {
    if (!piano.initialized || !enablePianoABTesting) return;

    TagManager.dataLayer({
      dataLayer: {
        abTest: pianoABTestingVariant,
      },
    });
  }, [piano.initialized, pianoABTestingVariant, enablePianoABTesting]);

  useEffect(() => {
    if (!pianoFeature.enabled || !piano.initialized) return;

    let chartBeatSubscriberType = ChartBeatSubscriberType.Visitor;
    if (piano.hasSubscription) {
      chartBeatSubscriberType = ChartBeatSubscriberType.Subscriber;
    } else if (piano.user?.uid) {
      chartBeatSubscriberType = ChartBeatSubscriberType.Member;
    }
    chartBeatSubscriberEngagement(chartBeatSubscriberType);
  }, [
    pianoFeature.enabled,
    piano.hasSubscription,
    piano.initialized,
    piano.user,
  ]);

  // force checkResetToken request after the bonzai request
  useEffect(() => {
    if (pianoFeature.enabled && hasBonzaiAd) {
      onPianoReady(() => {
        window.localStorage.removeItem(ResetTokenConfig.KEY);
        setTimeout(() => {
          checkResetToken(false, true);
        }, ResetTokenConfig.BONZAI_REQUEST_DURATION);
      });
    }
  }, [pianoFeature.enabled, hasBonzaiAd]);

  useEffect(() => {
    if (!pianoFeature.enabled || !piano.initialized) return;

    if (!piano.user) {
      ssoLogin()
        .then(async (res) => {
          if (res.success && res.data.accessToken) {
            const { accessToken } = res.data;
            const tp = await getPianoReady();
            try {
              await tp.pianoId.loginByToken(accessToken);
              const url = new URL(window.location.href);
              url.searchParams.set('msg', 'sso');
              if (res.data.siteName) {
                url.searchParams.set('origin', res.data.siteName);
              }
              window.location.href = url.href;
            } catch (e) {
              console.error(e);
            }
          }
        })
        .catch(console.error);
    }
  }, [pianoFeature.enabled, piano.initialized, piano.user]);

  useEffect(() => {
    window.setSubscribeButtonStyle = (style) => {
      dispatch(pianoSlice.actions.setSubscribeButtonStyle(style));
    };
    window.setSubscribeButtonText = (text) => {
      dispatch(pianoSlice.actions.setSubscribeButtonText(text));
    };
    window.setSubscribeButtonHref = (href) => {
      dispatch(pianoSlice.actions.setSubscribeButtonHref(href));
    };
    window.setSubscribeButtonOverride = (override) => {
      dispatch(pianoSlice.actions.setSubscribeButtonOverride(override));
    };
  }, [dispatch]);

  if (!pianoFeature.enabled) {
    return null;
  }

  return (
    <>
      <div className="hidden" id={REGISTERED_HOOK_ID} />
      <Script
        defer
        id="piano-tinypass"
        src={`${pianoCdnUrl}/api/tinypass.min.js`}
      />
    </>
  );
}
