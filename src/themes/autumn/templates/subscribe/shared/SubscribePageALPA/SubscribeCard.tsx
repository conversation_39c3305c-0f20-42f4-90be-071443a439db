import { useCallback } from 'react';

import Button from 'themes/autumn/components/generic/Button';
import Skeleton from 'themes/autumn/components/generic/Skeleton';
import { SimplePeriodUnit, type SimpleTerm } from 'types/Piano';
import { redirectToRegister } from 'util/auth';
import { setGa4GtmCommerce } from 'util/gtm';

interface SubscribeCardProps {
  fullPriceTerm?: SimpleTerm;
  offerId: string;
  term?: SimpleTerm;
}

export default function SubscribeCard({
  fullPriceTerm,
  offerId,
  term,
}: SubscribeCardProps) {
  const seatsTotal = (term?.sharedAccountCount ?? 0) + 1;
  const fullPriceYearly =
    fullPriceTerm?.finalPlan.format(({ price }) => Number(price), {
      convertPeriodToUnit: SimplePeriodUnit.YEARS,
    }) ?? 0;
  const priceYearly =
    term?.finalPlan.format(({ price }) => Number(price), {
      convertPeriodToUnit: SimplePeriodUnit.YEARS,
    }) ?? 0;
  const savings = 1 - priceYearly / seatsTotal / fullPriceYearly;

  const onClickSubscribe = useCallback(() => {
    if (!term) {
      return;
    }

    setGa4GtmCommerce({
      event: 'begin_checkout',
      items: [
        {
          category: 'Subscription',
          id: term.termId,
          name: term.name,
          price: term.firstPlan.price,
        },
      ],
      value: term.firstPlan.price,
    });
    const searchQueryArgs = new URLSearchParams({
      offerId,
      source: 'alpa',
      termId: term.termId,
      ...(term.sharedAccountCount > 0 && {
        return: '/manage-seats/?checkout=true',
      }),
    });
    redirectToRegister(`/payment/?${searchQueryArgs.toString()}`);
  }, [offerId, term]);

  const priceFn = (price: string, period: string) => (
    <div>
      <span className="text-xl font-bold">${price}</span>{' '}
      <span className="font-semibold">/ {period}</span>
    </div>
  );

  return (
    <Skeleton
      className="w-full max-w-344 md:max-w-full"
      showContent={term !== undefined}
    >
      <div className="flex flex-col gap-y-4 rounded border border-gray-300 px-5 pb-7 pt-6 font-inter text-base text-gray-900 shadow-lg">
        <div className="flex flex-row gap-x-4.5">
          <div className="flex size-[52px] items-center justify-center bg-green-100 font-playfair text-3xl font-semibold leading-none">
            {seatsTotal}
          </div>
          <div className="flex flex-col gap-y-1">
            <div className="text-[22px] font-bold leading-none">
              {seatsTotal === 1 ? (
                <div>1 user</div>
              ) : (
                <div>Up to {seatsTotal} users</div>
              )}
            </div>
            {term // eslint-disable-next-line max-len
              ? term.finalPlan.format(({ period, price }) =>
                  priceFn(price, period),
                )
              : priceFn('', '')}
          </div>
        </div>
        <div>
          <div className="font-semibold text-green-600">
            Save more than {Math.floor(Math.floor(savings * 100) / 5) * 5}%
          </div>
          <div className="font-medium">
            {term
              ? term.finalPlan.format(
                  ({ periodFrequency, price }) => {
                    const pricePerUser = (Number(price) / seatsTotal).toFixed(
                      2,
                    );
                    return `$${pricePerUser} / user ${periodFrequency}`;
                  },
                  {
                    convertPeriodToSingleFactor: true,
                    convertPeriodToUnit: SimplePeriodUnit.WEEKS,
                  },
                )
              : '$ / user'}
          </div>
        </div>
        <Button
          bgColor="bg-red-600 disabled:bg-gray-300"
          desktopFullWidth
          fontSize="text-base"
          height="h-10.5 md:h-10.5 lg:h-10.5"
          hoverColor="hover:bg-red-700 disabled:hover:bg-gray-300"
          mobileFullWidth
          onClick={onClickSubscribe}
          text="Subscribe Now"
          textColor="text-white"
          type="button"
        />
      </div>
    </Skeleton>
  );
}
