'use client';

import clsx from 'clsx';
import { useSearchParams } from 'next/navigation';
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';

import { useAppSelector } from 'store/hooks';
import Link from 'themes/autumn/components/generic/Link';
import UGCNewsletterWidget from 'themes/autumn/components/ugc/UGCNewsletterWidget';
import { sendGtmInteractionEvent } from 'themes/autumn/templates/Classifieds/utils';
import { useLoadingMoreUgcItems } from 'util/hooks';
import {
  ImageResizeMode,
  TransformOutputFormat,
  storyImageUrl,
} from 'util/image';
import {
  fetchCategories,
  formattedDateParts,
  formattedDateRange,
  isOneDayEvent,
} from 'util/ugc';

import {
  ClearFilter,
  DATE_RANGE_OPTIONS,
  DISTANCE_OPTIONS,
  FilterDropdown,
  LocationFilter,
  RadioOption,
} from './Filtering';

import type { UgcListZoneItem } from 'types/ZoneItems';
import type { FormattedDate, UGC, UGCCategory } from 'types/ugc';

const MAX_UGC = 20;
const LOAD_UGC_COUNT = 10;

interface CalendarItemProps {
  category: string;
  endDate?: FormattedDate;
  id: number;
  image?: string;
  location: string;
  onClick?: () => void;
  startDate: FormattedDate;
  title: string;
  url: string;
}

export interface StrapUgcProps {
  index: number;
  ugcItem: UGC;
}

interface VerticalCalendarViewProps {
  limit?: number;
  offset: number;
  ugc: StrapUgcProps[];
}

const CalendarItem: React.FC<CalendarItemProps> = ({
  category,
  endDate,
  id,
  image,
  location,
  onClick,
  startDate,
  title,
  url,
}) => {
  const handleLinkClick = useCallback(() => {
    onClick?.();
    sendGtmInteractionEvent(
      {
        action: 'click',
        label: title,
        section: "what's on",
        signPost: [category],
        ugc_id: id,
      },
      'ugc_interaction',
    );
  }, [category, id, onClick, title]);

  return (
    <Link
      className="relative mb-4 flex border-b border-gray-300 text-gray-900 hover:opacity-75 focus:opacity-75"
      href={url}
      noStyle
      onClick={handleLinkClick}
    >
      <div className="mr-6 flex w-1/5 items-start justify-center">
        <div className="flex flex-col items-center">
          <div className="text-sm">{startDate.weekday}</div>
          <div className="text-2xl font-semibold @md:text-4xl">
            {startDate.day}
          </div>
          <div className="text-sm">{startDate.monthShort}</div>
        </div>
      </div>
      <div className="mb-4 mr-7 flex w-full min-w-0 flex-col items-start justify-start gap-y-2">
        <div className="inline-block rounded-3xl bg-gray-100 px-2 py-1 text-xs font-medium text-gray-800">
          {category}
        </div>
        <h3 className="text-sm font-semibold leading-6 @md:text-lg">
          {title}
        </h3>
        <div className="w-full truncate text-xs @md:text-sm">
          <svg
            className="mr-2 inline"
            fill="none"
            height="16"
            viewBox="0 0 16 16"
            width="16"
          >
            <path
              d="m8.5006 0.6665c3.1296 0 5.6666 2.537 5.6666 5.6667 0 1.9904-0.9185 4.0031-2.4541 5.967-0.545 0.6971-1.1377 1.347-1.745 1.9389-0.05002 0.0488-0.09922 0.0963-0.14752 0.1425l-0.27863 0.2613-0.25435 0.2292-0.22717 0.1962-0.13874 0.115c-0.24346 0.1984-0.59238 0.2-0.83762 0.0038l-0.14506-0.1201-0.22614-0.1953-0.25388-0.2289-0.27851-0.2612-0.14752-0.1425c-0.60723-0.5919-1.2-1.2418-1.745-1.9389-1.5356-1.9639-2.4541-3.9766-2.4541-5.967 0-3.1296 2.537-5.6667 5.6667-5.6667zm0 1.3333c-2.3932 0-4.3333 1.9401-4.3333 4.3333 0 1.6374 0.80186 3.3945 2.1712 5.1457 0.50556 0.6466 1.0586 1.253 1.6253 1.8055l0.26848 0.2568c0.08846 0.083 0.17704 0.1661 0.26836 0.246l0.26833-0.2459 0.26851-0.2569c0.56673-0.5525 1.1198-1.1589 1.6253-1.8055 1.3693-1.7512 2.1712-3.5083 2.1712-5.1457 0-2.3932-1.9401-4.3333-4.3334-4.3333zm0 1.6667c1.4728 0 2.6666 1.1939 2.6666 2.6667s-1.1939 2.6667-2.6666 2.6667-2.6667-1.1939-2.6667-2.6667 1.1939-2.6667 2.6667-2.6667zm0 1.3333c-0.73638 0-1.3333 0.59695-1.3333 1.3333s0.59695 1.3333 1.3333 1.3333 1.3333-0.59695 1.3333-1.3333-0.59695-1.3333-1.3333-1.3333z"
              fill="#111827"
            />
          </svg>
          {location}
        </div>
        <div className="flex items-center text-xs text-gray-600 @md:text-sm">
          <svg
            className="mr-2 text-gray-600"
            fill="none"
            height="16"
            viewBox="-1.5 1 16 16"
            width="16"
          >
            <path
              d="m4.3333 4.1667v-2.6667m5.3333 2.6667v-2.6667m-6 5.3333h6.6666m-8 6.6667h9.3334c0.7363 0 1.3333-0.597 1.3333-1.3333v-8c0-0.73638-0.597-1.3333-1.3333-1.3333h-9.3334c-0.73638 0-1.3333 0.59696-1.3333 1.3333v8c0 0.7363 0.59695 1.3333 1.3333 1.3333z"
              stroke="#4b5563"
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="1.3333"
            />
          </svg>
          {formattedDateRange(startDate, endDate)}
        </div>
      </div>
      <div className="mb-4 flex shrink-0 flex-col items-center justify-center">
        <img
          alt={title}
          className="size-20 rounded-xl object-cover @md:size-40"
          src={image}
        />
      </div>
    </Link>
  );
};

function VerticalCalendarView({
  limit,
  offset,
  ugc,
}: VerticalCalendarViewProps): React.ReactElement | null {
  const transformUrl = useAppSelector((state) => state.settings.transformUrl);
  const supportNewsletterSubscribeFlow = useAppSelector(
    (state) =>
      state.features.mail.enabled &&
      state.features.mail.data.supportNewslettersLandingPage &&
      state.features.mail.data.supportNewslettersSubscribeFlow,
  );

  if (ugc.length < offset || offset < 1) {
    return null;
  }

  const displayItems = ugc.slice(offset - 1, limit);
  const shouldShowNewsletter = (index: number) => {
    if (!supportNewsletterSubscribeFlow) {
      return false;
    }
    const itemCount = index + 1;
    return (
      itemCount === 4 ||
      (displayItems.length < 4 && itemCount === displayItems.length)
    );
  };

  return (
    <div className="@container">
      {displayItems.map((item, index) => {
        const { ugcItem } = item;
        const shouldUseNextOccurrence =
          !!ugcItem.recurrenceText && !!ugcItem.nextOccurrence;

        if (ugcItem.images?.length === 0) {
          return null;
        }

        return (
          <React.Fragment key={ugcItem.id}>
            {shouldShowNewsletter(index) && <UGCNewsletterWidget />}
            {ugcItem.startDatetime && (
              <CalendarItem
                category={ugcItem.categoryName}
                endDate={
                  isOneDayEvent(ugcItem.startDatetime, ugcItem.endDatetime)
                    ? undefined
                    : formattedDateParts(ugcItem.endDatetime)
                }
                id={ugcItem.id}
                image={storyImageUrl({
                  fit: ImageResizeMode.MAX,
                  height: 400,
                  image: { uri: ugcItem.images[0] },
                  outputFormat: TransformOutputFormat.WEBP,
                  transformUrl,
                  width: 400,
                })}
                location={ugcItem.location}
                startDate={formattedDateParts(
                  shouldUseNextOccurrence
                    ? ugcItem.nextOccurrence
                    : ugcItem.startDatetime,
                )}
                title={ugcItem.title}
                url={ugcItem.canonicalUrl}
              />
            )}
          </React.Fragment>
        );
      })}
    </div>
  );
}

function WhatsOn({
  zoneItemData: { limit, offset, pinnedUgcOnly, ugc, ugcListId },
}: UgcListZoneItem): React.ReactElement | null {
  const index0Offset = offset - 1;
  const cappedLimit = Math.min(limit ?? ugc.length, MAX_UGC);
  const limitWithOffset = cappedLimit + index0Offset;

  const params = useSearchParams();
  const mastheadLocation = useAppSelector((state) => state.conf.location);
  const initialDateRange = params?.get('date_range')?.toLowerCase() ?? '';
  const initialCategory = params?.get('category')?.toLowerCase() ?? '';
  const initialLocation = params?.get('location')?.toLowerCase() ?? '';
  const initialDistance = params?.get('distance_km') ?? '';

  const [categories, setCategories] = useState<UGCCategory[]>([]);
  const [locationInput, setLocationInput] = useState(initialLocation);
  const isSearching = !!(
    initialDateRange ||
    initialCategory ||
    initialLocation ||
    initialDistance
  );
  const updateUrlParam = (paramName: string, value: string | null) => {
    const url = new URL(window.location.href);
    if (value === null) {
      url.searchParams.delete(paramName);
    } else {
      url.searchParams.set(paramName, value.toLowerCase());
    }
    window.location.href = url.toString();
  };

  const handleFilterSelect = (paramName: string, value: string) => {
    updateUrlParam(paramName, value);
  };

  const handleLocationSubmit = () => {
    if (!locationInput) return;
    updateUrlParam('location', locationInput);
  };

  const handleLocationInputChange = (
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const { value } = event.target;
    if (value.trim() === '') {
      setLocationInput('');
    } else {
      setLocationInput(value);
    }
  };

  const clearFilter = (filterName: string) => {
    updateUrlParam(filterName, null);
  };

  useEffect(() => {
    fetchCategories('event')
      .then((cats) => {
        if (cats) {
          setCategories(cats);
        }
      })
      .catch(console.error);
  }, []);

  // Find the category ID that matches the category name from URL params
  const categoryId = useMemo(() => {
    if (!initialCategory || categories.length === 0) return '';
    const matchedCategory = categories.find(
      (cat) => cat.name.toLowerCase() === initialCategory,
    );

    return matchedCategory ? matchedCategory.id.toString() : '';
  }, [initialCategory, categories]);

  const { loadMore, loadedUgcItems, loadedUgcLimit, loading, noMoreUgcItems } =
    useLoadingMoreUgcItems({
      category: categoryId,
      dateRange: initialDateRange,
      distance: initialDistance,
      limitWithOffset,
      loadUgcCount: limit || LOAD_UGC_COUNT,
      location: initialLocation,
      pinnedUgcOnly,
      ugcItems: ugc,
      ugcListId,
    });

  const initialLoadRef = useRef(false);
  useEffect(() => {
    if (
      categories.length > 0 &&
      (initialDateRange ||
        initialCategory ||
        initialLocation ||
        initialDistance) &&
      !initialLoadRef.current
    ) {
      initialLoadRef.current = true;
      loadMore(true);
    }
  }, [
    categories,
    initialDateRange,
    initialDistance,
    initialCategory,
    initialLocation,
    categoryId,
    loadMore,
  ]);

  const hideLoadMore = loadedUgcItems.length < cappedLimit - index0Offset;

  const strapUgc: StrapUgcProps[] = loadedUgcItems
    .slice(0, loadedUgcLimit)
    .map((ugcItem, ugcIndex) => ({
      index: ugcIndex + 1,
      ugcItem,
    }));

  return (
    <>
      <div className="mt-2 flex items-center">
        Filter by
        <FilterDropdown label="Date">
          {DATE_RANGE_OPTIONS.map((item) => (
            <RadioOption
              isSelected={initialDateRange === item.toLowerCase()}
              key={item}
              label={item}
              onClick={() => handleFilterSelect('date_range', item)}
            />
          ))}
          {!!initialDateRange && (
            <ClearFilter onClear={() => clearFilter('date_range')} />
          )}
        </FilterDropdown>
        <FilterDropdown label="Event Type">
          {categories.map((category) => (
            <RadioOption
              hideButton
              isSelected={initialCategory === category.name.toLowerCase()}
              key={category.id}
              label={category.name}
              onClick={() => handleFilterSelect('category', category.name)}
            />
          ))}
          {!!initialCategory && (
            <ClearFilter onClear={() => clearFilter('category')} />
          )}
        </FilterDropdown>
        <FilterDropdown label="Distance">
          <div className="ml-3 text-left text-sm">
            Distance from {mastheadLocation}
          </div>
          {DISTANCE_OPTIONS.map((item) => (
            <RadioOption
              isSelected={initialDistance === item.value}
              key={item.value}
              label={item.label}
              onClick={() => handleFilterSelect('distance_km', item.value)}
            />
          ))}
          {!!initialDistance && (
            <ClearFilter onClear={() => clearFilter('distance_km')} />
          )}
        </FilterDropdown>
        <LocationFilter
          clearLocation={() => clearFilter('location')}
          handleLocationInputChange={handleLocationInputChange}
          handleLocationSubmit={handleLocationSubmit}
          initialLocation={initialLocation}
          locationInput={locationInput}
        />
      </div>

      {isSearching && (
        <a className="my-2 inline-block underline" href=".">
          Clear search filters
        </a>
      )}

      {strapUgc?.length > 0 && (
        <div className="mt-5.5">
          <div data-testid="ugc-container">
            <div className="w-full">
              <VerticalCalendarView
                limit={loadedUgcLimit}
                offset={offset}
                ugc={strapUgc}
              />
            </div>
          </div>
          <div className="mb-5 mt-6 w-full">
            {noMoreUgcItems ? (
              <div
                className="m-auto text-center font-inter text-base text-gray-500"
                data-testid="no-more"
              >
                <div className="font-medium">
                  You have reached the end.
                  <br />
                  Fancy some more?{' '}
                  <a className="text-indigo-700 underline" href="/">
                    Visit our home page
                  </a>
                </div>
              </div>
            ) : (
              !hideLoadMore && (
                <button
                  className={clsx(
                    'm-auto mt-10 flex select-none items-center justify-center',
                    'rounded-md border border-gray-900 px-4 py-2 text-sm',
                    'font-inter text-gray-900 hover:bg-gray-50 focus:outline-none',
                    {
                      'pointer-events-none opacity-50': loading,
                    },
                  )}
                  data-testid="load-button"
                  onClick={() => loadMore()}
                  type="button"
                >
                  Load More
                </button>
              )
            )}
          </div>
        </div>
      )}

      {strapUgc.length === 0 && isSearching && (
        <div className="mt-8 font-semibold">
          Sorry, we didn&rsquo;t find any results.
        </div>
      )}
    </>
  );
}

export default WhatsOn;
