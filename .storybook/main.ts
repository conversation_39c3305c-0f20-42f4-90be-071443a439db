import type { StorybookConfig } from '@storybook/nextjs';
import type { RuleSetRule } from 'webpack';

const config: StorybookConfig = {
  addons: ['@storybook/addon-links', '@storybook/addon-essentials'],
  core: {
    disableTelemetry: true,
  },
  framework: {
    name: '@storybook/nextjs',
    options: {
      builder: {
        lazyCompilation: true,
      },
      strictMode: true,
    },
  },
  stories: ['../src/**/*.stories.@(js|jsx|cjs|mjs|ts|tsx)'],
  webpackFinal(webpackConfig) {
    const fileLoaderRule = webpackConfig.module?.rules?.find(
      (rule) =>
        !!rule &&
        typeof rule === 'object' &&
        rule.test instanceof RegExp &&
        rule.test.test('.svg'),
    ) as RuleSetRule;

    if (!fileLoaderRule) {
      return webpackConfig;
    }

    /* eslint-disable no-param-reassign */
    webpackConfig.module ??= {};
    webpackConfig.module.rules ??= [];
    webpackConfig.module.rules.push({
      issuer: fileLoaderRule.issuer,
      test: /\.svg$/i,
      use: '@svgr/webpack',
    });
    /* eslint-enable no-param-reassign */
    fileLoaderRule.exclude = /\.svg$/i;

    return webpackConfig;
  },
};

export default config;
