'use client';

import clsx from 'clsx';
import { useEffect, useRef, useState } from 'react';
import ReactDOM from 'react-dom';

import { useAppSelector } from 'store/hooks';
import { RenderMode } from 'store/slices/conf';
// eslint-disable-next-line import/no-named-as-default
import AdPlaceholder from 'themes/autumn/components/ads/AdPlaceholder';
import { AdSlot, DFPManager } from 'themes/autumn/components/ads/GAM/dfp';
import { initialAdSlotsState } from 'types/ad';
import {
  GUTTER_AD_CONTAINER_ID,
  getClassNamesForSizes,
  useAdTargeting,
} from 'util/ads';
import { setGtmDataLayer } from 'util/gtm';
import { useDeviceTypeFromWidth, useLazyLoadComponentState } from 'util/hooks';
import { useScrollX } from 'util/scrollx';

import type {
  SlotRenderEndedEvent,
  TargetingArguments,
} from 'themes/autumn/components/ads/GAM/dfp';
import type { AdSizeType } from 'util/ads';

export interface Props {
  autoRefresh?: boolean;
  className?: string;
  forwardedRef?: React.Ref<HTMLDivElement>;
  isBonzaiTruSkin?: boolean;
  isBrandedContent?: boolean;
  isScrollXAdSlot?: boolean;
  labelPosition?: string;
  // Tailwind spacing classes - e.g. space-y-2
  labelSpace?: string;
  lgSizes?: AdSizeType;
  mdSizes?: AdSizeType;
  moveWhenGutterAdPresent?: boolean;
  onRender?: (event: SlotRenderEndedEvent, firstAdRendering: boolean) => void;
  // Tailwind padding classes - e.g. py-2
  placeholderPadding?: string;
  position: number;
  renderOutOfThePage?: boolean;
  sizes: AdSizeType;
  slotId: string;
  // `true` or `top-n` classname for custom top offset
  sticky?: boolean | string;
  stickyOnTablet?: boolean;
  supportPortal?: boolean;
  targetingArguments?: Record<string, string>;
  withLabel?: boolean;
  withPlaceholder?: boolean;
  withPlaceholderBackground?: boolean;
  xlSizes?: AdSizeType;
}

// Taken from Titan
export function hashCode(str: string) {
  let hash = 0;

  if (str.length === 0) {
    return hash;
  }

  for (let i = 0; i < str.length; i++) {
    const charCode = str.charCodeAt(i);
    /* eslint-disable no-bitwise */
    hash = (hash << 5) - hash + charCode;
    hash &= hash;
    /* eslint-enable no-bitwise */
  }

  return hash;
}

const ALLOWED_ADS_IN_PREMIUM_SUBSCRIPTION = ['polar'];

export default function Ad({
  autoRefresh = true,
  className,
  forwardedRef,
  isBonzaiTruSkin,
  isBrandedContent,
  isScrollXAdSlot = false,
  labelPosition = 'top',
  labelSpace = 'space-y-2',
  lgSizes,
  mdSizes,
  moveWhenGutterAdPresent,
  onRender,
  placeholderPadding = 'py-2',
  position,
  renderOutOfThePage,
  sizes,
  slotId,
  sticky,
  stickyOnTablet: stickOnTablet,
  supportPortal,
  targetingArguments,
  withLabel = true,
  withPlaceholder = true,
  withPlaceholderBackground = true,
  xlSizes,
}: Props): React.ReactElement | null {
  const mode = useAppSelector((state) => state.conf.mode);
  const isZoneView = useAppSelector((state) => state.editmode.isZoneView);
  const adServing = useAppSelector((state) => state.features.adServing);
  const viewType = useAppSelector((state) => state.settings.viewType);
  const hasGutterAd = useAppSelector((state) => state.page.hasGutterAd);
  const { topDownAdCatTargeting } = useAppSelector((state) => state.conf);
  const { cat, cat1, cat2, cat3, cat4 } = useAdTargeting();
  const { showComponent, showComponentPlaceholder } =
    useLazyLoadComponentState();
  const deviceType = useDeviceTypeFromWidth();
  const [sizeRendered, setSizeRendered] = useState<googletag.SingleSizeArray>([
    0, 0,
  ]);
  const [sizeRenderedPlaceholder, setSizeRenderedPlaceholder] =
    useState<googletag.SingleSizeArray>([0, 0]);
  const [firstAdRendering, setFirstAdRendering] = useState(true);
  const ref = useRef<HTMLDivElement>(null);
  const isScrollX = useScrollX(ref, sizes);

  useEffect(() => {
    if (!firstAdRendering) {
      DFPManager.refresh(slotId);
    }
    // Only refresh Ad on deviceType change
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [deviceType]);

  // Refresh the ads every n seconds
  const autoRefreshInterval = adServing.enabled
    ? adServing.data.autoRefreshInterval
    : 0;
  useEffect(() => {
    if (
      !adServing.enabled ||
      !autoRefreshInterval ||
      !autoRefresh ||
      isBrandedContent ||
      isBonzaiTruSkin ||
      firstAdRendering
    ) {
      return;
    }

    const id = setInterval(() => {
      DFPManager.refresh(slotId);
    }, autoRefreshInterval * 1000);

    // eslint-disable-next-line consistent-return
    return () => {
      clearInterval(id);
    };
  }, [
    adServing.enabled,
    autoRefresh,
    autoRefreshInterval,
    firstAdRendering,
    isBonzaiTruSkin,
    slotId,
    isBrandedContent,
  ]);

  const hideAds =
    !showComponent && !ALLOWED_ADS_IN_PREMIUM_SUBSCRIPTION.includes(slotId);

  if (
    !adServing.enabled ||
    (hideAds && !showComponentPlaceholder) ||
    (mode === RenderMode.EDIT && isZoneView)
  ) {
    return null;
  }

  const { ad: adClassNames, sizeMapping } = getClassNamesForSizes(
    sizes,
    mdSizes,
    lgSizes,
    xlSizes,
    isScrollX,
  );

  // validate ads position to avoid the same position with diff slot Id
  function getValidAdPosition(
    adSizeArray: string[],
    slotIdPositionList: Record<string, Array<number>>,
  ): number {
    // array to keep the valid positions from diff ads size for one ad slot
    const adPositionsArray: Array<number> = [-1];

    adSizeArray.forEach((adSize) => {
      // eslint-disable-next-line no-prototype-builtins
      if (slotIdPositionList.hasOwnProperty(adSize)) {
        const existingPositions = slotIdPositionList[
          adSize
        ] as unknown as Set<number>;
        if (existingPositions.has(position)) {
          adPositionsArray.push(
            Math.max(...Array.from(existingPositions)) + 1,
          );
        }
      }
    });

    return Math.max(...adPositionsArray);
  }

  // to keep the ads size and position mapping in the memory
  function recordAdSlots(adSizeArray: string[], adPosition: number): void {
    window.adSlots.slotIdAndPositions.push({
      name: slotId,
      position: adPosition,
    });

    adSizeArray.forEach((sizeProp) => {
      // eslint-disable-next-line no-prototype-builtins
      if (window.adSlots.sizeAndPosition.hasOwnProperty(sizeProp)) {
        window.adSlots.sizeAndPosition[sizeProp].add(adPosition);
      } else {
        window.adSlots.sizeAndPosition[sizeProp] = new Set<number>([
          adPosition,
        ]);
      }
    });
  }

  function getAdsPosition(sizeString: string): number {
    let adPosition = position;
    const adSizeArray = sizeString.split('_');

    if (!window.adSlots) {
      window.adSlots = window.adSlots || initialAdSlotsState;
    }

    const existingSlotIdRecord = window.adSlots.slotIdAndPositions.find(
      (slotElement) => slotElement.name === slotId,
    );

    if (existingSlotIdRecord) {
      return existingSlotIdRecord.position;
    }

    const matchedSlotIdPositionList: Record<
      string,
      Array<number>
    > = Object.keys(window.adSlots.sizeAndPosition)
      .filter((key) => adSizeArray.includes(key))
      .reduce(
        (obj, key) => ({
          ...obj,
          [key]: window.adSlots.sizeAndPosition[key],
        }),
        {},
      );

    if (Object.keys(matchedSlotIdPositionList).length > 0) {
      const validPosition = getValidAdPosition(
        adSizeArray,
        matchedSlotIdPositionList,
      );
      if (validPosition !== -1) {
        adPosition = validPosition;
        console.error(
          `Duplicated position for ${slotId}. ` +
            `Position changed to ${adPosition}`,
        );
      }
    }

    recordAdSlots(adSizeArray, adPosition);
    return adPosition;
  }

  const sizeProps = sizeMapping
    .reduce(
      (size, mapping) => [...size, ...mapping.sizes],
      [] as googletag.SingleSizeArray[],
    )
    .filter((size) => size.length);

  const sizeString = sizeProps.map((size) => size.join('x')).join('_');
  const sysEnv = deviceType;

  const adsPosition =
    // eslint-disable-next-line rulesdir/no-typeof-window-outside-useeffect
    typeof window !== 'undefined' ? getAdsPosition(sizeString) : position;

  const cats = [
    cat,
    cat1,
    cat2,
    ...(topDownAdCatTargeting ? [cat3, cat4] : []),
  ];

  const uniqueId = [
    adServing.data.doubleClickSite,
    viewType,
    ...cats,
    adsPosition,
    sizeString,
    sysEnv,
  ].join('|');

  const allTargetingArguments: TargetingArguments = {
    ...targetingArguments,
    condAd: 'true', // TODO needed?
    pos: adsPosition.toString(),
    ur: uniqueId,
    urh: hashCode(uniqueId).toString(),
  };

  // Allow test ads via `adcallkw` query string parameter
  // eslint-disable-next-line rulesdir/no-typeof-window-outside-useeffect
  if (typeof window !== 'undefined') {
    const urlSearchParams = new URLSearchParams(window.location.search);
    const adcallkw = urlSearchParams.get('adcallkw');

    if (adcallkw) {
      allTargetingArguments.adcallkw = adcallkw;
    }
  }

  const adUnitParts = [adServing.data.doubleClickSite];

  cats.filter(Boolean).forEach((catPart) => adUnitParts.push(catPart));

  const ad = (
    <AdSlot
      adUnit={adUnitParts.join('/')}
      onSlotIsViewable={() => {
        setGtmDataLayer({
          event: 'ad_impression',
          id: slotId,
          number_ad_slots: Object.keys(DFPManager.registeredSlots).length,
        });
      }}
      onSlotRender={(e) => {
        onRender?.(e, firstAdRendering);
        if (firstAdRendering) {
          setFirstAdRendering(false);
        }
        if (e.event.size) {
          const renderedAdSize = e.event.size as googletag.SingleSizeArray;

          // Handle ads that incorrectly report their size, especially APS ads
          if (renderedAdSize[0] === 1 && renderedAdSize[1] === 1) {
            return;
          }

          setSizeRendered(renderedAdSize);

          const adsRef = e.adElementRef.current as HTMLElement;
          const placeholder =
            adsRef?.parentElement?.parentElement?.parentElement;
          if (placeholder) {
            const placeholderHeight = placeholder.clientHeight;
            if (placeholderHeight !== renderedAdSize[1]) {
              setSizeRenderedPlaceholder(
                e.event.size as googletag.SingleSizeArray,
              );
            }
          }
        }
      }}
      renderOutOfThePage={renderOutOfThePage}
      sizeMapping={sizeMapping}
      sizes={sizeProps}
      slotId={slotId}
      targetingArguments={allTargetingArguments}
    />
  );

  if (renderOutOfThePage) {
    return ad;
  }

  if (hasGutterAd && moveWhenGutterAdPresent && !hideAds) {
    // Note this will only ever execute on the client due to `hasGutterAd`
    const el = document.getElementById(GUTTER_AD_CONTAINER_ID);

    if (el) {
      return ReactDOM.createPortal(
        // Use a simple placeholder with gutter ads
        <div className={clsx(adClassNames)}>{ad}</div>,
        el,
      );
    }
  }

  return (
    <AdPlaceholder
      className={className}
      forwardedRef={forwardedRef}
      isBonzaiTruSkin={isBonzaiTruSkin}
      isScrollX={isScrollX}
      isScrollXAdSlot={isScrollXAdSlot}
      labelPosition={labelPosition}
      labelSpace={labelSpace}
      lgSizes={lgSizes}
      mdSizes={mdSizes}
      placeholderPadding={placeholderPadding}
      ref={ref}
      sizeRendered={sizeRendered}
      sizeRenderedPlaceholder={sizeRenderedPlaceholder}
      sizes={sizes}
      slotId={slotId}
      sticky={sticky}
      stickyOnTablet={stickOnTablet}
      supportPortal={supportPortal}
      withLabel={withLabel}
      withPlaceholder={withPlaceholder}
      withPlaceholderBackground={withPlaceholderBackground}
      xlSizes={xlSizes}
    >
      {ad}
    </AdPlaceholder>
  );
}
