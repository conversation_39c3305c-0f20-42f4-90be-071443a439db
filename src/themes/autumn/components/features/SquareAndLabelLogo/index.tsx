import clsx from 'clsx';
import React from 'react';

import Link from 'themes/autumn/components/generic/Link';
import Logo, { LogoType } from 'themes/autumn/components/generic/Logo';

export interface SquareAndLabelLogoProps {
  logoHeight: string;
  logoWidth: string;
  title?: string;
  titleClassName?: string;
  url?: string;
}

export function SquareAndLabelLogo({
  logoHeight,
  logoWidth,
  title,
  titleClassName,
  url,
}: SquareAndLabelLogoProps): React.ReactElement {
  const content = (
    <div className="flex">
      <span
        className={clsx(
          'z-10 -mr-0.5 flex items-center rounded-r bg-white pr-1',
          logoWidth,
        )}
      >
        <Logo
          className={clsx('contents w-auto', logoHeight)}
          imageClassName={`!${logoWidth}`}
          type={LogoType.SQUARE}
          withLink={false}
        />
      </span>
      <span
        className={clsx(
          'rounded-r py-0.5 pl-2.5 pr-1.5 font-semibold uppercase tracking-wider',
          titleClassName,
        )}
      >
        {title}
      </span>
    </div>
  );
  return (
    <div className="flex items-center gap-x-1">
      {url ? (
        <Link href={url} noStyle>
          {content}
        </Link>
      ) : (
        content
      )}
    </div>
  );
}
