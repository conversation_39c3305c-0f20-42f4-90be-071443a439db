import { useAppSelector } from 'store/hooks';
import Link from 'themes/autumn/components/generic/Link';

import type { LinkProps } from 'themes/autumn/components/generic/Link';

type Props = Omit<LinkProps, 'href' | 'target'>;

const URL = 'https://austcommunitymedia.my.site.com/faq/s/contactsupport?pub=';

export default function HelpLink(props: Props) {
  const name = useAppSelector((state) => state.conf.name);
  const href = `${URL}${encodeURIComponent(name)}`;

  return (
    <Link
      href={href}
      target="_blank"
      // eslint-disable-next-line react/jsx-props-no-spreading
      {...props}
    />
  );
}
