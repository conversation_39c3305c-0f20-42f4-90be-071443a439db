import clsx from 'clsx';

import Link from '../Link';

export enum BannerType {
  Default = 'default',
  EXPLORE_HOMEPAGE = 'explore_homepage',
  CAROUSEL = 'carousel',
}

interface BannerProps {
  animate?: boolean;
  animateEndClassName?: string;
  animateStartClassName?: string;
  bannerType: BannerType;
  bgOpacity?: string;
  btnText?: string;
  description: string;
  image: string;
  openNewWindow: boolean;
  title: string;
  url: string;
}

export default function Banner({
  animate = false,
  animateEndClassName = '',
  animateStartClassName = '',
  bannerType = BannerType.Default,
  bgOpacity = 'opacity-30',
  btnText = 'Explore',
  description,
  image,
  openNewWindow = false,
  title,
  url,
}: BannerProps): React.ReactElement {
  return (
    <div
      className={clsx('relative', {
        'h-80 md:h-96': bannerType === BannerType.Default,
        'h-96 md:h-120': bannerType === BannerType.EXPLORE_HOMEPAGE,
      })}
    >
      <div
        className={clsx(
          'relative flex w-full flex-col items-center text-center',
          {
            'py-40 md:py-52': bannerType === BannerType.Default,
            'py-48 md:py-60': [
              BannerType.EXPLORE_HOMEPAGE,
              BannerType.CAROUSEL,
            ].includes(bannerType),
          },
        )}
      >
        <div
          className="absolute left-0 top-0 z-0 size-full bg-cover bg-center"
          style={{
            backgroundImage: `url(${image})`,
          }}
        />
        <div
          className={clsx(
            'absolute left-0 top-0 block size-full bg-neutral-900',
            bgOpacity,
          )}
        />
      </div>

      <div
        className={clsx(
          'absolute inset-0 flex flex-col items-center justify-center text-center',
          {
            [animateEndClassName]: animate && !!animateEndClassName,
            [animateStartClassName]: !animate && !!animateStartClassName,
            'bottom-8': [
              BannerType.EXPLORE_HOMEPAGE,
              BannerType.CAROUSEL,
            ].includes(bannerType),
            'px-14': bannerType === BannerType.CAROUSEL,
          },
        )}
      >
        <div className="mx-2 line-clamp-3 text-2xl font-medium tracking-wide text-white md:text-heading2">
          {title}
        </div>
        <div className="m-4 line-clamp-3 max-w-2xl text-center text-sm font-medium tracking-wide text-white md:mx-auto md:text-base">
          {description}
        </div>
        {url && (
          <Link
            className="mt-4 rounded border-2 border-white/20 bg-white/10 px-28 py-2 font-inter text-base font-medium text-white shadow-sm hover:bg-white/30 md:px-12 md:py-3.5"
            href={url}
            noStyle
            target={openNewWindow ? '_blank' : undefined}
          >
            {btnText}
          </Link>
        )}
      </div>
    </div>
  );
}
