import React from 'react';

import { getRandomImageUrl, loremIpsumGenerator } from 'util/storybook';

import Card from '.';

import type { Meta, StoryFn } from '@storybook/react';

export default {
  component: Card,
  title: 'Components/Generic/Card',
} as Meta<typeof Card>;

/* eslint-disable react/jsx-props-no-spreading */
const TemplateDefault: StoryFn<typeof Card> = (args) => <Card {...args} />;
/* eslint-enable react/jsx-props-no-spreading */

const unstyledBottomSection = <span>Bottom Section</span>;
const unstyledDescription = <span>{loremIpsumGenerator()}</span>;

export const Default = TemplateDefault.bind({});

Default.args = {
  bottomSection: unstyledBottomSection,
  date: 'March 28th',
  descriptionSection: unstyledDescription,
  location: 'City, State',
  thumbnail: getRandomImageUrl(),
  title: loremIpsumGenerator(),
};
