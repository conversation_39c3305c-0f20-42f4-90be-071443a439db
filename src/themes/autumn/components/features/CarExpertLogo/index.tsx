import Logo from '../Logo';

export const alt = 'Car Expert';
export const src =
  'https://cdn.newsnow.io/Mastheads/car_expert_strap_logo.svg';

interface Props {
  hideIcon?: boolean;
  iconClassName?: string;
  imgClassName?: string;
}

export default function CarExpertLogo({
  hideIcon = false,
  iconClassName,
  imgClassName,
}: Props): React.ReactElement {
  return (
    <Logo
      alt={alt}
      hideIcon={hideIcon}
      iconClassName={iconClassName}
      imgClassName={imgClassName}
      src={src}
    />
  );
}
