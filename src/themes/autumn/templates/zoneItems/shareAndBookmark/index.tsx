import BookmarkButton from 'components/Bookmark';
import { useAppSelector } from 'store/hooks';
import ShareButton from 'themes/autumn/templates/stories/common/ShareButton';
import { UserBookmarkResourceType } from 'types/sepang-types/bookmark';
import { useWindowHref } from 'util/hooks';

const ShareAndBookmark = () => {
  const page = useAppSelector((state) => state.page);
  const url = useWindowHref({ strip: true });

  return (
    <div className="mt-6 flex w-full flex-row items-center justify-start gap-4 md:mt-0 md:justify-end">
      <BookmarkButton
        containerClassName="hover:bg-gray-100 py-0 h-9 w-[94px]"
        metadata={{
          description: page.metaDescription,
          ...(page.cardImage && { thumbnail: page.cardImage }),
          title: page.metaTitle || page.name,
          url,
        }}
        resourceId={page.pageId.toString()}
        resourceType={UserBookmarkResourceType.PAGE}
        title="Save"
      />
      <ShareButton className="border-1 border-gray-300" url={url} />
    </div>
  );
};

export default ShareAndBookmark;
