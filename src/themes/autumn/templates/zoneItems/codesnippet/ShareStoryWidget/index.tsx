import React from 'react';

import ShareWidget from 'themes/autumn/components/ugc/ShareWidget';

const StoryIcon = () => (
  <svg fill="none" height="123" viewBox="0 0 123 123" width="123">
    <path
      d="m61.107 30.108 7.8227 19.88 20.798 0.931c0.5603 0.0226 1.0439 0.3932 1.2111 0.9264 0.1718 0.5378-0.0135 1.1163-0.4609 1.4597l-16.455 13.553s3.9019 10.686 3.3882 11.293-1.5411 2.265-1.5411 2.265 0.0773-0.6399-0.9106 2.335c-0.988 2.9749-1.2206 3.1074-1.2206 3.1074l-14.177-8.9359-17.377 11.474c-0.6146 0.4022-1.4416 0.235-1.8484-0.3796-0.2169-0.3299-0.2757-0.7457-0.1627-1.1253l5.3417-20.616-16.278-12.984c-0.4383-0.3435-0.6191-0.9264-0.4428-1.4552 0.1717-0.5332 0.6598-0.8993 1.2156-0.9174l21.294-1.3015 7.3166-19.487 0.0045 0.0046c0.1898-0.5152 0.6824-0.8587 1.2337-0.8632 0.5514-0.0045 1.044 0.3299 1.2473 0.8451l-1e-4 -0.0099z"
      fill="#FCD34D"
    />
    <path
      clip-rule="evenodd"
      d="m69.825 49.916-8.6407-21.959 1e-4 0.0109c-0.2246-0.569-0.7687-0.9384-1.3777-0.9334-0.6089 0.0049-1.153 0.3843-1.3627 0.9534l-5e-3 -5e-3 -8.0816 21.525-23.521 1.4376c-0.614 0.02-1.1531 0.4243-1.3428 1.0134-0.1947 0.584 5e-3 1.2279 0.4892 1.6073l17.98 14.342-5.9003 22.772c-0.1248 0.4193-0.0599 0.8785 0.1797 1.2429 0.4493 0.6789 1.3628 0.8636 2.0417 0.4193l19.194-12.674 12.127 7.73c0.7579-0.2009 1.5409-0.3406 2.3432-0.4135 0.0553-0.4776 0.13-0.9534 0.2242-1.4261l-13.928-8.877c-0.4892-0.3145-1.1182-0.3095-1.5974 0.0149v-0.0099l-16.802 11.092 5.1914-20.067c0.1497-0.5591-0.0499-1.1531-0.4992-1.5175l-15.738-12.544 20.686-1.2629c0.614-0.0349 1.1382-0.4492 1.3229-1.0333l7.0383-18.764 7.5925 19.283-0.0013-0.0049c0.2196 0.5641 0.7637 0.9385 1.3727 0.9385v5e-3l20.042 0.8985-15.999 13.183c-0.4493 0.3644-0.639 0.9634-0.4842 1.5224l3.4418 12.472c0.5879-1.1332 1.3066-2.2163 2.156-3.2269l-2.5165-9.1084 18.175-14.97c0.4941-0.3794 0.6988-1.0183 0.5091-1.6124-0.1847-0.589-0.7188-0.9983-1.3378-1.0233l-22.973-1.0283zm-32.211-24.899c0.7039-0.4043 0.9435-1.3028 0.5391-2.0066l-3.1598-5.476c-0.4093-0.7039-1.3078-0.9435-2.0116-0.5391-0.7039 0.4093-0.9435 1.3078-0.5342 2.0116h-5e-3l3.1599 5.476c0.4093 0.7039 1.3078 0.9435 2.0116 0.5341zm-16.272 16.234c0.3943-0.6889 0.1647-1.5725-0.5192-1.9868l-5.476-3.1648c-0.6989-0.3943-1.5924-0.1498-1.9917 0.5491s-0.1647 1.5924 0.5291 1.9967h-5e-3l5.486 3.1648c0.7039 0.3844 1.5774 0.1348 1.9768-0.559zm-5.9203 22.218c0-0.8136-0.6588-1.4725-1.4725-1.4725h-6.3245c-0.81361 0-1.4725 0.6589-1.4725 1.4725s0.65885 1.4725 1.4725 1.4725h6.3245c0.8136 0 1.4725-0.6589 1.4725-1.4725zm5.9453 22.203c-0.4044-0.7039-1.3029-0.9485-2.0067-0.5392l-5.476 3.1599c-0.7039 0.4093-0.9435 1.3078-0.5391 2.0116 0.4093 0.7039 1.3078 0.9435 2.0116 0.5341v5e-3l5.476-3.1598c0.7039-0.4093 0.9435-1.3078 0.5342-2.0116zm16.248 16.252c-0.7039-0.404-1.6024-0.159-2.0117 0.54l-3.1549 5.48c-0.3943 0.704-0.1497 1.593 0.5491 1.992 0.6989 0.405 1.5874 0.165 1.9967-0.529l5e-3 5e-3 3.1549-5.486c0.1947-0.339 0.2446-0.739 0.1447-1.113-0.0998-0.374-0.3494-0.699-0.6838-0.889zm20.726 7.413h-5e-3l0.0051 6.32c0 0.813 0.6588 1.472 1.4725 1.472 0.8136 0 1.4725-0.659 1.4725-1.472v-6.32c0.0399-0.414-0.0998-0.828-0.3794-1.138-0.2795-0.309-0.6788-0.484-1.0932-0.484-0.4193 0-0.8186 0.175-1.0982 0.484-0.2795 0.31-0.4143 0.724-0.3743 1.138zm45.873-45.864c0 0.8136 0.659 1.4725 1.473 1.4725h6.324c0.814 0 1.473-0.6589 1.473-1.4725s-0.659-1.4725-1.473-1.4725h-6.324c-0.814 0-1.473 0.6588-1.473 1.4725zm-5.945-22.198c0.4044 0.7039 1.3029 0.9435 2.007 0.5341l5.476-3.1648c0.704-0.4043 0.943-1.3028 0.539-2.0066-0.409-0.7039-1.308-0.9435-2.012-0.5391l-5.4758 3.1648c-0.7039 0.4093-0.9435 1.3078-0.5342 2.0116zm-16.234-16.282c0.6889 0.3943 1.5724 0.1647 1.9868-0.5192l3.1648-5.476c0.3993-0.7039 0.1547-1.5974-0.5441-2.0017-0.3395-0.1897-0.7388-0.2446-1.1182-0.1398-0.3744 0.0999-0.6938 0.3494-0.8835 0.6839l-3.1649 5.476c-0.3843 0.7039-0.1347 1.5774 0.5591 1.9768zm-21.115-6.2547c0.2796-0.3095 0.4143-0.7238 0.3744-1.1381l-1e-4 -6.3245c0-0.8136-0.6589-1.4725-1.4725-1.4725s-1.4725 0.65884-1.4725 1.4725v6.3245c-0.04 0.4143 0.0998 0.8286 0.3793 1.1381 0.2796 0.3045 0.6789 0.4842 1.0932 0.4842 0.4194 0 0.8187-0.1797 1.0982-0.4842z"
      fill="#1F2937"
      fill-rule="evenodd"
    />
    <path
      d="m91.546 76.044h0.1282c3.4616 0.032 6.8967 1.3616 9.5375 4.0025 3.091 3.091 4.402 7.2991 3.919 11.328l-0.155 1.2869h2.001c4.955 0 8.94 3.9852 8.94 8.9401 0 4.954-3.985 8.939-8.94 8.939h-29.082c-5.9213 0-10.72-4.799-10.72-10.72 0-5.5898 4.2702-10.173 9.7298-10.669l0.9309-0.0846 0.1074-0.9285c0.3421-2.9573 1.6478-5.8214 3.918-8.0916 2.6727-2.6725 6.1782-4.0032 9.6849-4.0032zm-0.0085 10.164h-0.0084l-0.0083 2e-4c-0.5773 0.0127-1.1594 0.242-1.5476 0.5971-8e-4 7e-4 -0.0015 0.0014-0.0023 0.0021l-6.5172 5.9247c-0.9807 0.8655-1.0104 2.3986-0.1791 3.317 0.8564 0.9462 2.393 0.991 3.3141 0.1533l3e-4 -3e-4 2.62-2.3848v10.159c0 1.291 1.0458 2.337 2.337 2.337s2.3369-1.046 2.3369-2.337v-10.159l2.62 2.3848 4e-4 3e-4c0.9585 0.8717 2.4423 0.7205 3.2746-0.1118h1e-4c0.9907-0.9909 0.7417-2.5628-0.134-3.3533l-0.0027-0.0024-6.5232-5.9302s-1e-4 0-1e-4 -1e-4c-0.2177-0.1984-0.4683-0.3671-0.7726-0.4739-0.2981-0.1045-0.5771-0.1244-0.8079-0.1227z"
      fill="#0D9488"
      stroke="#1F2937"
      stroke-width="2.2998"
    />
  </svg>
);

export default function ShareStoryWidget(): React.ReactElement {
  return (
    <ShareWidget
      icon={<StoryIcon />}
      linkHref="/notice-board/share-story/"
      linkText="Send your News"
      subtitle="Tell all the stories that matter to the community. It's free."
      title="Got a story to tell?"
    />
  );
}
