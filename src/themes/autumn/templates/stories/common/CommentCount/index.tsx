import CommentIcon from 'themes/autumn/components/icons/CommentIcon';
import Count from 'themes/autumn/components/stories/Comments/Count';

import type { StoryState } from 'store/slices/story';

interface CommentCountProps {
  story: StoryState;
}

const CommentCount: React.FC<CommentCountProps> = ({ story }) => (
  <Count
    className="flex h-[36px] w-[94px] flex-row items-center justify-center rounded-3xl hover:bg-gray-100"
    countOnly
    icon={<CommentIcon className="mr-2" />}
    id={story.id}
    state={story.comments}
  />
);

export default CommentCount;
