import React from 'react';

import { useAppSelector } from 'store/hooks';
import Ad, { AdSize } from 'themes/autumn/components/ads/Ad';
import { useLazyLoadComponentState } from 'util/hooks';
import { isSponsoredPage } from 'util/page';

function Teads(): React.ReactElement | null {
  const teads = useAppSelector((state) => state.features.teads);
  const viewType = useAppSelector((state) => state.settings.viewType);
  const { showComponent } = useLazyLoadComponentState();

  if (!teads.enabled || isSponsoredPage(viewType) || !showComponent) {
    return null;
  }

  return (
    <div>
      <div id="teads" />
      <Ad
        position={1}
        publiftName="teads-1"
        sizes={AdSize.teads}
        slotId="teads-container"
        withLabel={false}
        withPlaceholder={false}
        withPlaceholderBackground={false}
      />
    </div>
  );
}

export default Teads;
