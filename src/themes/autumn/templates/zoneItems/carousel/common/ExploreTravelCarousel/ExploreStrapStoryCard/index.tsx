import clsx from 'clsx';

import Headline from 'themes/autumn/components/stories/Headline';
import { ColorPalette } from 'themes/autumn/components/stories/Signpost';
import { ImagePosition } from 'types/ZoneItems';

import { CardType } from '../types';

import type { Story } from 'types/Story';

interface ExploreStrapStoryCardProps {
  containerClassName?: string;
  enableDivider?: boolean;
  enforceSummary?: boolean;
  headlineWrapperClassName?: string;
  idPrefix?: string;
  imageHeight?: number;
  imagePosition?: ImagePosition;
  imageWidth?: number;
  imageWrapperClassName?: string;
  leadingImageClassName?: string;
  restoreArticleListPosition?: boolean;
  showSummaryInStoryList?: boolean;
  signPostClassName?: string;
  signPostColorPalette?: ColorPalette;
  sourceClassName?: string;
  story?: Story;
  storyListId: number;
  summaryFontClassName?: string;
  textBlockPaddingClassName?: string;
  titleSize?: string;
  type: CardType;
}

export default function ExploreStrapStoryCard({
  containerClassName = '',
  enableDivider = false,
  enforceSummary = true,
  headlineWrapperClassName = 'md:w-72',
  idPrefix = '',
  imageHeight = 192,
  imagePosition = ImagePosition.ABOVE,
  imageWidth = 288,
  imageWrapperClassName = '!mb-0',
  leadingImageClassName = 'xl:w-72 lg:min-w-[231px]',
  restoreArticleListPosition = false,
  showSummaryInStoryList = true,
  signPostClassName = '!mb-2 mt-0',
  signPostColorPalette = ColorPalette.PALETTE_EXPLORE,
  sourceClassName = 'text-xs',
  story,
  storyListId,
  summaryFontClassName = '!font-inter !line-clamp-2',
  textBlockPaddingClassName = '',
  titleSize = 'text-xl',
  type,
}: ExploreStrapStoryCardProps) {
  if (type === CardType.LOADING) {
    return (
      <div
        className={clsx(
          'animate-pulse overflow-hidden rounded-lg bg-white md:w-72 lg:w-full',
          containerClassName,
        )}
      >
        <div className="relative h-48 rounded-lg bg-gray-100" />
        <div className="flex flex-col pt-2">
          <div className="h-5 w-3/4 bg-gray-100" />
          <div className="mt-1.5 h-5 w-full bg-gray-100" />
          <div className="mt-3 h-4 w-3/4 bg-gray-100" />
          <div className="mt-1 h-4 w-full bg-gray-100" />
          <div className="mt-2 h-3 w-2/3 bg-gray-100" />
        </div>
      </div>
    );
  }
  if (type === CardType.PADDING) {
    return (
      <div
        className={clsx('bg-transparent md:w-0 lg:w-full', containerClassName)}
      />
    );
  }

  if (type === CardType.CONTENT && story) {
    return (
      <div
        className={clsx('group text-gray-900', containerClassName)}
        id={`${idPrefix}-story-${story.id}`}
        role="article"
      >
        {enableDivider && (
          <div className="w-full border-t border-gray-300 pb-7 md:pb-7" />
        )}
        <Headline
          enableBylineLink
          enableSummaryLink
          enforceSummary={enforceSummary}
          headlineWrapperClassName={headlineWrapperClassName}
          hideCommentComponent
          imageHeight={imageHeight}
          imageLazyLoad
          imagePosition={imagePosition}
          imageResize
          imageWidth={imageWidth}
          imageWrapperClassName={imageWrapperClassName}
          index={Number(story.id)}
          isHeading
          leadingImageClassName={clsx(
            '!rounded-lg aspect-[1.5] object-cover overflow-hidden bg-gray-100 h-full',
            leadingImageClassName,
          )}
          restoreArticleListPosition={restoreArticleListPosition}
          showByline
          showImagePlayIcon={false}
          showSignpost
          showSummaryInStoryList={showSummaryInStoryList}
          signPostClassName={signPostClassName}
          signPostColorPalette={signPostColorPalette}
          sourceClassName={sourceClassName}
          story={story}
          storyListId={storyListId}
          summaryFontClassName={summaryFontClassName}
          textBlockPaddingClassName={textBlockPaddingClassName}
          titleSize={clsx(
            'line-clamp-3 font-medium font-playfair visited:text-gray-900 group-hover:underline',
            titleSize,
          )}
        />
      </div>
    );
  }
  return null;
}
