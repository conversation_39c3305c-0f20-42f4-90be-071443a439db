'use client';

import React, { useState } from 'react';

import Slider from 'themes/autumn/components/generic/Slider';
import { useOnce } from 'util/hooks';
import { fetchFarmbuyFeaturedProperties } from 'util/organization/suzuka';

import FarmbuyPropertyCard from './PropertyEntry';

import type { FarmbuyProperty } from 'types/Farmbuy';

function PropertySlider() {
  const [featuredProperties, setFeaturedProperties] = useState<
    FarmbuyProperty[]
  >([]);
  const [initialized, setInitialized] = useState(false);

  useOnce(() => {
    fetchFarmbuyFeaturedProperties()
      .then((res) => {
        setFeaturedProperties(res.properties);
        setInitialized(true);
      })
      .catch(console.error);
    return true;
  }, []);

  if (!initialized) {
    return null;
  }

  return (
    <div className="my-4">
      <Slider childWidth={288}>
        {featuredProperties.map((farmbuyProperty: FarmbuyProperty) => (
          <FarmbuyPropertyCard
            farmbuyProperty={farmbuyProperty}
            key={farmbuyProperty.id}
          />
        ))}
      </Slider>
    </div>
  );
}

export default PropertySlider;
