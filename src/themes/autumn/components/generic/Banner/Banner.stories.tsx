import React from 'react';

import { getRandomImageUrl, loremIpsumGenerator } from 'util/storybook';

import Banner from '.';

import type { Meta, StoryFn } from '@storybook/react';

export default {
  component: Banner,
  title: 'Components/Generic/Banner',
} as Meta<typeof Banner>;

/* eslint-disable react/jsx-props-no-spreading */
const TemplateDefault: StoryFn<typeof Banner> = (args) => <Banner {...args} />;
/* eslint-enable react/jsx-props-no-spreading */

export const Default = TemplateDefault.bind({});

Default.args = {
  description: loremIpsumGenerator(),
  image: getRandomImageUrl(1080, 1920),
  openNewWindow: true,
  title: loremIpsumGenerator(),
  url: 'https://google.com',
};
