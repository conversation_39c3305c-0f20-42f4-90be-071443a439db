import { render } from '@testing-library/react';

import { createStore } from 'store/store';
import { TestWrapper } from 'util/jest';

import SubscriptionFooter from '.';

describe('subscription footer', () => {
  it('renders', () => {
    expect.assertions(1);

    const { container } = render(
      <TestWrapper store={createStore()}>
        <SubscriptionFooter />
      </TestWrapper>,
    );

    expect(container.firstChild).toMatchSnapshot();
  });
});
