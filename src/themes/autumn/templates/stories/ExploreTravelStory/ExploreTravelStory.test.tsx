import { fireEvent, render, screen, waitFor } from '@testing-library/react';

import BookmarkProvider from 'components/Bookmark/BookmarkProvider';
import { CompleteProfileEnrichment } from 'store/slices/features';
import { createStore } from 'store/store';
import StoryElements from 'themes/autumn/components/stories/StoryElements';
import {
  Service,
  ServiceType,
  StoryChannel,
  StoryElementType,
} from 'types/Story';
import { StoryViewType } from 'types/ZoneItems';
import { TestWrapper, genMockStories } from 'util/jest';
import { fetchMailGroups } from 'util/mail';
import * as newsletterUtils from 'util/newsletter';

import NewsletterSignup from './NewsletterSignup';

import ExploreTravelStory from '.';

import type { EnhancedStore } from '@reduxjs/toolkit';
import type { StoryState } from 'store/slices/story';
import type { RootState } from 'store/store';
import type { PianoLoginUserData } from 'types/Piano';
import type { MailGroupsApiResponse } from 'types/mail';

describe('ExploreTravelStory component', () => {
  it('render', () => {
    expect.assertions(1);

    const story: StoryState = {
      ...genMockStories({ length: 1 })[0],
      authors: [
        {
          bio: 'Test Bio',
          facebook: '',
          id: 1,
          instagram: '',
          linkedin: '',
          mugshot: '',
          name: 'Test Author',
          position: 'Test Position',
          tiktok: '',
          twitter: '',
          web: '',
          youtube: '',
        },
      ],
      elements: [
        {
          colourMode: 'RGBA',
          cropConfig: {
            cropHeight: 331,
            cropWidth: 589,
            cropX: 0,
            cropY: 1,
            focalX: 295,
            focalY: 167,
            scale: 1,
          },
          description: '',
          height: 333,
          link: '',
          title: '',
          type: StoryElementType.Image,
          // eslint-disable-next-line @stylistic/max-len
          uri: '/vb4YnMcqczPKYQnWpMkavU/901aee49-93ed-45ed-b132-77ffea46780c.png',
          visible: [StoryChannel.Web, StoryChannel.Print],
          width: 589,
        },
        {
          level: '2',
          text: 'Test Heading 2',
          type: StoryElementType.Heading,
        },
        {
          text: 'Test Paragraph',
          type: StoryElementType.Paragraph,
        },
      ],
    };
    const store = createStore((state) => ({
      ...state,
      accessToken: 'test_token',
      features: {
        ...state.features,
        exploreTravelRecirculation: {
          ...state.features.exploreTravelRecirculation,
          data: {
            latestStoryWidget: {
              limit: 5,
              stories: genMockStories({ length: 5 }),
              storyListId: null,
            },
            mostPopularStoryWidget: {
              limit: 10,
              stories: genMockStories({ length: 10 }),
              storyListId: null,
            },
          },
          enabled: true,
        },
      },
      settings: {
        ...state.settings,
        host: 'test.com.au',
        viewType: StoryViewType.STORY_TRAVEL,
      },
      story,
    }));
    const { container } = render(
      <TestWrapper store={store}>
        <ExploreTravelStory />
      </TestWrapper>,
    );

    expect(container.firstChild).toMatchSnapshot();
  });

  it('render with byline', () => {
    expect.assertions(1);

    const story: StoryState = {
      ...genMockStories({ length: 1 })[0],
      authors: [],
      byline: 'Test Byline',
      elements: [
        {
          colourMode: 'RGBA',
          cropConfig: {
            cropHeight: 331,
            cropWidth: 589,
            cropX: 0,
            cropY: 1,
            focalX: 295,
            focalY: 167,
            scale: 1,
          },
          description: '',
          height: 333,
          link: '',
          title: '',
          type: StoryElementType.Image,
          // eslint-disable-next-line @stylistic/max-len
          uri: '/vb4YnMcqczPKYQnWpMkavU/901aee49-93ed-45ed-b132-77ffea46780c.png',
          visible: [StoryChannel.Web, StoryChannel.Print],
          width: 589,
        },
        {
          level: '2',
          text: 'Test Heading 2',
          type: StoryElementType.Heading,
        },
        {
          text: 'Test Paragraph',
          type: StoryElementType.Paragraph,
        },
      ],
    };
    const store = createStore((state) => ({
      ...state,
      accessToken: 'test_token',
      features: {
        ...state.features,
        exploreTravelRecirculation: {
          ...state.features.exploreTravelRecirculation,
          data: {
            latestStoryWidget: {
              limit: 5,
              stories: genMockStories({ length: 5 }),
              storyListId: null,
            },
            mostPopularStoryWidget: {
              limit: 10,
              stories: genMockStories({ length: 10 }),
              storyListId: null,
            },
          },
          enabled: true,
        },
      },
      settings: {
        ...state.settings,
        host: 'test.com.au',
        viewType: StoryViewType.STORY_TRAVEL,
      },
      story,
    }));
    const { container } = render(
      <TestWrapper store={store}>
        <ExploreTravelStory />
      </TestWrapper>,
    );

    expect(container.firstChild).toMatchSnapshot();
  });

  it('render with byline with updated time', () => {
    expect.assertions(1);

    const story: StoryState = {
      ...genMockStories({ length: 1 })[0],
      authors: [],
      byline: 'Test Byline',
      elements: [
        {
          colourMode: 'RGBA',
          cropConfig: {
            cropHeight: 331,
            cropWidth: 589,
            cropX: 0,
            cropY: 1,
            focalX: 295,
            focalY: 167,
            scale: 1,
          },
          description: '',
          height: 333,
          link: '',
          title: '',
          type: StoryElementType.Image,
          // eslint-disable-next-line @stylistic/max-len
          uri: '/vb4YnMcqczPKYQnWpMkavU/901aee49-93ed-45ed-b132-77ffea46780c.png',
          visible: [StoryChannel.Web, StoryChannel.Print],
          width: 589,
        },
        {
          level: '2',
          text: 'Test Heading 2',
          type: StoryElementType.Heading,
        },
        {
          text: 'Test Paragraph',
          type: StoryElementType.Paragraph,
        },
      ],
      publishFrom: '2021-01-01T00:00:00.000Z',
      updatedOn: '2021-01-02T00:00:00.000Z',
    };
    const store = createStore((state) => ({
      ...state,
      accessToken: 'test_token',
      features: {
        ...state.features,
        exploreTravelRecirculation: {
          ...state.features.exploreTravelRecirculation,
          data: {
            latestStoryWidget: {
              limit: 5,
              stories: genMockStories({ length: 5 }),
              storyListId: null,
            },
            mostPopularStoryWidget: {
              limit: 10,
              stories: genMockStories({ length: 10 }),
              storyListId: null,
            },
          },
          enabled: true,
        },
      },
      settings: {
        ...state.settings,
        host: 'test.com.au',
        viewType: StoryViewType.STORY_TRAVEL,
      },
      story,
    }));
    const { container } = render(
      <TestWrapper store={store}>
        <ExploreTravelStory />
      </TestWrapper>,
    );

    expect(container.firstChild).toMatchSnapshot();
  });

  it('render with byline with updated time on same day', () => {
    expect.assertions(1);

    const story: StoryState = {
      ...genMockStories({ length: 1 })[0],
      authors: [],
      byline: 'Test Byline',
      elements: [
        {
          colourMode: 'RGBA',
          cropConfig: {
            cropHeight: 331,
            cropWidth: 589,
            cropX: 0,
            cropY: 1,
            focalX: 295,
            focalY: 167,
            scale: 1,
          },
          description: '',
          height: 333,
          link: '',
          title: '',
          type: StoryElementType.Image,
          // eslint-disable-next-line @stylistic/max-len
          uri: '/vb4YnMcqczPKYQnWpMkavU/901aee49-93ed-45ed-b132-77ffea46780c.png',
          visible: [StoryChannel.Web, StoryChannel.Print],
          width: 589,
        },
        {
          level: '2',
          text: 'Test Heading 2',
          type: StoryElementType.Heading,
        },
        {
          text: 'Test Paragraph',
          type: StoryElementType.Paragraph,
        },
      ],
      publishFrom: '2021-01-01T00:00:00.000Z',
      updatedOn: '2021-01-01T01:00:00.000Z',
    };
    const store = createStore((state) => ({
      ...state,
      accessToken: 'test_token',
      features: {
        ...state.features,
        exploreTravelRecirculation: {
          ...state.features.exploreTravelRecirculation,
          data: {
            latestStoryWidget: {
              limit: 5,
              stories: genMockStories({ length: 5 }),
              storyListId: null,
            },
            mostPopularStoryWidget: {
              limit: 10,
              stories: genMockStories({ length: 10 }),
              storyListId: null,
            },
          },
          enabled: true,
        },
      },
      settings: {
        ...state.settings,
        host: 'test.com.au',
        viewType: StoryViewType.STORY_TRAVEL,
      },
      story,
    }));
    const { container } = render(
      <TestWrapper store={store}>
        <ExploreTravelStory />
      </TestWrapper>,
    );

    expect(container.firstChild).toMatchSnapshot();
  });

  it('render with byline with updated time on different day', () => {
    expect.assertions(1);

    const story: StoryState = {
      ...genMockStories({ length: 1 })[0],
      authors: [],
      byline: 'Test Byline',
      elements: [
        {
          colourMode: 'RGBA',
          cropConfig: {
            cropHeight: 331,
            cropWidth: 589,
            cropX: 0,
            cropY: 1,
            focalX: 295,
            focalY: 167,
            scale: 1,
          },
          description: '',
          height: 333,
          link: '',
          title: '',
          type: StoryElementType.Image,
          // eslint-disable-next-line @stylistic/max-len
          uri: '/vb4YnMcqczPKYQnWpMkavU/901aee49-93ed-45ed-b132-77ffea46780c.png',
          visible: [StoryChannel.Web, StoryChannel.Print],
          width: 589,
        },
        {
          level: '2',
          text: 'Test Heading 2',
          type: StoryElementType.Heading,
        },
        {
          text: 'Test Paragraph',
          type: StoryElementType.Paragraph,
        },
      ],
      publishFrom: '2021-01-01T00:00:00.000Z',
      updatedOn: '2021-02-01T01:00:00.000Z',
    };
    const store = createStore((state) => ({
      ...state,
      accessToken: 'test_token',
      features: {
        ...state.features,
        exploreTravelRecirculation: {
          ...state.features.exploreTravelRecirculation,
          data: {
            latestStoryWidget: {
              limit: 5,
              stories: genMockStories({ length: 5 }),
              storyListId: null,
            },
            mostPopularStoryWidget: {
              limit: 10,
              stories: genMockStories({ length: 10 }),
              storyListId: null,
            },
          },
          enabled: true,
        },
      },
      settings: {
        ...state.settings,
        host: 'test.com.au',
        viewType: StoryViewType.STORY_TRAVEL,
      },
      story,
    }));
    const { container } = render(
      <TestWrapper store={store}>
        <ExploreTravelStory />
      </TestWrapper>,
    );

    expect(container.firstChild).toMatchSnapshot();
  });

  it('render with byline with no updated time', () => {
    expect.assertions(1);

    const story: StoryState = {
      ...genMockStories({ length: 1 })[0],
      authors: [],
      byline: 'Test Byline',
      elements: [
        {
          colourMode: 'RGBA',
          cropConfig: {
            cropHeight: 331,
            cropWidth: 589,
            cropX: 0,
            cropY: 1,
            focalX: 295,
            focalY: 167,
            scale: 1,
          },
          description: '',
          height: 333,
          link: '',
          title: '',
          type: StoryElementType.Image,
          // eslint-disable-next-line @stylistic/max-len
          uri: '/vb4YnMcqczPKYQnWpMkavU/901aee49-93ed-45ed-b132-77ffea46780c.png',
          visible: [StoryChannel.Web, StoryChannel.Print],
          width: 589,
        },
        {
          level: '2',
          text: 'Test Heading 2',
          type: StoryElementType.Heading,
        },
        {
          text: 'Test Paragraph',
          type: StoryElementType.Paragraph,
        },
      ],
      publishFrom: '2021-01-01T00:00:00.000Z',
      updatedOn: '2021-01-01T00:00:00.000Z',
    };
    const store = createStore((state) => ({
      ...state,
      accessToken: 'test_token',
      features: {
        ...state.features,
        exploreTravelRecirculation: {
          ...state.features.exploreTravelRecirculation,
          data: {
            latestStoryWidget: {
              limit: 5,
              stories: genMockStories({ length: 5 }),
              storyListId: null,
            },
            mostPopularStoryWidget: {
              limit: 10,
              stories: genMockStories({ length: 10 }),
              storyListId: null,
            },
          },
          enabled: true,
        },
      },
      settings: {
        ...state.settings,
        host: 'test.com.au',
        viewType: StoryViewType.STORY_TRAVEL,
      },
      story,
    }));
    const { container } = render(
      <TestWrapper store={store}>
        <ExploreTravelStory />
      </TestWrapper>,
    );

    expect(container.firstChild).toMatchSnapshot();
  });

  it('render without carousel if explore travel feature is not enabled', () => {
    expect.assertions(1);

    const story: StoryState = {
      ...genMockStories({ length: 1 })[0],
      authors: [
        {
          bio: 'Test Bio',
          facebook: '',
          id: 1,
          instagram: '',
          linkedin: '',
          mugshot: '',
          name: 'Test Author',
          position: 'Test Position',
          tiktok: '',
          twitter: '',
          web: '',
          youtube: '',
        },
      ],
      elements: [
        {
          colourMode: 'RGBA',
          cropConfig: {
            cropHeight: 331,
            cropWidth: 589,
            cropX: 0,
            cropY: 1,
            focalX: 295,
            focalY: 167,
            scale: 1,
          },
          description: '',
          height: 333,
          link: '',
          title: '',
          type: StoryElementType.Image,
          // eslint-disable-next-line @stylistic/max-len
          uri: '/vb4YnMcqczPKYQnWpMkavU/901aee49-93ed-45ed-b132-77ffea46780c.png',
          visible: [StoryChannel.Web, StoryChannel.Print],
          width: 589,
        },
        {
          level: '2',
          text: 'Test Heading 2',
          type: StoryElementType.Heading,
        },
        {
          text: 'Test Paragraph',
          type: StoryElementType.Paragraph,
        },
      ],
    };
    const store = createStore((state) => ({
      ...state,
      accessToken: 'test_token',
      features: {
        ...state.features,
        exploreTravelRecirculation: {
          ...state.features.exploreTravelRecirculation,
          data: {
            latestStoryWidget: {
              limit: 1,
              stories: [],
              storyListId: null,
            },
            mostPopularStoryWidget: {
              limit: 1,
              stories: [],
              storyListId: null,
            },
          },
          enabled: false,
        },
      },
      settings: {
        ...state.settings,
        host: 'test.com.au',
        viewType: StoryViewType.STORY_TRAVEL,
      },
      story,
    }));
    const { container } = render(
      <TestWrapper store={store}>
        <BookmarkProvider>
          <ExploreTravelStory />
        </BookmarkProvider>
      </TestWrapper>,
    );

    expect(container.firstChild).toMatchSnapshot();
  });

  it('does not show newsletter signup with a one paragraph story', () => {
    // Creating a story with only one paragraph
    const story: StoryState = {
      ...genMockStories({ length: 1 })[0],
      authors: [
        {
          bio: 'Test Bio',
          facebook: '',
          id: 1,
          instagram: '',
          linkedin: '',
          mugshot: '',
          name: 'Test Author',
          position: 'Test Position',
          tiktok: '',
          twitter: '',
          web: '',
          youtube: '',
        },
      ],
      elements: [
        {
          colourMode: 'RGBA',
          cropConfig: {
            cropHeight: 331,
            cropWidth: 589,
            cropX: 0,
            cropY: 1,
            focalX: 295,
            focalY: 167,
            scale: 1,
          },
          description: '',
          height: 333,
          link: '',
          title: '',
          type: StoryElementType.Image,
          // eslint-disable-next-line @stylistic/max-len
          uri: '/vb4YnMcqczPKYQnWpMkavU/901aee49-93ed-45ed-b132-77ffea46780c.png',
          visible: [StoryChannel.Web, StoryChannel.Print],
          width: 589,
        },
        {
          level: '2',
          text: 'Test Heading 2',
          type: StoryElementType.Heading,
        },
        {
          text: 'Single Paragraph',
          type: StoryElementType.Paragraph,
        },
      ],
    };
    const store = createStore((state) => ({
      ...state,
      accessToken: 'test_token',
      features: {
        ...state.features,
        exploreTravelRecirculation: {
          ...state.features.exploreTravelRecirculation,
          data: {
            latestStoryWidget: {
              limit: 5,
              stories: genMockStories({ length: 5 }),
              storyListId: null,
            },
            mostPopularStoryWidget: {
              limit: 10,
              stories: genMockStories({ length: 10 }),
              storyListId: null,
            },
          },
          enabled: true,
        },
      },
      settings: {
        ...state.settings,
        host: 'test.com.au',
        viewType: StoryViewType.STORY_TRAVEL,
      },
      story,
    }));

    render(
      <TestWrapper store={store}>
        <ExploreTravelStory />
      </TestWrapper>,
    );

    // The newsletter component has a "Sign Up" button when rendered
    expect(
      screen.queryByRole('button', { name: /Sign Up/i }),
    ).not.toBeInTheDocument();
  });

  it('hides injected recommendation component when paywall is active', () => {
    const store = createStore((state) => ({
      ...state,
      features: {
        ...state.features,
        piano: {
          ...state.features.piano,
          data: {
            aid: 'test-aid',
            articlePaywallHeadingText: 'Test Heading',
            betaResourceId: 'test-resource',
            completeProfileEnrichments: CompleteProfileEnrichment.NONE,
            ctaVariant: 'default',
            enterpriseSubscriptions: [],
            hasSocialScreen: false,
            header: 'Test Header',
            hideArticleAnnualSavingsPill: false,
            hideSubscriberSignposts: false,
            isAbTesting: false,
            isFullwidthRecommendationContentEnabled: false,
            isPremiumRequest: false,
            isRecommendationContentEnabled: false,
            registrationOnly: false,
            siteId: 'test-site',
            subColour: '#000000',
            subHeader: 'Test Subheader',
            supportAuthServerPaywall: false,
            supportLoginApple: false,
            supportLoginFacebook: false,
            supportLoginGoogle: false,
            supportMonthlyAnnualPaywall: false,
            supportPremiumExtended: false,
            supportPremiumSubscription: false,
            supportPrintBundle: false,
          },
          enabled: true,
        },
      },
      piano: {
        ...state.piano,
        hasAccess: false,
        hasPaywall: true,
      },
    }));

    const { container } = render(
      <TestWrapper store={store}>
        <StoryElements
          elements={[
            {
              text: 'Test Paragraph',
              type: StoryElementType.Paragraph,
            },
            {
              text: 'Another Paragraph',
              type: StoryElementType.Paragraph,
            },
          ]}
          injectComponents={[
            {
              injectComponent: (
                <div data-testid="recommendation">Recommendation</div>
              ),
              injectComponentClassName: 'recommendation-wrapper',
              injectPos: 1,
            },
          ]}
          usePaywall
        />
      </TestWrapper>,
    );

    const recommendation = container.querySelector('.recommendation-wrapper');
    expect(recommendation).toBeNull();
  });

  it('renders correctly with ads for non-sponsored stories', () => {
    expect.assertions(1);

    const story: StoryState = {
      ...genMockStories({ length: 1 })[0],
      authors: [],
      elements: [
        {
          colourMode: 'RGBA',
          cropConfig: {
            cropHeight: 331,
            cropWidth: 589,
            cropX: 0,
            cropY: 1,
            focalX: 295,
            focalY: 167,
            scale: 1,
          },
          description: '',
          height: 333,
          link: '',
          title: '',
          type: StoryElementType.Image,
          uri: '/test-image.png',
          visible: [StoryChannel.Web, StoryChannel.Print],
          width: 589,
        },
        {
          text: 'Test paragraph',
          type: StoryElementType.Paragraph,
        },
      ],
      tags: ['tag1', 'tag2'], // No story-sponsored tag
    };

    // Use the simple format to avoid type issues
    const store = createStore();
    store.dispatch({
      payload: StoryViewType.STORY_TRAVEL,
      type: 'settings/setViewType',
    });
    store.dispatch({
      payload: story,
      type: 'story/setStory',
    });
    store.dispatch({
      payload: { enabled: true, feature: 'adServing' },
      type: 'features/setFeatureEnabled',
    });

    const { container } = render(
      <TestWrapper store={store}>
        <ExploreTravelStory />
      </TestWrapper>,
    );

    expect(container.firstChild).toMatchSnapshot();
  });

  it('renders correctly without ads when story has story-sponsored tag', () => {
    expect.assertions(1);

    const story: StoryState = {
      ...genMockStories({ length: 1 })[0],
      authors: [],
      elements: [
        {
          colourMode: 'RGBA',
          cropConfig: {
            cropHeight: 331,
            cropWidth: 589,
            cropX: 0,
            cropY: 1,
            focalX: 295,
            focalY: 167,
            scale: 1,
          },
          description: '',
          height: 333,
          link: '',
          title: '',
          type: StoryElementType.Image,
          uri: '/test-image.png',
          visible: [StoryChannel.Web, StoryChannel.Print],
          width: 589,
        },
        {
          text: 'Test paragraph',
          type: StoryElementType.Paragraph,
        },
      ],
      tags: ['tag1', 'story-sponsored', 'tag3'],
    };

    const store = createStore();
    store.dispatch({
      payload: StoryViewType.STORY_TRAVEL,
      type: 'settings/setViewType',
    });
    store.dispatch({
      payload: story,
      type: 'story/setStory',
    });
    store.dispatch({
      payload: { enabled: true, feature: 'adServing' },
      type: 'features/setFeatureEnabled',
    });

    const { container } = render(
      <TestWrapper store={store}>
        <ExploreTravelStory />
      </TestWrapper>,
    );

    expect(container.firstChild).toMatchSnapshot();
  });

  // eslint-disable-next-line @stylistic/max-len
  it('render player below the story elements if text to speech feature is enabled', () => {
    // Creating a story with only one paragraph
    const story: StoryState = {
      ...genMockStories({ length: 1 })[0],
      authors: [
        {
          bio: 'Test Bio',
          facebook: '',
          id: 1,
          instagram: '',
          linkedin: '',
          mugshot: '',
          name: 'Test Author',
          position: 'Test Position',
          tiktok: '',
          twitter: '',
          web: '',
          youtube: '',
        },
      ],
      elements: [
        {
          colourMode: 'RGBA',
          cropConfig: {
            cropHeight: 331,
            cropWidth: 589,
            cropX: 0,
            cropY: 1,
            focalX: 295,
            focalY: 167,
            scale: 1,
          },
          description: '',
          height: 333,
          link: '',
          title: '',
          type: StoryElementType.Image,
          // eslint-disable-next-line @stylistic/max-len
          uri: '/vb4YnMcqczPKYQnWpMkavU/901aee49-93ed-45ed-b132-77ffea46780c.png',
          visible: [StoryChannel.Web, StoryChannel.Print],
          width: 589,
        },
        {
          level: '2',
          text: 'Test Heading 2',
          type: StoryElementType.Heading,
        },
        {
          text: 'Single Paragraph',
          type: StoryElementType.Paragraph,
        },
      ],
      isTextToSpeechEnabled: true,
      textToSpeechProjectId: 'a1b2c3d4e5',
    };
    const store = createStore((state) => ({
      ...state,
      accessToken: 'test_token',
      features: {
        ...state.features,
        exploreTravelRecirculation: {
          ...state.features.exploreTravelRecirculation,
          data: {
            latestStoryWidget: {
              limit: 5,
              stories: genMockStories({ length: 5 }),
              storyListId: null,
            },
            mostPopularStoryWidget: {
              limit: 10,
              stories: genMockStories({ length: 10 }),
              storyListId: null,
            },
          },
          enabled: true,
        },
        piano: {
          data: {
            aid: 'test-aid',
            articlePaywallHeadingText: 'Test Heading',
            betaResourceId: 'test-resource',
            completeProfileEnrichments: CompleteProfileEnrichment.NONE,
            ctaVariant: 'default',
            enterpriseSubscriptions: [],
            hasSocialScreen: false,
            header: 'Test Header',
            hideArticleAnnualSavingsPill: false,
            hideSubscriberSignposts: false,
            isAbTesting: false,
            isFullwidthRecommendationContentEnabled: false,
            isPremiumRequest: false,
            isRecommendationContentEnabled: false,
            registrationOnly: false,
            siteId: 'test-site',
            subColour: '#000000',
            subHeader: 'Test Subheader',
            supportAuthServerPaywall: false,
            supportLoginApple: false,
            supportLoginFacebook: false,
            supportLoginGoogle: false,
            supportMonthlyAnnualPaywall: false,
            supportPremiumExtended: false,
            supportPremiumSubscription: false,
            supportPrintBundle: false,
          },
          enabled: true,
        },
      },
      piano: {
        ...state.piano,
        hasAccess: true,
        hasPaywall: false,
        initialized: true,
        loadingPaywall: false,
      },
      settings: {
        ...state.settings,
        host: 'test.com.au',
        viewType: StoryViewType.STORY_TRAVEL,
      },
      story,
    }));

    const { container } = render(
      <TestWrapper store={store}>
        <ExploreTravelStory />
      </TestWrapper>,
    );

    expect(container.firstChild).toMatchSnapshot();
  });

  it('renders with Dailymotion video using explore travel config', () => {
    expect.assertions(3);

    const story: StoryState = {
      ...genMockStories({ length: 1 })[0],
      authors: [],
      elements: [
        {
          description: 'Test Dailymotion video for explore travel',
          embed: 'https://www.dailymotion.com/embed/video/x7abc123',
          service: Service.Dailymotion,
          serviceId: 'x7abc123',
          serviceType: ServiceType.Video,
          title: 'Test Video Title',
          type: StoryElementType.Generic,
        },
        {
          text: 'Test paragraph after video',
          type: StoryElementType.Paragraph,
        },
      ],
    };

    const store = createStore((state) => ({
      ...state,
      accessToken: 'test_token',
      conf: {
        ...state.conf,
        dailymotionPlayerId: 'default-player-id',
        dailymotionPlayerIdForExploreTravelArticles:
          'explore-travel-player-id',
      },
      features: {
        ...state.features,
        exploreTravelRecirculation: {
          ...state.features.exploreTravelRecirculation,
          data: {
            latestStoryWidget: {
              limit: 5,
              stories: genMockStories({ length: 5 }),
              storyListId: null,
            },
            mostPopularStoryWidget: {
              limit: 10,
              stories: genMockStories({ length: 10 }),
              storyListId: null,
            },
          },
          enabled: true,
        },
      },
      settings: {
        ...state.settings,
        host: 'test.com.au',
        viewType: StoryViewType.STORY_TRAVEL,
      },
      story,
    }));

    const { container } = render(
      <TestWrapper store={store}>
        <ExploreTravelStory />
      </TestWrapper>,
    );

    // Verify the store has the correct configuration
    const state = store.getState();
    expect(state.conf.dailymotionPlayerId).toBe('default-player-id');
    expect(state.conf.dailymotionPlayerIdForExploreTravelArticles).toBe(
      'explore-travel-player-id',
    );

    // Verify that the component renders successfully with the video
    expect(container.firstChild).toMatchSnapshot();
  });
});

type NewsletterUtilsMock = typeof newsletterUtils & {
  subSuccessToast: jest.Mock;
  useNewsletterClick: jest.Mock;
  useSubmitNewsletter: jest.Mock;
};

jest.mock('util/mail', () => ({
  fetchMailGroups: jest.fn(),
}));

jest.mock('util/newsletter', () => {
  const actualModule =
    jest.requireActual<typeof newsletterUtils>('util/newsletter');
  return {
    ...actualModule,
    subSuccessToast: jest.fn(),
    useNewsletterClick: jest.fn().mockReturnValue(jest.fn()),
    useSubmitNewsletter: jest.fn().mockReturnValue(jest.fn()),
  } as NewsletterUtilsMock;
});

describe('NewsletterSignup component', () => {
  let store: EnhancedStore<RootState>;

  const mockMailGroupsResponse: MailGroupsApiResponse = {
    defaultInterests: [],
    groups: {},
    isSubscribed: false,
    nationalLists: {},
    requiresManualResubscribe: false,
    subscribedGroups: {},
    subscribedNational: {},
    success: true,
  };

  const mockSubscribedMailGroupsResponse: MailGroupsApiResponse = {
    ...mockMailGroupsResponse,
    subscribedNational: {
      'Explore Travel': true,
    },
  };

  beforeEach(() => {
    jest.clearAllMocks();
    store = createStore((state) => ({
      ...state,
      accessToken: 'test_token',
      piano: {
        ...state.piano,
        hasAccess: true,
        initialized: true,
        user: null,
      },
    }));
  });

  it('renders the newsletter signup correctly', () => {
    expect.assertions(1);
    jest.mocked(fetchMailGroups).mockResolvedValue(mockMailGroupsResponse);
    const { container } = render(
      <TestWrapper store={store}>
        <NewsletterSignup />
      </TestWrapper>,
    );
    expect(container.firstChild).toMatchSnapshot();
  });

  it('renders with onNewswell prop', () => {
    expect.assertions(1);
    jest.mocked(fetchMailGroups).mockResolvedValue(mockMailGroupsResponse);

    const { container } = render(
      <TestWrapper store={store}>
        <NewsletterSignup onNewswell />
      </TestWrapper>,
    );

    expect(container.firstChild).toMatchSnapshot();
  });

  it('does not render newsletter signup when already subscribed', async () => {
    expect.assertions(2);

    jest
      .mocked(fetchMailGroups)
      .mockResolvedValue(mockSubscribedMailGroupsResponse);

    store = createStore((state) => ({
      ...state,
      piano: {
        ...state.piano,
        hasAccess: true,
        initialized: true,
        user: {
          aud: 'test',
          confirmed: true,
          email: '<EMAIL>',
          email_confirmation_required: false,
          exp: 123,
          family_name: 'test',
          firstName: 'test',
          given_name: 'test',
          iat: 123,
          iss: 'test',
          jti: 'test',
          lastName: 'test',
          login_timestamp: 'test',
          r: true,
          sub: 'test',
          uid: 'test',
          valid: true,
        } as PianoLoginUserData,
      },
    }));

    render(
      <TestWrapper store={store}>
        <NewsletterSignup />
      </TestWrapper>,
    );

    await waitFor(() => {
      expect(screen.queryByText('Sign Up')).not.toBeInTheDocument();
    });
  });

  it('handles sign up button click correctly', () => {
    expect.assertions(2);
    const handleClickMock = jest.fn();
    (newsletterUtils.useNewsletterClick as jest.Mock).mockReturnValue(
      handleClickMock,
    );
    jest.mocked(fetchMailGroups).mockResolvedValue(mockMailGroupsResponse);

    render(
      <TestWrapper store={store}>
        <NewsletterSignup />
      </TestWrapper>,
    );

    const signupButton = screen.getByRole('button', { name: /Sign Up/i });
    expect(signupButton).toBeInTheDocument();

    fireEvent.click(signupButton);
    expect(signupButton).toHaveTextContent('Sign Up');
  });
});
