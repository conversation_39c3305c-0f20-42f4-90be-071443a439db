/**
 * List Shared Subscriptions
 */

import { type PhoenixParams, callAuthApi } from './api';

import type {
  PhoenixApiRequest,
  PhoenixSharedSubscriptionInviteRequest,
  PhoenixSharedSubscriptionListRequest,
  PhoenixSharedSubscriptionRemoveRequest,
  PhoenixSharedSubscriptionResendRequest,
} from 'types/phoenix-types/requests';
import type {
  PhoenixApiResponse,
  PhoenixSharedSubscriptionInviteResponse,
  PhoenixSharedSubscriptionListResponse,
  PhoenixSharedSubscriptionRemoveResponse,
  PhoenixSharedSubscriptionResendResponse,
} from 'types/phoenix-types/responses';

export async function listSharedSubscriptions(
  params: PhoenixParams<PhoenixSharedSubscriptionListRequest>,
): Promise<PhoenixApiResponse<PhoenixSharedSubscriptionListResponse>> {
  return callAuthApi<PhoenixApiRequest, PhoenixSharedSubscriptionListResponse>(
    'shared/list',
    params,
  );
}

export async function sendSharedInvite(
  params: PhoenixParams<PhoenixSharedSubscriptionInviteRequest>,
): Promise<PhoenixApiResponse<PhoenixSharedSubscriptionInviteResponse>> {
  return callAuthApi<
    PhoenixApiRequest,
    PhoenixSharedSubscriptionInviteResponse
  >('shared/invite', params);
}

export async function resendSharedInvite(
  params: PhoenixParams<PhoenixSharedSubscriptionResendRequest>,
): Promise<PhoenixApiResponse<PhoenixSharedSubscriptionResendResponse>> {
  return callAuthApi<
    PhoenixApiRequest,
    PhoenixSharedSubscriptionResendResponse
  >('shared/resend', params);
}

export async function removeSharedInvite(
  params: PhoenixParams<PhoenixSharedSubscriptionRemoveRequest>,
): Promise<PhoenixApiResponse<PhoenixSharedSubscriptionRemoveResponse>> {
  return callAuthApi<
    PhoenixApiRequest,
    PhoenixSharedSubscriptionRemoveResponse
  >('shared/remove', params);
}
