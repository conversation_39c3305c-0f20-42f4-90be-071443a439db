import { render } from '@testing-library/react';
import React from 'react';

import { ClassifiedsLayout } from 'store/slices/features';
import { createStore } from 'store/store';
import { TestWrapper } from 'util/jest';

import { ads, categories, children } from './mock';

import Classifieds from '.';

const createMockStore = (withAds: boolean) => {
  /* eslint-disable sort-keys */
  const appStore = createStore((state) => ({
    ...state,
    classifieds: {
      categories: withAds ? [] : categories,
      ad: null,
      ads,
      category: null,
      clusterAds: null,
      isMasonryLayout: true,
      primarySimilarAdsLen: null,
      query: '',
      sideBarAds: null,
      similarAds: null,
      subcategory: null,
      tooltipClicked: false,
    },
    features: {
      ...state.features,
      classifieds: {
        data: {
          layout: ClassifiedsLayout.Masonry,
          showSimilarAds: false,
        },
        enabled: true,
      },
    },
    page: {
      ...state.page,
      children,
      name: '',
      showHeading: true,
    },
    settings: {
      ...state.settings,
      transformUrl: 'https://transform.dev.newsnow.io/',
      host: 'test.com.au',
    },
  }));
  /* eslint-enable sort-keys */
  return appStore;
};

describe('classifieds template', () => {
  it('render results page correctly', () => {
    expect.assertions(1);
    const testStore = createMockStore(true);

    const { container } = render(
      <TestWrapper store={testStore}>
        <Classifieds />
      </TestWrapper>,
    );

    expect(container.firstChild).toMatchSnapshot();
  });

  it('render categories page correctly', () => {
    expect.assertions(1);
    const testStore = createMockStore(false);

    const { container } = render(
      <TestWrapper store={testStore}>
        <Classifieds />
      </TestWrapper>,
    );

    expect(container.firstChild).toMatchSnapshot();
  });
});
