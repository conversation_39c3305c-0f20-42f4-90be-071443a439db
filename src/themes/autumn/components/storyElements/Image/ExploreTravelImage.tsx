import clsx from 'clsx';

import { useAppSelector } from 'store/hooks';
import { StoryChannel } from 'types/Story';
import {
  DeviceType,
  IMG_WIDTHS,
  charsToPixels,
  htmlToText,
} from 'util/device';
import {
  ImageResizeMode,
  getImageAspectClass,
  getTransformParams,
  hasValidURI,
} from 'util/image';

import StoryImage from '../common/StoryImage';
import StoryImageLink from '../common/StoryImageLink';

import type { ImageElement, StoryElementBaseProps } from 'types/Story';

interface Props extends StoryElementBaseProps {
  className?: string;
  element: ImageElement;
}

interface HeightsProps {
  deviceType: DeviceType;
  item: StoryElementBaseProps<ImageElement>;
}

function ExploreTravelImage({
  className,
  element,
  index,
}: Props): React.ReactElement | null {
  const story = useAppSelector((state) => state.story);
  const { cropConfig, visible } = element;

  if (
    (visible && !visible.includes(StoryChannel.Web)) ||
    !hasValidURI(element)
  ) {
    return null;
  }

  const description = element.description || story.title;
  const aspectClass = getImageAspectClass(cropConfig);

  return (
    <div className="mx-auto mb-6 md:my-6">
      <div className={aspectClass}>
        {element.link && (
          <StoryImageLink
            alt={description}
            fitMode={ImageResizeMode.MAX}
            fixedSize="large"
            image={element}
            imageClassName="md:min-w-[980px]"
            link={element.link}
            resize
            sourceFixedSize="exploreTravel"
          />
        )}
        {!element.link && (
          <StoryImage
            alt={description}
            fitMode={ImageResizeMode.MAX}
            fixedSize="large"
            highPriority={index === 0}
            image={element}
            imageClassName="md:min-w-[980px]"
            lazyLoad={index !== 0}
            resize
            sourceFixedSize="exploreTravel"
          />
        )}
      </div>
      {description && (
        <div
          className={clsx(
            'px-4 pt-3 font-inter text-sm font-normal leading-5 text-gray-500 md:px-0',
            className,
          )}
        >
          {description}
        </div>
      )}
    </div>
  );
}

export function calcImageHeights(
  deviceType: DeviceType,
  element: ImageElement,
  description?: string,
): number {
  const { height, width } = element;

  if (!hasValidURI(element)) {
    return 0;
  }

  const { h: heightRest, w: widthRest } = getTransformParams({
    fit: ImageResizeMode.MAX,
    height,
    image: element,
    size: 'large',
    useFocalPoint: false,
    width,
  });
  const { h: heightMobile, w: widthMobile } = getTransformParams({
    fit: ImageResizeMode.MAX,
    height,
    image: element,
    size: 'medium',
    useFocalPoint: false,
    width,
  });

  // Add 2 for chars worth for icon.
  const chars = description ? htmlToText(description).length + 2 : 0;

  const scale =
    IMG_WIDTHS[deviceType] /
    (deviceType === DeviceType.MOBILE ? widthMobile : widthRest);
  const newHeight = Math.round(
    (deviceType === DeviceType.MOBILE ? heightMobile : heightRest) * scale,
  );

  // Add 12px top padding
  const captionHeight = chars
    ? charsToPixels(chars, deviceType, 'caption') + 12
    : 0;
  return newHeight + captionHeight;
}

export function imageHeightsExploreTravel({
  deviceType,
  item: { element },
}: HeightsProps): number {
  const { visible } = element;
  if (visible && !visible.includes(StoryChannel.Web)) {
    return 0;
  }
  const store = window.getStore();
  const { story } = store.getState();
  const description = element.description || story.title;

  return calcImageHeights(deviceType, element, description);
}

export default ExploreTravelImage;
