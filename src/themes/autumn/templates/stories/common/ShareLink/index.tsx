import Link from 'themes/autumn/components/generic/Link';

interface ShareLinkProps {
  Icon: React.FC<{ className?: string }>;
  href: string;
  onClick?: () => void;
  text: string;
}

const ShareLink = ({ Icon, href, onClick, text }: ShareLinkProps) => (
  <Link
    className="flex cursor-pointer flex-col items-center justify-center gap-1"
    href={href}
    noStyle
    onClick={onClick}
  >
    <div className="flex size-12 items-center justify-center rounded-full bg-gray-100 p-2 hover:bg-gray-200">
      <Icon />
    </div>
    <span className="font-inter text-xs font-normal text-gray-900">
      {text}
    </span>
  </Link>
);

export default ShareLink;
