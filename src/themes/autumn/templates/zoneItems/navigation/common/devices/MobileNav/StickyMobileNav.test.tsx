import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';

import { createStore } from 'store/store';
import { LabelLogoType } from 'themes/autumn/components/generic/Logo';
import { StoryElementType } from 'types/Story';

import StickyMobileNav from './StickyMobileNav';

jest.mock(
  'themes/autumn/components/stories/Comments/ViafouraTray',
  () =>
    function MockViafouraTray() {
      return <div data-testid="viafoura-tray">Viafoura Tray</div>;
    },
);

describe('StickyMobileNav', () => {
  const createTestStore = (overrides = {}) =>
    createStore((state) => ({
      ...state,
      nav: {
        ...state.nav,
        returnUrl: {
          text: 'Back to News',
          url: '/news',
        },
      },
      settings: {
        ...state.settings,
        viewType: 'story',
      },
      story: {
        ...state.story,
        elements: [
          {
            level: '2',
            text: 'First Heading',
            type: StoryElementType.Heading,
          },
          {
            level: '2',
            text: 'Second Heading',
            type: StoryElementType.Heading,
          },
        ],
        makeH2AnchorLinks: true,
      },
      ...overrides,
    }));

  it('renders correctly in story view with anchor links', () => {
    expect.assertions(2);
    const store = createTestStore();

    const { container } = render(
      <Provider store={store}>
        <StickyMobileNav showSticky />
      </Provider>,
    );

    expect(screen.getByText('In this article')).toBeInTheDocument();
    expect(container.firstChild).toMatchSnapshot();
  });

  it('renders correctly in non-story view', () => {
    expect.assertions(2);
    const store = createTestStore({
      settings: {
        viewType: 'story-travel',
      },
    });

    const { container } = render(
      <Provider store={store}>
        <StickyMobileNav showSticky />
      </Provider>,
    );

    expect(screen.getByText('In this article')).toBeInTheDocument();
    expect(container.firstChild).toMatchSnapshot();
  });

  it('toggles anchor box when clicking the dropdown button', async () => {
    expect.assertions(3);
    const store = createTestStore();

    const { container } = render(
      <Provider store={store}>
        <StickyMobileNav showSticky />
      </Provider>,
    );

    expect(
      screen.queryByTestId('article-anchors-menu'),
    ).not.toBeInTheDocument();
    fireEvent.click(screen.getByText('In this article'));
    expect(container).toMatchSnapshot();
    fireEvent.click(screen.getByText('In this article'));
    await waitFor(() => {
      expect(
        screen.queryByTestId('article-anchors-menu'),
      ).not.toBeInTheDocument();
    });
  });

  it('closes anchor box when clicking an anchor item', () => {
    expect.assertions(1);
    const store = createTestStore();

    const { container } = render(
      <Provider store={store}>
        <StickyMobileNav showSticky />
      </Provider>,
    );

    fireEvent.click(screen.getByText('In this article'));
    expect(container).toMatchSnapshot();
  });

  it('closes anchor box when clicking outside', () => {
    expect.assertions(1);
    const store = createTestStore();

    const { container } = render(
      <Provider store={store}>
        <StickyMobileNav showSticky />
      </Provider>,
    );

    fireEvent.click(screen.getByText('In this article'));
    expect(container).toMatchSnapshot();
    fireEvent.mouseDown(document.body);
  });

  it('renders back link when no heading elements are available', () => {
    expect.assertions(3);
    const store = createTestStore({
      story: {
        elements: [],
        makeH2AnchorLinks: true,
      },
    });

    const { container } = render(
      <Provider store={store}>
        <StickyMobileNav showSticky />
      </Provider>,
    );

    expect(screen.queryByText('In this article')).not.toBeInTheDocument();
    expect(screen.getByText('Back to News')).toBeInTheDocument();
    expect(container.firstChild).toMatchSnapshot();
  });

  it('renders nothing when showSticky is false', () => {
    expect.assertions(1);
    const store = createTestStore();

    const { container } = render(
      <Provider store={store}>
        <StickyMobileNav showSticky={false} />
      </Provider>,
    );

    expect(container.firstChild).toBeNull();
  });

  it('renders with viafoura tray when showViafoura is true', () => {
    expect.assertions(1);

    const store = createTestStore();

    const { container } = render(
      <Provider store={store}>
        <StickyMobileNav showSticky showViafoura />
      </Provider>,
    );

    expect(container.firstChild).toMatchSnapshot();
  });

  it('renders with custom logo type', () => {
    expect.assertions(1);
    const store = createTestStore({
      settings: {
        viewType: 'homepage',
      },
    });

    const { container } = render(
      <Provider store={store}>
        <StickyMobileNav
          labelLogoType={LabelLogoType.LABEL_WITH_SQUARE_LOGO}
          labelOfLogo="Test Label"
          showSticky
        />
      </Provider>,
    );

    expect(container.firstChild).toMatchSnapshot();
  });
});
