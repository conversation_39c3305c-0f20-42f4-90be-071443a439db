import { createSlice } from '@reduxjs/toolkit';

import type { PayloadAction } from '@reduxjs/toolkit';
import type { StoryList } from 'types/ZoneItems';

export interface ChildPage {
  altMenuName: string;
  menuName: string;
  metaDescription: string;
  name: string;
  newWindow: boolean;
  url: string;
}

export interface PageState {
  altMenuName: string;
  cardImage: string | null;
  children: ChildPage[];
  csrftoken?: string;
  doubleClickCat: string;
  hasBonzaiAd: boolean;
  hasGutterAd: boolean;
  hideRevBanner: boolean;
  isGalleryOpen: boolean;
  menuName: string;
  menuVisible: boolean;
  metaDescription: string;
  metaTitle: string;
  name: string;
  newWindow: boolean;
  noIndex: boolean;
  noSnippet: boolean;
  openGraphDescription: string;
  openGraphImage: string;
  openGraphTitle: string;
  openGraphType: string;
  openGraphUrl: string;
  pageId: number;
  showHeading: boolean;
  showHelp: boolean;
  showSiblingsOnChildPages: boolean;
  siblings: {
    altMenuName: string;
    menuName: string;
    name: string;
    newWindow: boolean;
    url: string;
  }[];
  storyList?: StoryList;
  template: string;
  templateSettings: unknown;
  url: string;
}

const initialState: PageState = {
  altMenuName: '',
  cardImage: '',
  children: [],
  csrftoken: undefined,
  doubleClickCat: '',
  hasBonzaiAd: false,
  hasGutterAd: false,
  hideRevBanner: false,
  isGalleryOpen: false,
  menuName: '',
  menuVisible: false,
  metaDescription: '',
  metaTitle: '',
  name: '',
  newWindow: false,
  noIndex: false,
  noSnippet: false,
  openGraphDescription: '',
  openGraphImage: '',
  openGraphTitle: '',
  openGraphType: '',
  openGraphUrl: '',
  pageId: 0,
  showHeading: false,
  showHelp: false,
  showSiblingsOnChildPages: false,
  siblings: [],
  storyList: undefined,
  template: '',
  templateSettings: {},
  url: '',
};

const pageSlice = createSlice({
  initialState,
  name: 'page',
  reducers: {
    setHasBonzaiAd: (state) => {
      state.hasBonzaiAd = true;
    },
    setHasGutterAd: (state) => {
      state.hasGutterAd = true;
    },
    setIsGalleryOpen(state, action: PayloadAction<boolean>) {
      state.isGalleryOpen = action.payload;
    },
    setShowHelp: (state) => {
      state.showHelp = true;
    },
  },
});

export default pageSlice;
