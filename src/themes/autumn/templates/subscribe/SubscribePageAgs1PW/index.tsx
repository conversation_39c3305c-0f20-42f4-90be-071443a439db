import { PianoHook } from 'components/Piano';
import { useAppSelector } from 'store/hooks';
import SubscriptionFooter from 'themes/autumn/components/footer/SubscriptionFooter';
import TemplateWrapper from 'themes/autumn/components/generic/TemplateWrapper';
import SubscriptionNav from 'themes/autumn/components/nav/SubscriptionNav';
import FaqContainerACM from 'themes/autumn/templates/subscribe/common/FaqContainerACM';

import Container from '../../../components/generic/Container';

function SubscribePageAgs1PW(): React.ReactElement | null {
  const pianoFeature = useAppSelector((state) => state.features.piano);
  const pianoHasAccess = useAppSelector((state) => state.piano.hasAccess);
  const pianoInitialized = useAppSelector((state) => state.piano.initialized);
  const staticSiteUrl = useAppSelector(
    (state) => state.settings.staticSiteUrl,
  );

  const subscribeImgPath = `${staticSiteUrl}images/subscribe`;

  if (!pianoFeature.enabled) {
    return null;
  }

  if (pianoInitialized && pianoHasAccess) {
    window.location.href = '/';
  }

  return (
    <TemplateWrapper
      footer={<SubscriptionFooter showLinksOnDesktop />}
      free
      hideSmartBanner
      nav={<SubscriptionNav showContact={false} showHelp={false} showLogin />}
    >
      <div className="relative flex w-full flex-col items-center">
        <picture>
          <source
            media="(max-width: 767px)"
            srcSet={`${subscribeImgPath}/subscribe_banner_mobile.png`}
          />
          <img
            alt="Subscribe Banner"
            className="h-auto w-full"
            src={`${subscribeImgPath}/subscribe_banner.png`}
          />
        </picture>
      </div>
      <div className="px-4">
        <div className="mx-auto max-w-[960px]">
          <Container className="px-1 md:px-6 xl:px-0" noGutter>
            <PianoHook
              className="min-h-[1500px] md:min-h-[730px]"
              id="subscription-select-wrap-fullpage"
            />

            <FaqContainerACM />
          </Container>
        </div>
      </div>
    </TemplateWrapper>
  );
}

export default SubscribePageAgs1PW;
