import { charsToPixels, htmlToText } from 'util/device';

import type { ParagraphElement, StoryElementBaseProps } from 'types/Story';
import type { DeviceType } from 'util/device';

interface HeightsProps {
  deviceType: DeviceType;
  item: StoryElementBaseProps<ParagraphElement>;
}

function ExploreTravelParagraph({
  element: { text },
}: StoryElementBaseProps<ParagraphElement>): React.ReactElement {
  return (
    <div className="md:w-180">
      <p
        className="text-lg font-normal leading-9 text-gray-800 md:text-xl md:leading-8 [&_a]:text-gray-800 [&_a]:underline [&_a]:decoration-gray-500 hover:[&_a]:decoration-gray-800"
        dangerouslySetInnerHTML={{ __html: text }}
      />
    </div>
  );
}

export function paragraphHeightsExploreTravel({
  deviceType,
  item: {
    element: { text },
  },
}: HeightsProps): number {
  const chars = htmlToText(text).length;
  return charsToPixels(chars, deviceType, 'prose');
}

export default ExploreTravelParagraph;
