'use client';

import Script from 'next/script';
import { useEffect, useState } from 'react';
import ReactDOM from 'react-dom';

import { fetchAndSetAdPublisherProvidedIdProc } from 'components/Piano';
import { useAppSelector } from 'store/hooks';
import { RenderMode } from 'store/slices/conf';
import AdBlockingRecovery from 'themes/autumn/components/ads/AdBlockingRecovery';
import AmazonPublisherServices from 'themes/autumn/components/ads/GAM/AmazonPublisherServices';
import {
  DFPManager,
  DFPSlotsProvider,
} from 'themes/autumn/components/ads/GAM/dfp';
import { AdSize, viewportMapping } from 'util/ads';
import { useLazyLoadComponentState } from 'util/hooks';

import type { LazyLoadConfig } from 'themes/autumn/components/ads/GAM/dfp/DFPManager';
import type { CreateAdOptions, Viewport } from 'util/ads';

export const DFP_NETWORK_ID = 21666581298;
export const GPT_URL = 'https://securepubads.g.doubleclick.net/tag/js/gpt.js';

// Potential parameters for A/B testing. One is selected from each set, for
// each page view. They do not persist for subsequent views. We will use this
// to discover which parameters lead to optimal ad performance
const fetchMargins = [0, 100, 200, 300, 400];
const mobileScales = [0, 1, 2, 3, 4];
const renderMargins = [100, 200, 300, 400, 500];

function randomBucket(choices: unknown[]) {
  return Math.floor(Math.random() * choices.length);
}

function useLazyLoading(): Required<LazyLoadConfig> {
  // A lower fetchMarginPercent avoids fetching ad resources too soon. And a
  // lower renderMarginPercent avoids rendering Ad when user is far off
  // seeing it. mobileScaling is a multiplier of fetch and render that
  // applies only in mobile view.
  // Default: 800
  let fetchMarginPercent;
  // Default: 3
  let mobileScaling;
  // Default: 400
  let renderMarginPercent;

  fetchMarginPercent =
    useAppSelector((state) =>
      state.features.adServing.enabled
        ? state.features.adServing.data.fetchMarginPercent
        : null,
    ) ?? 200;
  mobileScaling =
    useAppSelector((state) =>
      state.features.adServing.enabled
        ? state.features.adServing.data.mobileScaling
        : null,
    ) ?? 2;
  renderMarginPercent =
    useAppSelector((state) =>
      state.features.adServing.enabled
        ? state.features.adServing.data.renderMarginPercent
        : null,
    ) ?? 100;

  const enableLazyLoadAbTest = useAppSelector(
    (state) =>
      state.features.adServing.enabled &&
      state.features.adServing.data.enableLazyLoadAbTest,
  );
  const [fmBucket] = useState(randomBucket(fetchMargins));
  const [msBucket] = useState(randomBucket(mobileScales));
  const [rmBucket] = useState(randomBucket(renderMargins));

  if (enableLazyLoadAbTest) {
    fetchMarginPercent = fetchMargins[fmBucket];
    mobileScaling = mobileScales[msBucket];
    renderMarginPercent = renderMargins[rmBucket];
  }

  return { fetchMarginPercent, mobileScaling, renderMarginPercent };
}

export default function AdProvider({ children }: React.PropsWithChildren) {
  const mode = useAppSelector((state) => state.conf.mode);
  const indexExchangeId = useAppSelector(
    (state) => state.conf.indexExchangeId,
  );
  const adServing = useAppSelector((state) => state.features.adServing);
  const pianoEnabled = useAppSelector((state) => state.features.piano.enabled);
  const allowAdsForPremium = useLazyLoadComponentState().showComponent;
  const disableInitialLoad =
    adServing.enabled && !!adServing.data.apsPublisherId;
  const shouldRequestAds = useAppSelector(
    (state) =>
      state.features.adServing.enabled &&
      (!state.features.adServing.data.apsPublisherId ||
        state.adServing.isAmazonPublisherServicesReady) &&
      (!pianoEnabled || allowAdsForPremium),
  );
  const adLazyLoadGPTEnabled = useAppSelector(
    (state) =>
      state.features.adLazyLoadGpt.enabled &&
      state.features.adServing.enabled &&
      // Try restricting this to the home page for now, as we prefer to
      // prioritise Piano on story and subscribe pages
      state.settings.viewType === 'homepage',
  );
  const lazyLoad = useLazyLoading();
  let dfpNetworkId = `${DFP_NETWORK_ID}`;

  if (
    // eslint-disable-next-line rulesdir/no-typeof-window-outside-useeffect
    typeof window !== 'undefined' &&
    window.location.search.includes('adcallkw=')
  ) {
    dfpNetworkId += '/onl.adtester';
  }

  useEffect(() => {
    if (!pianoEnabled) {
      fetchAndSetAdPublisherProvidedIdProc('anonymous');
    }
  }, [pianoEnabled]);

  useEffect(() => {
    if (disableInitialLoad && shouldRequestAds) {
      DFPManager.refresh();
    }
  }, [disableInitialLoad, shouldRequestAds]);

  if (!adServing.enabled) {
    return children;
  }

  if (adLazyLoadGPTEnabled) {
    ReactDOM.preload(GPT_URL, { as: 'script' });
  }

  return (
    <>
      {adServing.enabled && !adLazyLoadGPTEnabled && (
        <Script async src={GPT_URL} />
      )}
      {allowAdsForPremium && <AmazonPublisherServices />}
      {indexExchangeId && (
        <Script
          async
          src={`https://js-sec.indexww.com/ht/p/${indexExchangeId}.js`}
          // Must load before DFP
        />
      )}
      <DFPSlotsProvider
        dfpNetworkId={dfpNetworkId}
        disableInitialLoad={disableInitialLoad}
        lazyLoad={lazyLoad}
      >
        {children}
      </DFPSlotsProvider>
      {mode !== RenderMode.EDIT && <AdBlockingRecovery />}
    </>
  );
}

AdProvider.createAd = function createAd({
  sizes,
  slotId,
  targetingArguments = {},
}: CreateAdOptions) {
  const store = window.getStore();
  const { adServing } = store.getState().features;

  if (!adServing.enabled) {
    return;
  }

  const cat = adServing.data.doubleClickCat || 'home';
  const adUnitParts = [DFP_NETWORK_ID, adServing.data.doubleClickSite, cat];
  const adUnitString = `/${adUnitParts.join('/')}`;
  const size: googletag.GeneralSize = AdSize.mobileBanner;
  googletag.cmd.push(() => {
    const slots = googletag
      .pubads()
      .getSlots()
      .filter((slot) => slot.getSlotElementId() === slotId);
    googletag.destroySlots(slots);
    const mapping = googletag.sizeMapping();

    Object.entries(sizes).forEach(([viewport, viewportSize]) => {
      mapping.addSize(viewportMapping[viewport as Viewport], viewportSize);
    });

    const slot = googletag
      .defineSlot(adUnitString, size, slotId)
      ?.defineSizeMapping(mapping.build())
      .addService(googletag.pubads());

    if (!slot) {
      return;
    }

    Object.entries(targetingArguments).forEach(([key, value]) => {
      slot?.setTargeting(key, value);
    });

    googletag.display(slotId);
    googletag.pubads().refresh([slot]);
  });
};

AdProvider.setTargetingArguments = function setTargetingArguments(
  kv: Record<string, string>,
) {
  DFPManager.setTargetingArguments(kv);
};
