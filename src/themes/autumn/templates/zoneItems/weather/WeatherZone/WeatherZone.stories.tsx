import { createStore } from 'store/store';
import { TestWrapper } from 'util/jest';

import Component from '.';

import type { Meta, StoryObj } from '@storybook/react';

const meta: Meta<typeof Component> = {
  component: Component,
  title: 'Weather/Page',
};

export default meta;

type Story = StoryObj<typeof Component>;

export const Default: Story = {
  render: () => (
    <TestWrapper
      store={createStore((state) => ({
        ...state,
        conf: {
          ...state.conf,
          location: 'Griffith, NSW',
        },
        racetracks: {
          ...state.racetracks,
          sochiUrl: 'https://sochi-stag.stag.newsnow.io/',
        },
      }))}
    >
      <Component />
    </TestWrapper>
  ),
};
