import clsx from 'clsx';
import { forwardRef } from 'react';

import { useAppSelector } from 'store/hooks';
import { RenderMode } from 'store/slices/conf';
import { getClassNamesForSizes } from 'util/ads';
import { useLazyLoadComponentState } from 'util/hooks';

import styles from './styles.module.css';

import type { AdSizeType } from 'util/ads';

export interface Props {
  children: React.ReactNode;
  className?: string;
  forwardedRef?: React.Ref<HTMLDivElement>;
  isBonzaiTruSkin?: boolean;
  isScrollX: boolean;
  isScrollXAdSlot?: boolean;
  labelPosition?: string;
  // Tailwind spacing classes - eg. space-y-2
  labelSpace?: string;
  lgSizes?: AdSizeType;
  mdSizes?: AdSizeType;
  // Tailwind padding classes - eg. py-2
  placeholderPadding?: string;
  sizeRendered: [number, number];
  sizeRenderedPlaceholder: [number, number];
  sizes: AdSizeType;
  slotId: string;
  // `true` or `top-n` classname for custom top offset
  sticky?: boolean | string;
  stickyOnTablet?: boolean;
  supportPortal?: boolean;
  withLabel?: boolean;
  withPlaceholder?: boolean;
  withPlaceholderBackground?: boolean;
  xlSizes?: AdSizeType;
}

const ALLOWED_ADS_IN_PREMIUM_SUBSCRIPTION = ['polar'];

export function AdPlaceholder(
  {
    children,
    className,
    forwardedRef,
    isBonzaiTruSkin,
    isScrollX,
    isScrollXAdSlot = false,
    labelPosition = 'top',
    labelSpace = 'space-y-2',
    lgSizes,
    mdSizes,
    placeholderPadding = 'py-2',
    sizeRendered,
    sizeRenderedPlaceholder,
    sizes,
    slotId,
    sticky,
    stickyOnTablet: stickOnTablet,
    supportPortal,
    withLabel = true,
    withPlaceholder = true,
    withPlaceholderBackground = true,
    xlSizes,
  }: Props,
  ref: React.ForwardedRef<HTMLDivElement>,
): React.ReactElement | null {
  const mode = useAppSelector((state) => state.conf.mode);
  const { showComponent } = useLazyLoadComponentState();

  const hideAds =
    !showComponent && !ALLOWED_ADS_IN_PREMIUM_SUBSCRIPTION.includes(slotId);

  const { ad: adClassNames, container: containerClassNames } =
    getClassNamesForSizes(sizes, mdSizes, lgSizes, xlSizes, isScrollX);

  const label = (
    <p className="text-center font-sans text-xxs font-medium uppercase leading-2 text-gray-400">
      Advertisement
    </p>
  );

  return (
    <div
      className={
        isBonzaiTruSkin
          ? 'hidden'
          : clsx(
              className,
              containerClassNames,
              sticky === true ? 'top-0' : sticky,
              // space between ad and label. Use tw classes - e.g. space-y-2
              labelSpace,
              {
                'bg-gray-100': withPlaceholderBackground,
                // Space between gray container and ad
                [`${stickOnTablet ? 'md:sticky' : 'lg:sticky'} z-10`]: sticky,
                [placeholderPadding]:
                  withPlaceholderBackground && placeholderPadding !== '',
              },
            )
      }
      id={`${slotId}-container`}
      ref={ref}
    >
      {withLabel && !isScrollX && labelPosition === 'top' && label}
      <div
        className={clsx(
          'items-center justify-center',
          // Do not apply height rules on Bonzai TruSkin ads
          isBonzaiTruSkin || adClassNames,
          {
            [styles.portalAd]: supportPortal,
            flex: !isScrollX,
            'overflow-hidden': !isScrollX || !isBonzaiTruSkin,
          },
        )}
        style={{
          // adjust the placeholder background size
          height:
            isScrollXAdSlot ||
            isScrollX ||
            isBonzaiTruSkin ||
            sizeRenderedPlaceholder[1] === 0
              ? undefined
              : `${sizeRenderedPlaceholder[1]}px`,
        }}
      >
        {withPlaceholder && !isScrollX && (
          <span className="absolute rounded-sm border border-gray-400 px-1 text-center font-sans text-xxs leading-4 text-gray-400">
            Ad
          </span>
        )}
        <div
          className={clsx({
            // Portal ads, if present, must not be `position: relative`
            'relative overflow-hidden':
              !isScrollX && !supportPortal && !isScrollXAdSlot,
            [styles.portalAdPosition]: supportPortal,
          })}
          ref={forwardedRef}
          style={
            // Only set the dimensions if they are available. Not all ads
            // provide sizes on render
            !isScrollXAdSlot && !isScrollX && sizeRendered.every(Boolean)
              ? {
                  // Needed to have ads vertically centered inside container.
                  // Container height will be maximum height of Sizes for that
                  // breakpoint
                  height: `${sizeRendered[1]}px`,
                  width: `${sizeRendered[0]}px`,
                }
              : undefined
          }
        >
          {mode !== RenderMode.EDIT && !hideAds && children}
        </div>
      </div>
      {withLabel && labelPosition === 'bottom' && label}
    </div>
  );
}

export default forwardRef(AdPlaceholder);
