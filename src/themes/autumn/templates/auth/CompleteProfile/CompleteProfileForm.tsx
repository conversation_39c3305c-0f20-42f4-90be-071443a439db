import clsx from 'clsx';
import { useCallback, useEffect, useRef, useState } from 'react';

import { getPianoReady } from 'components/Piano/ready';
import { useAppSelector } from 'store/hooks';
import Button from 'themes/autumn/components/generic/Button';
import {
  AuthField,
  authErrorToast,
  getQueryValue,
  redirectToLogin,
} from 'util/auth';
import { sendToGtm } from 'util/gtm';
import { useOnce } from 'util/hooks';

import ValidatedTextInput from './inputs/ValidatedTextInput';
import { refreshTokenAndRedirect, submitProfile } from './util/auth';
import { scrollToField, trimFields, validateFields } from './util/fields';

import type { PianoExtendedUserCallbackData } from 'types/Piano';
import type { PhoenixApiError } from 'types/phoenix-types/responses';

export type CompleteProfileValidatedFields = Exclude<
  AuthField,
  | AuthField.POSTCODE
  | AuthField.CONFIRM_PASSWORD
  | AuthField.REGIONAL_BIRTH_YEAR
>;

export const CUSTOM_FIELD_MAP: Record<string, CompleteProfileValidatedFields> =
  {
    ba_zip: AuthField.DELIVERY_POSTCODE,
    billing_address: AuthField.BILLING_ADDRESS,
    billing_postcode: AuthField.BILLING_POSTCODE,
    billing_state: AuthField.BILLING_STATE,
    billing_suburb: AuthField.BILLING_SUBURB,
    'contact-number': AuthField.CONTACT_NUMBER,
    postcode: AuthField.DELIVERY_POSTCODE,
    send_address: AuthField.DELIVERY_ADDRESS,
    send_postcode: AuthField.DELIVERY_POSTCODE,
    send_state: AuthField.DELIVERY_STATE,
    send_suburb: AuthField.DELIVERY_SUBURB,
  };

interface CompleteProfileFormProps {
  addressRequired?: boolean;
  agsPropertyProduce?: string;
  children: React.ReactNode;
  extendedProfile: PianoExtendedUserCallbackData | undefined;
  fields: Record<AuthField, string>;
  nextScreen: (requiresLogin: boolean) => void;
  setField: (field: CompleteProfileValidatedFields, value: string) => void;
  showAlpaBusiness: boolean;
  showBillingAddressToggle?: boolean;
  supportsEnrichment: boolean;
}

export default function CompleteProfileForm({
  addressRequired,
  agsPropertyProduce,
  children,
  extendedProfile,
  fields,
  nextScreen,
  setField,
  showAlpaBusiness,
  showBillingAddressToggle,
  supportsEnrichment,
}: CompleteProfileFormProps) {
  const [errors, setErrors] = useState<PhoenixApiError[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isAddressRequired, setIsAddressRequired] = useState(false);
  const [isBillingSameAsDelivery, setIsBillingSameAsDelivery] = useState(true);
  const [forceTokenLogin, setForceTokenLogin] = useState(false);

  const pianoInitialized = useAppSelector((state) => state.piano.initialized);
  const user = useAppSelector((state) => state.piano.user);

  const canSetPassword =
    user?.passwordType === 'passwordless' ||
    user?.passwordType === 'passwordExpired';

  const firstNameRef = useRef<HTMLInputElement>(null);

  const onToggleClick = useCallback(() => {
    setIsBillingSameAsDelivery((state) => !state);
  }, [setIsBillingSameAsDelivery]);

  function sendErrorToGtm(label: string) {
    sendToGtm({
      action: 'continue_button_click',
      label,
      section: 'step_1',
      trigger: 'complete_new_profile',
    });
  }

  const setAndTrackErrors = useCallback(
    (untrackedErrors: PhoenixApiError[]) => {
      untrackedErrors.forEach((error) => {
        sendErrorToGtm(`server_error:${error.message}`);
      });
      setErrors(untrackedErrors);
    },
    [setErrors],
  );

  const submit = useCallback(async () => {
    const tp = await getPianoReady();
    const accessToken = tp.pianoId.getToken();
    if (!accessToken) {
      sendErrorToGtm('validation_error:session_timed_out');
      redirectToLogin();
      return;
    }

    // Intentionally not trimmed
    const password = fields[AuthField.PASSWORD];
    const trimmedFields = trimFields(fields);

    const validationErrors: PhoenixApiError[] = validateFields({
      canSetPassword,
      fieldValues: trimmedFields,
      fieldsToValidate: [
        AuthField.BILLING_ADDRESS,
        AuthField.BILLING_POSTCODE,
        AuthField.BILLING_STATE,
        AuthField.BILLING_SUBURB,
        AuthField.CONTACT_NUMBER,
        AuthField.DELIVERY_ADDRESS,
        AuthField.DELIVERY_POSTCODE,
        AuthField.DELIVERY_STATE,
        AuthField.DELIVERY_SUBURB,
        AuthField.FIRST_NAME,
        AuthField.LAST_NAME,
        AuthField.PASSWORD,
      ],
      isAddressRequired,
      isBillingSameAsDelivery,
    });

    if (validationErrors.length > 0) {
      setErrors(validationErrors);
      scrollToField(validationErrors[0]?.field);
      sendErrorToGtm(
        `validation_error:${validationErrors.map((e) => e.field).join(',')}`,
      );
      return;
    }

    setIsSubmitting(true);
    try {
      const successful = await submitProfile({
        accessToken,
        agsPropertyProduce,
        canSetPassword,
        fields,
        isBillingSameAsDelivery,
        password,
        setErrors: setAndTrackErrors,
      });

      if (!successful) {
        setIsSubmitting(false);
        return;
      }

      let requiresLogin = false;
      if (canSetPassword && password) {
        // piano's create password api invalidates token for security reasons
        // so we have to generate a new one and use that to re-login user
        setForceTokenLogin(true);
        const tokenRefreshedSuccessfully =
          await refreshTokenAndRedirect(accessToken);
        // If token was not succesfully refreshed, show the login screen.
        requiresLogin = !tokenRefreshedSuccessfully;
      }

      sendToGtm({
        action: 'continue',
        section: 'step_1',
        trigger: 'complete_new_profile',
      });

      nextScreen(requiresLogin);
    } catch (e) {
      console.error(e);
      authErrorToast('Unable to connect to authentication server');
      sendErrorToGtm('validation_error:unable_to_connect');
      setIsSubmitting(false);
    }
  }, [
    isBillingSameAsDelivery,
    fields,
    canSetPassword,
    isAddressRequired,
    setAndTrackErrors,
    agsPropertyProduce,
    nextScreen,
  ]);

  useEffect(() => {
    sendToGtm({
      action: 'profile_stage_impressions',
      section: 'step_1',
      trigger: 'complete_new_profile',
    });
  }, []);

  useEffect(() => {
    firstNameRef.current?.focus();
  }, [firstNameRef]);

  useEffect(() => {
    if (pianoInitialized && !user && !forceTokenLogin) {
      redirectToLogin();
    }
  }, [forceTokenLogin, pianoInitialized, user]);

  useEffect(() => {
    setIsAddressRequired(
      getQueryValue('isPrint') === 'true' || addressRequired || false,
    );
  }, [setIsAddressRequired, addressRequired]);

  useOnce(() => {
    if (pianoInitialized && !!user && !!extendedProfile) {
      setIsLoading(false);
      setField(AuthField.EMAIL, user.email);
      setField(AuthField.FIRST_NAME, user.firstName ?? '');
      setField(AuthField.LAST_NAME, user.lastName ?? '');
      extendedProfile.custom_field_values.forEach((customField) => {
        const fieldName = customField.field_name.toLowerCase();
        const fieldValue = customField.value;

        if (fieldName === 'is_billing_and_send_address_equal') {
          setIsBillingSameAsDelivery(fieldValue === 'true');
        } else {
          const fieldType: AuthField | undefined = CUSTOM_FIELD_MAP[fieldName];
          if (fieldType) {
            setField(fieldType, fieldValue);
          }
        }
      });
      return true;
    }
    return false;
  }, [pianoInitialized, user, setField, extendedProfile]);

  return (
    <div className="w-full max-w-[284px]">
      {supportsEnrichment && (
        <div className="mt-6 text-sm leading-6 text-gray-600">
          Step 1/{showAlpaBusiness ? '3' : '2'}
        </div>
      )}
      <div
        className={clsx('text-xl font-semibold md:text-2xl', {
          'mt-2': supportsEnrichment,
          'mt-7': !supportsEnrichment,
        })}
      >
        Complete profile
      </div>
      <form
        className="w-full"
        onSubmit={(e) => {
          e.preventDefault();
          submit().catch(console.error);
        }}
      >
        <div className="mt-5 flex flex-col gap-y-3">
          {!isLoading && showAlpaBusiness && (
            <ValidatedTextInput
              errors={errors}
              fieldName={AuthField.ALPA_BUSINESS}
              fields={fields}
              name="ALPA Registered Business Name"
              setErrors={setErrors}
              setField={setField}
            />
          )}
          <ValidatedTextInput
            disabled
            errors={errors}
            fieldName={AuthField.EMAIL}
            fields={fields}
            name="Email"
            setErrors={setErrors}
            setField={setField}
            show={!isLoading}
            skeleton
          />
          {canSetPassword && (
            <ValidatedTextInput
              errors={errors}
              fieldName={AuthField.PASSWORD}
              fields={fields}
              name="Password (optional)"
              setErrors={setErrors}
              setField={setField}
              show={!isLoading}
              type="password"
            />
          )}
          <ValidatedTextInput
            errors={errors}
            fieldName={AuthField.FIRST_NAME}
            fields={fields}
            name="First Name"
            ref={firstNameRef}
            setErrors={setErrors}
            setField={setField}
            show={!isLoading}
            skeleton
          />
          <ValidatedTextInput
            errors={errors}
            fieldName={AuthField.LAST_NAME}
            fields={fields}
            name="Last Name"
            setErrors={setErrors}
            setField={setField}
            show={!isLoading}
            skeleton
          />
          <ValidatedTextInput
            errors={errors}
            fieldName={AuthField.CONTACT_NUMBER}
            fields={fields}
            name="Contact Number"
            setErrors={setErrors}
            setField={setField}
            show={!isLoading}
            skeleton
            type="tel"
          />
          {!isAddressRequired ? (
            <ValidatedTextInput
              errors={errors}
              fieldName={AuthField.DELIVERY_POSTCODE}
              fields={fields}
              maxlength={4}
              name="Postcode"
              setErrors={setErrors}
              setField={setField}
              show={!isLoading}
              skeleton
            />
          ) : (
            <>
              <ValidatedTextInput
                errors={errors}
                fieldName={AuthField.DELIVERY_ADDRESS}
                fields={fields}
                name="Delivery Address"
                setErrors={setErrors}
                setField={setField}
                show={!isLoading}
                skeleton
              />
              <ValidatedTextInput
                errors={errors}
                fieldName={AuthField.DELIVERY_SUBURB}
                fields={fields}
                name="Suburb"
                setErrors={setErrors}
                setField={setField}
                show={!isLoading}
                skeleton
              />
              <ValidatedTextInput
                errors={errors}
                fieldName={AuthField.DELIVERY_STATE}
                fields={fields}
                name="State"
                setErrors={setErrors}
                setField={setField}
                show={!isLoading}
                skeleton
              />
              <ValidatedTextInput
                errors={errors}
                fieldName={AuthField.DELIVERY_POSTCODE}
                fields={fields}
                maxlength={4}
                name="Postcode"
                setErrors={setErrors}
                setField={setField}
                show={!isLoading}
                skeleton
              />
              {showBillingAddressToggle && (
                <div>
                  <button
                    className="flex w-full items-center gap-x-3 py-3"
                    onClick={onToggleClick}
                    type="button"
                  >
                    <div className="text-left text-base leading-6 text-gray-600">
                      Use same details for billing address
                    </div>
                    <div
                      className={clsx(
                        'relative h-6 w-[52px] rounded-full transition-all duration-200 ease-in',
                        isBillingSameAsDelivery
                          ? 'bg-green-600'
                          : 'bg-gray-200',
                      )}
                    >
                      <div
                        className={clsx(
                          'absolute top-0.5 flex size-5 items-center justify-center rounded-full bg-white shadow transition-all duration-200 ease-in',
                          isBillingSameAsDelivery ? 'left-5.5' : 'left-0.5',
                        )}
                      />
                    </div>
                  </button>
                </div>
              )}

              {!isBillingSameAsDelivery && (
                <>
                  <ValidatedTextInput
                    errors={errors}
                    fieldName={AuthField.BILLING_ADDRESS}
                    fields={fields}
                    name="Billing Address"
                    setErrors={setErrors}
                    setField={setField}
                    show={!isLoading}
                  />
                  <ValidatedTextInput
                    errors={errors}
                    fieldName={AuthField.BILLING_SUBURB}
                    fields={fields}
                    name="Billing Suburb"
                    setErrors={setErrors}
                    setField={setField}
                    show={!isLoading}
                  />
                  <ValidatedTextInput
                    errors={errors}
                    fieldName={AuthField.BILLING_STATE}
                    fields={fields}
                    name="Billing State"
                    setErrors={setErrors}
                    setField={setField}
                    show={!isLoading}
                  />
                  <ValidatedTextInput
                    errors={errors}
                    fieldName={AuthField.BILLING_POSTCODE}
                    fields={fields}
                    maxlength={4}
                    name="Billing Postcode"
                    setErrors={setErrors}
                    setField={setField}
                    show={!isLoading}
                  />
                </>
              )}
            </>
          )}
          {children}
        </div>
        <Button
          bgColor="bg-red-600 disabled:bg-gray-300"
          className={clsx('mb-8 mt-5', {
            'opacity-0': isLoading,
          })}
          desktopFullWidth
          disabled={isLoading || isSubmitting}
          fontSize="text-sm"
          height="h-10.5 md:h-10.5 lg:h-10.5"
          hoverColor="hover:bg-red-700 disabled:hover:bg-gray-300"
          mobileFullWidth
          text="Continue"
          textColor="text-white"
          type="submit"
        />
      </form>
    </div>
  );
}
