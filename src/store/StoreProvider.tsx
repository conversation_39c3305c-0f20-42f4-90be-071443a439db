'use client';

import { useRef } from 'react';
import { Provider } from 'react-redux';

import { createStore } from './store';

import type { AppStore } from './store';

export default function StoreProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const storeRef = useRef<AppStore>(undefined);
  if (!storeRef.current) {
    // Create the store instance the first time this renders
    storeRef.current = createStore();

    // Using typeof window instead of a useEffect hook to guarantee
    // that it executes before functions that would require this fn
    // to exist for access to the store. Safe as it does not affect
    // the DOM so cannot cause SSR desync
    // eslint-disable-next-line rulesdir/no-typeof-window-outside-useeffect
    if (typeof window !== 'undefined') {
      window.getStore = () => storeRef.current as AppStore;
    }
  }

  return <Provider store={storeRef.current}>{children}</Provider>;
}
