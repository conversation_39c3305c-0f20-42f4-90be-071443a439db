import { faRss } from '@fortawesome/free-solid-svg-icons';
import { useEffect, useState } from 'react';
import { FontAwesomeSvgIcon as FontAwesomeIcon } from 'react-fontawesome-svg-icon';

import BookmarkButton from 'components/Bookmark';
import Link from 'themes/autumn/components/generic/Link';
import { getSocialIcons } from 'themes/autumn/components/stories/AuthorSocials/utils';
import ViafouraFollowButton from 'themes/autumn/components/stories/Comments/ViafouraFollowButton';
import ShareButton from 'themes/autumn/templates/stories/common/ShareButton';
import { UserBookmarkResourceType } from 'types/sepang-types/bookmark';
import { useWindowHref } from 'util/hooks';

import type { StoryAuthor } from 'types/Story';

interface Props {
  author: StoryAuthor;
}

const AuthorSocials: React.FC<Props> = ({ author }) => {
  const socials = getSocialIcons(author);
  const [profileUrl, setProfileUrl] = useState('');
  const fullProfileUrl = useWindowHref({ strip: true });

  useEffect(() => {
    const url = new URL(window.location.href);
    url.search = '';
    url.hash = '';
    const paths = url.pathname.split('/');
    paths.pop();
    url.pathname = paths.join('/');
    setProfileUrl(url.toString());
  }, []);

  return (
    <div className="flex h-32 w-full flex-row px-4 md:mx-auto md:h-18 md:max-w-lg">
      <div className="flex size-full flex-col items-center justify-center gap-4 border-y-1 border-y-gray-200 py-4 md:flex-row md:justify-between">
        <div className="flex flex-row gap-5">
          <Link href={`${profileUrl}/rss.xml`} noStyle>
            <FontAwesomeIcon className="size-6" icon={faRss} />
          </Link>
          {socials.map((social) => (
            <Link
              className="flex items-center justify-center"
              href={`${social.domain}${social.url}`}
              key={social.name}
              noStyle
            >
              {social.Icon ? (
                <social.Icon className="scale-110" />
              ) : (
                <FontAwesomeIcon className="size-6" icon={social.iconName} />
              )}
            </Link>
          ))}
        </div>
        <div className="flex flex-row items-center gap-2">
          <ViafouraFollowButton
            id={author.id.toString()}
            name={author.name}
            type="author"
          />
          <BookmarkButton
            containerClassName="hover:bg-gray-100 py-0 h-9 w-[94px]"
            metadata={{
              description: author.position,
              ...(author.mugshot && { thumbnail: author.mugshot }),
              title: author.name,
              url: fullProfileUrl,
            }}
            resourceId={author.id.toString()}
            resourceType={UserBookmarkResourceType.AUTHOR}
            title="Save"
          />
          <ShareButton
            className="rounded-[100px] border-1 border-gray-300"
            url={fullProfileUrl}
          />
        </div>
      </div>
    </div>
  );
};

export default AuthorSocials;
