/* Fix thick borders due to CSS conflict from Tailwind */
.viafoura .vf-expanding-hover-light::before {
  border-style: none;
}

/* Prevent scrollbar appearing after piano modal closed */
body.tp-modal-close.overflow-hidden {
  overflow: hidden !important;
}

/* enable the piano overlay  */
body .piano-overlay-active {
  display: block;
  background: linear-gradient(transparent, rgb(0, 0, 0));
}

.number-appearance-none::-webkit-inner-spin-button,
.number-appearance-none::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.dailymotion-player-wrapper {
  .dm__pip-close-button {
    position: absolute !important;
    top: var(--dm-close-top, 0px) !important;
    right: var(--dm-close-right, 0) !important;
    width: var(--dm-close-width, 40px) !important;
    height: var(--dm-close-height, 40px) !important;
    background: var(--dm-close-bg, rgba(128, 128, 128, 0.4)) !important;
    opacity: var(--dm-close-opacity, 1) !important;
    border-radius: 50% !important;
    padding: var(--dm-close-padding, 7px) !important;
    border: var(--dm-close-border, 0) !important;
    cursor: pointer;
    visibility: hidden;
    display: none;

    svg {
      width: 100%;
      height: 100%;
    }

    &.dm-player-close-show {
      display: block;
    }
  }
}

#dailymotion-pip-small-viewport,
#dailymotion-pip-large-viewport {
  .dm__pip-close-button {
    visibility: visible;
  }
}

#dailymotion-pip-small-viewport {
  position: fixed !important;
  top: 48px !important;
  left: 0 !important;
  right: 0 !important;
  transform: translateY(0) !important;
  will-change: transform;
}

.block.md\:hidden + #dailymotion-pip-small-viewport {
  --position-top: 48px;
}

.dm-collection-right-side {
  flex-wrap: wrap;
}
