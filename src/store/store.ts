import {
  combineReducers,
  configureStore,
  createSlice,
} from '@reduxjs/toolkit';

import merge from 'util/merge';

import accessTokenSlice from './slices/accessToken';
import adServingSlice from './slices/adServing';
import auctionSlice from './slices/auctions';
import authorSlice from './slices/author';
import classifiedsSlice from './slices/classifieds';
import clusterSlice from './slices/cluster';
import confSlice from './slices/conf';
import editmodeSlice from './slices/editmode';
import featuresSlice from './slices/features';
import googleExtendedAccessSlice from './slices/googleExtendedAccess';
import navSlice from './slices/nav';
import outagesSlice from './slices/outages';
import pageSlice from './slices/page';
import pagesSlice from './slices/pages';
import pianoSlice from './slices/piano';
import racetracksSlice from './slices/racetracks';
import runtimeSlice from './slices/runtime';
import settingsSlice from './slices/settings';
import sportsHubSlice from './slices/sportsHub';
import storySlice from './slices/story';
import tokenInfoSlice from './slices/tokenInfo';
import UgcSlice from './slices/ugc';
import yieldIntelligenceSlice from './slices/yieldIntelligence';
import zoneItemsSlice from './slices/zoneItems';

import type { PayloadAction } from '@reduxjs/toolkit';

const ACTION_HYDRATE = 'HYDRATE';
export function hydrateAction(
  state: Partial<RootState>,
): PayloadAction<Partial<RootState>> {
  return {
    payload: state as RootState,
    type: ACTION_HYDRATE,
  };
}

const combinedReducer = combineReducers({
  accessToken: accessTokenSlice.reducer,
  adServing: adServingSlice.reducer,
  auctions: auctionSlice.reducer,
  author: authorSlice.reducer,
  classifieds: classifiedsSlice.reducer,
  cluster: clusterSlice.reducer,
  conf: confSlice.reducer,
  editmode: editmodeSlice.reducer,
  features: featuresSlice.reducer,
  googleExtendAccess: googleExtendedAccessSlice.reducer,
  nav: navSlice.reducer,
  outages: outagesSlice.reducer,
  page: pageSlice.reducer,
  pages: pagesSlice.reducer,
  piano: pianoSlice.reducer,
  racetracks: racetracksSlice.reducer,
  runtime: runtimeSlice.reducer,
  settings: settingsSlice.reducer,
  sportsHub: sportsHubSlice.reducer,
  story: storySlice.reducer,
  tokenInfo: tokenInfoSlice.reducer,
  ugc: UgcSlice.reducer,
  yieldIntelligence: yieldIntelligenceSlice.reducer,
  zoneItems: zoneItemsSlice.reducer,

  /* eslint-disable sort-keys */
  layoutTheme: createSlice({
    initialState: '',
    name: 'layoutTheme',
    reducers: {},
  }).reducer,

  layoutTemplate: createSlice({
    initialState: '',
    name: 'layoutTemplate',
    reducers: {},
  }).reducer,

  themeDir: createSlice({
    initialState: '',
    name: 'themeDir',
    reducers: {},
  }).reducer,
  /* eslint-enable sort-keys */
});

export const createStore = (configure?: (state: RootState) => RootState) => {
  const store = configureStore({
    reducer: (state: RootState | undefined, action) => {
      if (action.type === ACTION_HYDRATE) {
        return merge(
          state,
          (action as ReturnType<typeof hydrateAction>).payload,
        ) as RootState;
      }
      return combinedReducer(state, action);
    },
  });

  if (configure) {
    store.dispatch(hydrateAction(configure(store.getState())));
  }

  return store;
};

export type AppStore = ReturnType<typeof createStore>;
export type RootState = ReturnType<typeof combinedReducer>;
export type AppDispatch = AppStore['dispatch'];
