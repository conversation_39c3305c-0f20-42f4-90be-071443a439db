'use client';

import React, { useCallback, useEffect, useRef } from 'react';
import TagManager from 'react-gtm-module';

import {
  Ga4EventType,
  ImpressionType,
  InteractionType,
} from 'components/GoogleTagManager';
import { onPianoReady } from 'components/Piano/ready';
import { useAppSelector } from 'store/hooks';
import SocialButton from 'themes/autumn/components/auth/SocialButton';
import Link from 'themes/autumn/components/generic/Link';
import { PhoenixSocialType } from 'types/phoenix-types/responses';
import useAbTest, {
  AbExperiment,
  AbTest,
  useAbExperiment,
} from 'util/ab-tests';
import {
  redirectToLogin,
  redirectToRegister,
  useEnterpriseLogins,
  useSocialCallback,
} from 'util/auth';
import { sendAbTestImpression, sendToGtm, setGtmDataLayer } from 'util/gtm';

import type { SendToGtmProps } from 'util/gtm';

export default function MonthlyPaywallContent() {
  const pianoFeature = useAppSelector((state) => state.features.piano);
  const staticUrl = useAppSelector((state) => state.settings.staticUrl);
  const pianoInitialized = useAppSelector((state) => state.piano.initialized);
  const terms = useAppSelector((state) => state.piano.terms);
  const user = useAppSelector((state) => state.piano.user);

  const [monthlyTerm, annualTerm] = terms;
  const useNewBehaviour = useAbTest(AbTest.PaywallShowDiscount_B);
  const isAbTestActive = useAbExperiment(AbExperiment.PaywallShowDiscount);
  const registrationOnly = useAppSelector(
    (state) =>
      state.features.piano.enabled &&
      state.features.piano.data.registrationOnly,
  );
  const termsHaveLoaded = terms.length > 0;
  const hasMultipleMonthlyPlans = monthlyTerm?.hasMultiplePlans ?? false;
  const isLoggedOut = !pianoInitialized || !user;
  const showPaywall = termsHaveLoaded || registrationOnly;

  let returnUrl = '';
  // eslint-disable-next-line rulesdir/no-typeof-window-outside-useeffect
  if (typeof window !== 'undefined') {
    returnUrl = registrationOnly
      ? window.location.href
      : `/offer/?return=${encodeURIComponent(window.location.href)}`;
  }

  const sendAbTestClick = useCallback(() => {
    if (isAbTestActive)
      TagManager.dataLayer({
        dataLayer: {
          event: 'abtest_click',
        },
      });
  }, [isAbTestActive]);

  const onLoginApple = useSocialCallback(PhoenixSocialType.APPLE, {
    returnURL: () => returnUrl ?? '',
    sendGtmProps: useRef<SendToGtmProps>({
      action: 'article_page',
      label: 'continue_apple_click',
      trigger: 'signup_flow',
    }),
  });

  const onClickLoginApple = useCallback(() => {
    sendAbTestClick();
    onLoginApple();
    setGtmDataLayer({
      event: Ga4EventType.PaywallInteraction,
      interaction_type: InteractionType.ClickLoginApple,
    });
  }, [onLoginApple, sendAbTestClick]);

  const onLoginFacebook = useSocialCallback(PhoenixSocialType.FACEBOOK, {
    returnURL: () => returnUrl ?? '',
    sendGtmProps: useRef<SendToGtmProps>({
      action: 'article_page',
      label: 'continue_facebook_click',
      trigger: 'signup_flow',
    }),
  });

  const onClickLoginFacebook = useCallback(() => {
    sendAbTestClick();
    onLoginFacebook();
    setGtmDataLayer({
      event: Ga4EventType.PaywallInteraction,
      interaction_type: InteractionType.ClickLoginFacebook,
    });
  }, [onLoginFacebook, sendAbTestClick]);

  const onLoginGoogle = useSocialCallback(PhoenixSocialType.GOOGLE, {
    returnURL: () => returnUrl ?? '',
    sendGtmProps: useRef<SendToGtmProps>({
      action: 'article_page',
      label: 'continue_google_click',
      trigger: 'signup_flow',
    }),
  });

  const onClickLoginGoogle = useCallback(() => {
    sendAbTestClick();
    onLoginGoogle();
    setGtmDataLayer({
      event: Ga4EventType.PaywallInteraction,
      interaction_type: InteractionType.ClickLoginGoogle,
    });
  }, [onLoginGoogle, sendAbTestClick]);

  const enterpriseLogins = useEnterpriseLogins({
    onClickCallback: sendAbTestClick,
    returnUrl,
    sendGtmProps: useCallback(
      (name: string) => ({
        action: 'article_page',
        label: `continue_${name}_click`,
        trigger: 'signup_flow',
      }),
      [],
    ),
  });

  const onClickEmail = useCallback(() => {
    sendAbTestClick();
    sendToGtm({
      action: 'article_page',
      label: 'continue_email_click',
      trigger: 'signup_flow',
    });
    setGtmDataLayer({
      event: Ga4EventType.PaywallInteraction,
      interaction_type: InteractionType.ClickEmail,
    });
    redirectToRegister(returnUrl ?? '', {
      paywall: 'true',
    });
  }, [returnUrl, sendAbTestClick]);

  const onClickLogin = useCallback(() => {
    sendToGtm({
      action: 'article_page',
      label: 'continue_login_click',
      trigger: 'signup_flow',
    });
    setGtmDataLayer({
      event: Ga4EventType.PaywallInteraction,
      interaction_type: InteractionType.ClickLogin,
    });

    onPianoReady((tp) => {
      tp.pianoId
        .logout()
        .catch(console.error)
        .finally(() => redirectToLogin());
    });
  }, []);

  const onClickContinue = useCallback(() => {
    sendAbTestClick();
    sendToGtm({
      action: 'article_page',
      label: 'continue_subscribe_click',
      trigger: 'signup_flow',
    });
    window.location.href = `/offer/?return=${encodeURIComponent(
      window.location.href,
    )}`;
  }, [sendAbTestClick]);

  const onSeeOptionsClick = useCallback(() => {
    if (isAbTestActive) {
      sendAbTestClick();
    }
    sendToGtm({
      action: 'article_page',
      label: 'see_subs_offer_link_click',
      trigger: 'signup_flow',
    });
  }, [sendAbTestClick, isAbTestActive]);

  useEffect(() => {
    if (!termsHaveLoaded && isAbTestActive) {
      sendAbTestImpression();
    }
  }, [termsHaveLoaded, isAbTestActive]);

  useEffect(() => {
    if (!showPaywall) {
      return;
    }
    sendToGtm({
      action: 'article_page',
      label: 'paywall_offer_impressions',
      trigger: 'signup_flow',
    });
    setGtmDataLayer({
      event: Ga4EventType.PaywallExpression,
      impression_type: ImpressionType.PaywallOffer,
    });
  }, [showPaywall, annualTerm]);

  if (!pianoFeature.enabled) {
    return null;
  }

  const { supportLoginApple, supportLoginFacebook, supportLoginGoogle } =
    pianoFeature.data;

  const buttons = isLoggedOut ? (
    <>
      <SocialButton
        icon={
          <img
            alt="Google"
            height="21"
            src={`${staticUrl}images/auth/mail.png`}
            width="19"
          />
        }
        onClick={onClickEmail}
        text="Continue with Email"
      />
      {supportLoginGoogle && (
        <SocialButton
          icon={
            <img
              alt="Google"
              height="21"
              src={`${staticUrl}images/auth/google.png`}
              width="19"
            />
          }
          onClick={onClickLoginGoogle}
          text="Continue with Google"
        />
      )}
      {supportLoginApple && (
        <SocialButton
          icon={
            <img
              alt="Apple"
              height="21"
              src={`${staticUrl}images/auth/apple.png`}
              width="17"
            />
          }
          onClick={onClickLoginApple}
          text="Continue with Apple"
        />
      )}
      {supportLoginFacebook && (
        <SocialButton
          icon={
            <img
              alt="Facebook"
              height="19"
              src={`${staticUrl}images/auth/facebook.png`}
              width="19"
            />
          }
          onClick={onClickLoginFacebook}
          text="Continue with Facebook"
        />
      )}
      {enterpriseLogins.map((enterprise) => (
        <SocialButton
          icon={
            <img
              alt={enterprise.name}
              height="21"
              src={`${staticUrl}${enterprise.iconUrl}`}
              width="19"
            />
          }
          key={enterprise.name}
          onClick={enterprise.onClick}
          text={`${enterprise.name} Login`}
        />
      ))}
    </>
  ) : (
    <button
      className="relative rounded bg-red-600 px-3 py-2.5 text-center font-inter text-sm leading-5 text-white shadow-sm hover:border-gray-300 hover:bg-red-700 disabled:bg-gray-300 disabled:hover:bg-gray-300"
      onClick={onClickContinue}
      type="button"
    >
      Continue
    </button>
  );

  const loginOrSignup = !user && (
    <p className="text-sm text-gray-600">
      <button
        className="cursor-pointer underline decoration-gray-400 hover:decoration-gray-600"
        onClick={onClickLogin}
        type="button"
      >
        Login
      </button>{' '}
      or signup to continue reading
    </p>
  );

  const pricingText = monthlyTerm?.firstPlan.format(
    ({ period: firstPeriod, plan: firstPlan, price: firstPrice }) => {
      if (hasMultipleMonthlyPlans) {
        if (firstPlan.price === 0) {
          return (
            <span className="text-2xl font-semibold leading-6">
              Free trial
            </span>
          );
        }
        if (useNewBehaviour) {
          return (
            <>
              <span className="mr-2 text-2xl font-semibold leading-6 text-gray-400 line-through">
                $
                {monthlyTerm?.finalPlan.format(
                  ({ price: finalPrice }) => finalPrice,
                  {
                    truncateUnnecessaryDecimals: true,
                  },
                )}
              </span>
              <span className="text-2xl font-semibold leading-6">
                ${firstPrice}/{firstPeriod}
              </span>
            </>
          );
        }
      }

      return (
        <>
          <span className="text-2xl font-semibold leading-6">
            ${firstPrice}
          </span>
          /{firstPeriod}
        </>
      );
    },
    {
      convertPeriodToSingleFactor: true,
      truncateUnnecessaryDecimals: true,
    },
  );

  const pricingSubText = monthlyTerm?.firstPlan.format(
    ({
      duration: firstDuration,
      endless: firstEndless,
      price: firstPrice,
    }) => {
      if (hasMultipleMonthlyPlans) {
        if (monthlyTerm?.firstPlan.price === 0) {
          const finalBilling = monthlyTerm.finalPlan.format(
            ({ periodFrequency: finalPeriodFrequency, price: finalPrice }) =>
              `Then, billed $${finalPrice} ${finalPeriodFrequency}`,
          );

          return `For the first ${firstDuration}. ${finalBilling}`;
        }
        if (useNewBehaviour) {
          return `Billed as ${firstPrice} for the first ${firstDuration}`;
        }
      }

      return `${
        !firstEndless && `for the first ${firstDuration} `
      }(min cost $${firstPrice})`;
    },
    {
      truncateUnnecessaryDecimals: true,
    },
  );

  return (
    <div className="flex flex-col items-center font-inter">
      <p className="mb-2 text-lg font-medium leading-6 text-gray-900">
        {pricingText}
      </p>
      <p className="mb-7 text-sm font-medium leading-6 text-gray-900">
        {pricingSubText}
      </p>
      {loginOrSignup}
      <div className="mt-5 flex w-80 flex-col gap-y-4">{buttons}</div>
      {!registrationOnly && (
        <Link
          channel="subscribe_button"
          className="pt-6 font-inter text-sm font-semibold text-gray-900 underline underline-offset-4"
          href="/subscribe/"
          noStyle
          onClick={onSeeOptionsClick}
        >
          See subscription options
        </Link>
      )}
    </div>
  );
}
