import { render, screen, waitFor } from '@testing-library/react';

import { createStore } from 'store/store';
import { TestWrapper } from 'util/jest';
import * as sochi from 'util/organization/sochi';
import { keysToCamel } from 'util/string';

import WeatherCard from '.';

import type { FetchWeatherResponse } from 'util/organization/sochi';

const mockData: FetchWeatherResponse = {
  forecasts: [
    {
      date: '2025-03-12',
      day: 0,
      dayName: 'Wednesday',
      frostRisk: 'Nil',
      iconFilename: 'mostly_sunny.png',
      iconPhrase: 'Mostly sunny',
      max: 33,
      min: 20,
      precis: "Mostly sunny. E'ly winds",
      rainProb: 5,
      rainRange: '< 1mm',
    },
    {
      date: '2025-03-13',
      day: 1,
      dayName: 'Thursday',
      frostRisk: 'Nil',
      iconFilename: 'mostly_sunny.png',
      iconPhrase: 'Mostly sunny',
      max: 35,
      min: 20,
      precis: 'Mostly sunny. NW winds tending SW',
      rainProb: 20,
      rainRange: '< 1mm',
    },
    {
      date: '2025-03-14',
      day: 2,
      dayName: 'Friday',
      frostRisk: 'Nil',
      iconFilename: 'mostly_sunny.png',
      iconPhrase: 'Mostly sunny',
      max: 37,
      min: 21,
      precis: 'Mostly sunny. NW winds tending SW',
      rainProb: 5,
      rainRange: '< 1mm',
    },
  ],
};

describe('<WeatherCard />', () => {
  it('renders forecasts', async () => {
    expect.assertions(3);

    const fetchWeatherMock = jest.spyOn(sochi, 'fetchWeather');
    fetchWeatherMock.mockImplementation(
      async () =>
        (await keysToCamel(mockData)) as Record<string, FetchWeatherResponse>,
    );

    const { container } = render(
      <TestWrapper
        store={createStore((state) => ({
          ...state,
          conf: {
            ...state.conf,
            location: 'Griffith, NSW',
          },
        }))}
      >
        <WeatherCard />
      </TestWrapper>,
    );

    await waitFor(() => {
      screen.debug();
      expect(screen.getAllByText('Weather Forecast').length > 0).toBeTruthy();
    });
    expect(fetchWeatherMock).toHaveBeenCalledTimes(1);
    expect(container.firstChild).toMatchSnapshot();
  });
});
