'use client';

import dynamic from 'next/dynamic';

import type { NextResponse } from 'next/server';
import type { ComponentType } from 'react';
import type { RootState } from 'store/store';
import type { PianoIdParameters } from 'types/Piano';

function conditionalRedirect(
  condition: (state: RootState) => boolean,
  url: string | URL = '/',
  status = 302,
) {
  return (ctx: typeof NextResponse, state: RootState) => {
    if (condition(state)) {
      return ctx.redirect(url, { status });
    }

    return undefined;
  };
}

const redirectIfPianoDisabled = conditionalRedirect(
  (state) => !state.features.piano.enabled,
);

export interface TemplateRecord {
  component: ComponentType<unknown>;
  hide?: boolean;
  name: string;
  pianoOptions?: {
    idParams?: PianoIdParameters;
    preventReturnUrl?: boolean;
  };
  preprocessor?: (
    ctx: typeof NextResponse,
    state: RootState,
  ) => NextResponse | void;
  settings?: string;
  zones?: Array<string>;
}

export type TemplateTypeRecord = Record<string, TemplateRecord>;

const templates: Record<string, TemplateTypeRecord> = {
  autumn: {
    '404.html': {
      component: dynamic(
        () => import('themes/autumn/templates/status/Status404'),
        {
          ssr: true,
        },
      ),
      hide: true,
      name: 'Error Page 404',
    },
    'adfreeupgrade.html': {
      component: dynamic(
        () => import('themes/autumn/templates/subscribe/AdFreeUpgrade'),
        {
          ssr: true,
        },
      ),
      name: 'Ad Free Upgrade',
      pianoOptions: {
        idParams: {
          screen: 'login',
        },
        preventReturnUrl: true,
      },
      zones: ['main'],
    },
    'app-landing-page.html': {
      component: dynamic(
        () => import('themes/autumn/templates/applandingpage/Regionals'),
        {
          ssr: true,
        },
      ),
      name: 'App Landing Page',
      preprocessor: conditionalRedirect(
        (state) => !state.features.mobileApp.enabled,
      ),
      settings: 'SubscribePageSettings',
    },
    'app-landing-page-ags.html': {
      component: dynamic(
        () => import('themes/autumn/templates/applandingpage/Ags'),
        {
          ssr: true,
        },
      ),
      name: 'App Landing Page (Ags)',
      preprocessor: conditionalRedirect(
        (state) => !state.features.mobileApp.enabled,
      ),
    },
    'aussie_as_ags_campaign.html': {
      component: dynamic(
        () => import('themes/autumn/templates/campaigns/AussieAsAgsCampaign'),
        { ssr: true },
      ),
      name: 'Aussie As Ags Campaign',
      preprocessor: redirectIfPianoDisabled,
    },
    'autumn-footer.html': {
      component: dynamic(() => import('themes/autumn/templates/Footer'), {
        ssr: true,
      }),
      name: 'Autumn - Footer',
    },
    'autumn-header.html': {
      component: dynamic(() => import('themes/autumn/templates/Header'), {
        ssr: true,
      }),
      name: 'Autumn - Header',
    },
    'autumn-promo-template.html': {
      component: dynamic(
        () => import('themes/autumn/templates/campaigns/AutumnPromoTemplate'),
        { ssr: true },
      ),
      name: 'Autumn Promo Template',
    },
    'beef_week_giveaway.html': {
      component: dynamic(
        () => import('themes/autumn/templates/campaigns/BeefWeekGiveaway'),
        {
          ssr: true,
        },
      ),
      name: 'Beef Week Giveaway',
    },
    'beta_access.html': {
      component: dynamic(() => import('themes/autumn/templates/BetaAccess'), {
        ssr: true,
      }),
      name: 'Beta Access',
    },
    'business/page_business_home.html': {
      component: dynamic(
        () => import('themes/autumn/templates/BusinessHome'),
        { ssr: true },
      ),
      name: 'Business Home',
    },
    'classifieds.html': {
      component: dynamic(() => import('themes/autumn/templates/Classifieds'), {
        ssr: true,
      }),
      name: 'Classifieds',
    },
    'community/contribute.html': {
      component: dynamic(
        () => import('themes/autumn/templates/Ugc/Contribute'),
        {
          ssr: true,
        },
      ),
      name: 'Community Contribute Page',
    },
    'community/index.html': {
      component: dynamic(
        () => import('themes/autumn/templates/index/IndexPageCommunity'),
        {
          ssr: true,
        },
      ),
      name: 'Community Index Page',
      zones: ['main', 'main-side', 'main-top', 'main-top-full-width', 'top'],
    },
    'community/share_event.html': {
      component: dynamic(
        () => import('themes/autumn/templates/Ugc/ShareForms/ShareEvent'),
        {
          ssr: true,
        },
      ),
      name: 'Share Event',
    },
    'community/share_photos.html': {
      component: dynamic(
        () => import('themes/autumn/templates/Ugc/ShareForms/SharePhotos'),
        {
          ssr: true,
        },
      ),
      name: 'Share Photos',
    },
    'community/share_story.html': {
      component: dynamic(
        () => import('themes/autumn/templates/Ugc/ShareForms/ShareStory'),
        {
          ssr: true,
        },
      ),
      name: 'Share Story',
    },
    'community/ugc_detail.html': {
      component: dynamic(
        () => import('themes/autumn/templates/Ugc/UgcDetail'),
        {
          ssr: true,
        },
      ),
      name: 'UGC Detail Page',
      zones: ['main', 'main-side', 'main-top', 'top'],
    },
    'community/ugc_edit.html': {
      component: dynamic(
        () => import('themes/autumn/templates/Ugc/ShareForms/EditForm'),
        {
          ssr: true,
        },
      ),
      name: 'UGC Edit Page',
    },
    'competition_black_opal_stakes.html': {
      component: dynamic(
        () => import('themes/autumn/templates/campaigns/BlackOpalStakes'),
        { ssr: true },
      ),
      name: 'Competition - Black Opal Stakes',
      preprocessor: redirectIfPianoDisabled,
    },
    'complete_profile.html': {
      component: dynamic(
        () => import('themes/autumn/templates/auth/CompleteProfile'),
        { ssr: true },
      ),
      name: 'Complete Profile',
      pianoOptions: {
        idParams: {
          confirmation: 'none',
        },
      },
      preprocessor: redirectIfPianoDisabled,
    },
    'content.html': {
      component: dynamic(() => import('themes/autumn/templates/Content'), {
        ssr: true,
      }),
      name: 'Content',
      zones: ['main'],
    },
    'content-full-width.html': {
      component: dynamic(
        () => import('themes/autumn/templates/ContentFullWidth'),
        {
          ssr: true,
        },
      ),
      name: 'Content Full Width',
      zones: ['main'],
    },
    'content-two-top-cols.html': {
      component: dynamic(
        () => import('themes/autumn/templates/ContentTwoTopCols'),
        { ssr: true },
      ),
      name: 'Content (Two Top Cols)',
      zones: ['main-top-left', 'main-top-right', 'main'],
    },
    'corporate_group_subscriptions_v2.html': {
      component: dynamic(
        () => import('themes/autumn/templates/subscribe/GroupSubscriptions'),
        {
          ssr: true,
        },
      ),
      name: 'Group Subscriptions',
      preprocessor: redirectIfPianoDisabled,
    },
    'explore_competition.html': {
      component: dynamic(
        () => import('themes/autumn/templates/explore/Competition'),
        {
          ssr: true,
        },
      ),
      name: 'Explore - Competition',
    },
    'explore_destination_page.html': {
      component: dynamic(
        () => import('themes/autumn/templates/explore/DestinationPage'),
        {
          ssr: true,
        },
      ),
      name: 'Explore - Destination Page',
      zones: ['main', 'main-side', 'banner'],
    },
    'explore_destination_page_article.html': {
      component: dynamic(
        () => import('themes/autumn/templates/explore/DestinationArticlePage'),
        {
          ssr: true,
        },
      ),
      name: 'Explore - Destination Article Page',
      zones: ['top', 'main', 'main-side'],
    },
    'explore_destinations.html': {
      component: dynamic(
        () => import('themes/autumn/templates/explore/Destinations'),
        {
          ssr: true,
        },
      ),
      name: 'Explore - Destinations',
    },
    'explore_destinations_australia.html': {
      component: dynamic(
        () => import('themes/autumn/templates/explore/ListingsAustralia'),
        {
          ssr: true,
        },
      ),
      name: 'Explore - Australia Listings',
    },
    'explore_destinations_international.html': {
      component: dynamic(
        () => import('themes/autumn/templates/explore/ListingsInternational'),
        {
          ssr: true,
        },
      ),
      name: 'Explore - International Listings',
    },
    'free_christmas_gift_card.html': {
      component: dynamic(
        () =>
          import('themes/autumn/templates/campaigns/FreeChristmasGiftCard'),
        {
          ssr: true,
        },
      ),
      name: 'Free Christmas Gift Card',
      preprocessor: redirectIfPianoDisabled,
    },
    'free_fuel.html': {
      component: dynamic(
        () => import('themes/autumn/templates/campaigns/FreeFuel'),
        {
          ssr: true,
        },
      ),
      name: 'Free Fuel',
      preprocessor: redirectIfPianoDisabled,
    },
    'free_fuel_non_piano.html': {
      component: dynamic(
        () => import('themes/autumn/templates/campaigns/FreeFuelNonPiano'),
        {
          ssr: true,
        },
      ),
      name: 'Free Fuel Non Piano',
    },
    'free_groceries.html': {
      component: dynamic(
        () => import('themes/autumn/templates/gifts/FreeGroceries'),
        {
          ssr: true,
        },
      ),
      name: 'Free Groceries',
      preprocessor: redirectIfPianoDisabled,
    },
    'index_advertisement.html': {
      component: dynamic(
        () => import('themes/autumn/templates/AdvertiseWithUs'),
        {
          ssr: true,
        },
      ),
      name: 'Advertise With Us',
    },
    'index_echidna.html': {
      component: dynamic(
        () => import('themes/autumn/templates/index/echidna/Index'),
        {
          ssr: true,
        },
      ),
      name: 'Index - The Echidna',
      zones: [
        'bottom',
        'top-left-top',
        'top-left-bottom',
        'top-right',
        'main',
      ],
    },
    'index_exploretravel.html': {
      component: dynamic(
        () => import('themes/autumn/templates/index/explore/Index'),
        {
          ssr: true,
        },
      ),
      name: 'Index - Explore Travel',
      zones: ['main-top', 'main', 'main-side', 'top', 'bottom', 'banner'],
    },
    'index_page.html': {
      component: dynamic(
        () => import('themes/autumn/templates/index/IndexPage'),
        {
          ssr: true,
        },
      ),
      name: 'Index Page',
      zones: ['main', 'main-side', 'main-top', 'top'],
    },
    'index_page_2nd_level.html': {
      component: dynamic(
        () => import('themes/autumn/templates/index/IndexPage2ndLevel'),
        { ssr: true },
      ),
      name: 'Index Page (2nd Level)',
      zones: ['main', 'main-side', 'top'],
    },
    'index_page_2nd_level_explore_travel.html': {
      component: dynamic(
        () =>
          import('themes/autumn/templates/index/explore/IndexPage2ndLevel'),
        {
          ssr: true,
        },
      ),
      name: 'Index Page 2nd Level - Explore Travel',
      zones: ['main', 'main-side'],
    },
    'index_page_2nd_level_sidebar.html': {
      component: dynamic(
        () =>
          import('themes/autumn/templates/index/IndexPage2ndLevelNoSidebar'),
        { ssr: true },
      ),
      name: 'Index Page (2nd Level) no sidebar',
      zones: ['main', 'top'],
    },
    'index_page_aap_press_releases.html': {
      component: dynamic(
        () =>
          import('themes/autumn/templates/index/IndexPageAAPPressReleases'),
        { ssr: true },
      ),
      name: 'Index page aap press releases',
      zones: ['main', 'top'],
    },
    'index_page_author.html': {
      component: dynamic(
        () => import('themes/autumn/templates/index/IndexPageAuthor/v2'),
        { ssr: true },
      ),
      name: 'Index Page With Author Profile',
      zones: ['main', 'main-side', 'top'],
    },
    'index_page_car_expert.html': {
      component: dynamic(
        () => import('themes/autumn/templates/index/IndexPageCarExpert'),
        { ssr: true },
      ),
      name: 'Car Expert Index Page',
      zones: ['main', 'main-side', 'top'],
    },
    'index_page_echidna.html': {
      component: dynamic(
        () => import('themes/autumn/templates/index/echidna/IndexPage'),
        {
          ssr: true,
        },
      ),
      name: 'Index Page - The Echidna',
      zones: ['main', 'main-side', 'top'],
    },
    'index_page_echidna_cartoon.html': {
      component: dynamic(
        () => import('themes/autumn/templates/index/echidna/IndexPageCartoon'),
        {
          ssr: true,
        },
      ),
      name: 'Index Page Cartoons - The Echidna',
      zones: ['main', 'main-side', 'top'],
    },
    'index_page_exploretravel.html': {
      component: dynamic(
        () => import('themes/autumn/templates/index/explore/IndexPage'),
        {
          ssr: true,
        },
      ),
      name: 'Index Page - Explore Travel',
      zones: ['main', 'main-side', 'bottom'],
    },
    'index_page_exploretravel_v2.html': {
      component: dynamic(
        () => import('themes/autumn/templates/index/explore/v2/IndexPage'),
        {
          ssr: true,
        },
      ),
      name: 'Index Page - Explore Travel V2',
      zones: [
        'main-top',
        'main-top-small-width',
        'main',
        'navigation',
        'bottom',
        'right-of-page-breadcrumb',
      ],
    },
    'index_page_newsletters.html': {
      component: dynamic(
        () => import('themes/autumn/templates/index/IndexPageNewsletters'),
        { ssr: true },
      ),
      name: 'Index Page Newsletters',
      preprocessor: conditionalRedirect(
        (state) =>
          !state.features.mail.enabled ||
          !state.features.mail.data.supportNewslettersLandingPage ||
          !state.features.mail.data.newsletters,
        '/my-account/',
      ),
      zones: ['top'],
    },
    'index_page_property.html': {
      component: dynamic(
        () => import('themes/autumn/templates/index/IndexPageProperty'),
        { ssr: true },
      ),
      name: 'Property Index Page',
      zones: ['main', 'main-side', 'top'],
    },
    'index_page_search_page.html': {
      component: dynamic(
        () => import('themes/autumn/templates/index/IndexPageSearch'),
        {
          ssr: true,
        },
      ),
      name: 'Search Index Page',
      zones: ['main', 'top'],
    },
    'index_page_user_bookmarks.html': {
      component: dynamic(
        () => import('themes/autumn/templates/index/IndexPageUserBookmarks'),
        { ssr: true },
      ),
      name: 'Index Page User Bookmarks',
      preprocessor: conditionalRedirect(
        (state) => !state.features.userBookmarks.enabled,
        '/',
      ),
      zones: ['top'],
    },
    'index_page_without_sidebar.html': {
      component: dynamic(
        () => import('themes/autumn/templates/index/IndexPageNoSidebar'),
        { ssr: true },
      ),
      name: 'Index page no sidebar',
      zones: ['main', 'top'],
    },
    'lean_my_account.html': {
      component: dynamic(() => import('themes/autumn/templates/MyAccount'), {
        ssr: true,
      }),
      name: 'My Account',
      preprocessor: redirectIfPianoDisabled,
    },
    'login.html': {
      component: dynamic(() => import('themes/autumn/templates/auth/Login'), {
        ssr: true,
      }),
      name: 'Login',
      pianoOptions: {
        idParams: {
          confirmation: 'none',
        },
      },
      preprocessor: redirectIfPianoDisabled,
      zones: ['main'],
    },
    'magic_link.html': {
      component: dynamic(
        () => import('themes/autumn/templates/auth/MagicLink'),
        {
          ssr: true,
        },
      ),
      name: 'Magic Link',
      pianoOptions: {
        idParams: {
          confirmation: 'none',
        },
      },
      preprocessor: redirectIfPianoDisabled,
    },
    'manage-seats.html': {
      component: dynamic(
        () => import('themes/autumn/templates/auth/ManageSeats'),
        {
          ssr: true,
        },
      ),
      name: 'Manage Seats',
    },
    'monthlyupgrade.html': {
      component: dynamic(
        () => import('themes/autumn/templates/subscribe/MonthlyUpgrade'),
        {
          ssr: true,
        },
      ),
      name: 'Monthly Upgrade',
      pianoOptions: {
        idParams: {
          screen: 'login',
        },
        preventReturnUrl: true,
      },
      zones: ['main'],
    },
    'mop_index.html': {
      component: dynamic(
        () => import('themes/autumn/templates/index/MOPIndex'),
        {
          ssr: true,
        },
      ),
      name: 'Homepage - MOP',
      zones: ['newswell', 'newswell-side', 'main', 'main-side'],
    },
    'newsletters.html': {
      component: dynamic(
        () => import('themes/autumn/templates/auth/Newsletters'),
        { ssr: true },
      ),
      name: 'Newsletters',
      pianoOptions: {
        idParams: {
          confirmation: 'none',
        },
      },
      preprocessor: redirectIfPianoDisabled,
    },
    'obituaries.html': {
      component: dynamic(() => import('themes/autumn/templates/Classifieds'), {
        ssr: true,
      }),
      name: 'Obituaries',
      zones: ['main'],
    },
    'offer.html': {
      component: dynamic(
        () => import('themes/autumn/templates/auth/Paywall/Offer'),
        {
          ssr: true,
        },
      ),
      name: 'Offer',
      pianoOptions: {
        idParams: {
          confirmation: 'none',
        },
      },
      preprocessor: redirectIfPianoDisabled,
    },
    'one_column.html': {
      component: dynamic(() => import('themes/autumn/templates/index/Index'), {
        ssr: true,
      }),
      name: 'One Column',
      zones: ['main', 'main-side', 'main-top', 'newswell', 'newswell-side'],
    },
    'pages/layouts/auctions.html': {
      component: dynamic(
        () => import('themes/autumn/templates/auctions/AuctionsList'),
        {
          ssr: true,
        },
      ),
      hide: true,
      name: 'Auctions List',
    },
    'pages/layouts/auctions-home.html': {
      component: dynamic(
        () => import('themes/autumn/templates/auctions/AuctionsHome'),
        {
          ssr: true,
        },
      ),
      hide: true,
      name: 'Auctions Home',
    },
    'payment.html': {
      component: dynamic(
        () => import('themes/autumn/templates/auth/Payment'),
        {
          ssr: true,
        },
      ),
      name: 'Payment',
      pianoOptions: {
        idParams: {
          confirmation: 'none',
          displayMode: 'modal',
        },
      },
      preprocessor: redirectIfPianoDisabled,
    },
    'piano_term_upgrade.html': {
      component: dynamic(
        () => import('themes/autumn/templates/PianoTermUpgrade'),
        {
          ssr: true,
        },
      ),
      name: 'Piano Term Upgrade',
      preprocessor: redirectIfPianoDisabled,
      zones: ['main'],
    },
    'playhq_match_detail.html': {
      component: dynamic(
        () =>
          import('themes/autumn/templates/index/IndexPagePlayHqMatchDetail'),
        { ssr: true },
      ),
      name: 'PlayHQ Match Detail',
      zones: ['main', 'main-side', 'top'],
    },
    'playhq_match_index.html': {
      component: dynamic(
        () => import('themes/autumn/templates/index/IndexPagePlayHqMatches'),
        { ssr: true },
      ),
      name: 'PlayHQ Matches Index Page',
      zones: ['main', 'main-side', 'top'],
    },
    'playhq_season.html': {
      component: dynamic(
        () => import('themes/autumn/templates/index/IndexPagePlayHqSeason'),
        { ssr: true },
      ),
      name: 'PlayHQ Season',
      zones: ['main', 'main-side', 'top'],
    },
    'promo_survey.html': {
      component: dynamic(
        () => import('themes/autumn/templates/auth/AgsXmasPromoSurvey'),
        { ssr: true },
      ),
      name: 'Ags Xmas Promo Survey',
      pianoOptions: {
        idParams: {
          confirmation: 'none',
        },
      },
      preprocessor: redirectIfPianoDisabled,
    },
    'puzzle_index.html': {
      component: dynamic(
        () => import('themes/autumn/templates/puzzle/PuzzleIndex'),
        { ssr: true },
      ),
      name: 'Puzzle Index',
      zones: ['main'],
    },
    'puzzle_page.html': {
      component: dynamic(
        () => import('themes/autumn/templates/puzzle/PuzzlePage'),
        { ssr: true },
      ),
      name: 'Puzzle Page',
      zones: ['main'],
    },
    'racehorse_campaign.html': {
      component: dynamic(
        () => import('themes/autumn/templates/campaigns/RacehorseCampaign'),
        { ssr: true },
      ),
      name: 'Racehorse Campaign',
      preprocessor: redirectIfPianoDisabled,
    },
    'redeem_shared.html': {
      component: dynamic(
        () => import('themes/autumn/templates/subscribe/shared/RedeemPage'),
        {
          ssr: true,
        },
      ),
      name: 'Redeem Shared Access',
      preprocessor: redirectIfPianoDisabled,
    },
    'register.html': {
      component: dynamic(
        () => import('themes/autumn/templates/auth/Register'),
        {
          ssr: true,
        },
      ),
      name: 'Register',
      pianoOptions: {
        idParams: {
          confirmation: 'none',
        },
      },
      preprocessor: redirectIfPianoDisabled,
      zones: ['main'],
    },
    'reset_password.html': {
      component: dynamic(
        () => import('themes/autumn/templates/auth/ResetPassword'),
        {
          ssr: true,
        },
      ),
      name: 'Reset Password',
      pianoOptions: {
        idParams: {
          confirmation: 'none',
        },
      },
      preprocessor: redirectIfPianoDisabled,
    },
    'share-your-photos.html': {
      component: dynamic(
        () => import('themes/autumn/templates/ShareYourPhotos'),
        {
          ssr: true,
        },
      ),
      name: 'Share your photos',
      zones: ['main', 'top'],
    },
    'sponsor_index.html': {
      component: dynamic(
        () => import('themes/autumn/templates/index/IndexPageSponsor'),
        { ssr: true },
      ),
      name: 'Sponsor Index Page',
      zones: ['newswell', 'newswell-side', 'main', 'main-side', 'top'],
    },
    'sport_index.html': {
      component: dynamic(
        () => import('themes/autumn/templates/index/IndexPageSport'),
        { ssr: true },
      ),
      name: 'Sport Index Page',
      zones: ['newswell', 'newswell-side', 'main', 'main-side'],
    },
    'sport_ladder_index.html': {
      component: dynamic(
        () => import('themes/autumn/templates/index/IndexPageSportLadder'),
        { ssr: true },
      ),
      name: 'Sport Ladder Index Page',
      zones: ['main-top', 'main'],
    },
    'sport_live_index.html': {
      component: dynamic(
        () => import('themes/autumn/templates/index/IndexPageSportLive'),
        { ssr: true },
      ),
      name: 'Sport Live Index Page',
      zones: ['main', 'main-side', 'top'],
    },
    'sport_match_detail.html': {
      component: dynamic(
        () =>
          import('themes/autumn/templates/index/IndexPageSportMatchDetail'),
        { ssr: true },
      ),
      name: 'Sport Match Detail Page',
      zones: ['main', 'main-side', 'top'],
    },
    'sport_match_index.html': {
      component: dynamic(
        () => import('themes/autumn/templates/index/IndexPageSportMatches'),
        { ssr: true },
      ),
      name: 'Sport Matches Index Page',
      zones: ['main', 'main-side', 'top'],
    },
    'spring_promo_landing_page.html': {
      component: dynamic(
        () => import('themes/autumn/templates/campaigns/SpringPromo'),
        { ssr: true },
      ),
      name: 'Spring Promo Landing Page',
      preprocessor: redirectIfPianoDisabled,
    },
    'stories/auction.html': {
      component: dynamic(
        () => import('themes/autumn/templates/auctions/AuctionsDetail'),
        {
          ssr: true,
        },
      ),
      hide: true,
      name: 'Auctions Detail',
    },
    'stories/story.html': {
      component: dynamic(
        () => import('themes/autumn/templates/stories/StorySwitcher'),
        { ssr: true },
      ),
      hide: true,
      name: 'Story',
    },
    'stories/story-specialpub.html': {
      component: dynamic(
        () => import('themes/autumn/templates/stories/StorySwitcher'),
        { ssr: true },
      ),
      hide: true,
      name: 'Story',
    },
    'stories/story-sponsored.html': {
      component: dynamic(
        () => import('themes/autumn/templates/stories/StorySwitcher'),
        { ssr: true },
      ),
      hide: true,
      name: 'Story',
    },
    'stories/video-story.html': {
      component: dynamic(
        () => import('themes/autumn/templates/stories/VideoStory'),
        { ssr: true },
      ),
      hide: true,
      name: 'Video Story',
    },
    'subcategory_page_exploretravel_v2.html': {
      component: dynamic(
        () =>
          import('themes/autumn/templates/index/explore/v2/SubcategoryPage'),
        {
          ssr: true,
        },
      ),
      name: 'Subcategory Page - Explore Travel V2',
      zones: [
        'main-top',
        'main-top-small-width',
        'right-of-page-breadcrumb',
        'main',
        'navigation',
        'bottom',
      ],
    },
    'subscribe_fullpage_ags_promo.html': {
      component: dynamic(
        () =>
          import('themes/autumn/templates/subscribe/SubscribePageAgsPromo'),
        {
          ssr: true,
        },
      ),
      name: 'Subscribe Page Ags Promo',
      preprocessor: redirectIfPianoDisabled,
    },
    'subscribe_fullpage_premium.html': {
      component: dynamic(
        () => import('themes/autumn/templates/subscribe/SubscribePagePremium'),
        {
          ssr: true,
        },
      ),
      name: 'Subscribe Premium',
      preprocessor: redirectIfPianoDisabled,
    },
    'subscribe_fullpage_v2.html': {
      component: dynamic(
        () => import('themes/autumn/templates/subscribe/SubscribePageCT'),
        {
          ssr: true,
        },
      ),
      name: 'Subscribe (CT)',
      preprocessor: redirectIfPianoDisabled,
    },
    'subscribe_fullpage_v2_acm.html': {
      component: dynamic(
        () => import('themes/autumn/templates/subscribe/SubscribePage'),
        {
          ssr: true,
        },
      ),
      name: 'Subscribe',
      preprocessor: redirectIfPianoDisabled,
      settings: 'SubscribePageSettings',
    },
    'subscribe_fullpage_v2_act_eurobodaila.html': {
      component: dynamic(
        () =>
          import(
            'themes/autumn/templates/subscribe/SubscribePageACTEurobodaila'
          ),
        {
          ssr: true,
        },
      ),
      name: 'Subscribe (ACT & Eurobodaila)',
      preprocessor: redirectIfPianoDisabled,
    },
    'subscribe_fullpage_v2_ags.html': {
      component: dynamic(
        () => import('themes/autumn/templates/subscribe/SubscribePageAgs'),
        {
          ssr: true,
        },
      ),
      name: 'Subscribe (Ags)',
      preprocessor: redirectIfPianoDisabled,
    },
    'subscribe_fullpage_v2_ags_1pw.html': {
      component: dynamic(
        () => import('themes/autumn/templates/subscribe/SubscribePageAgs1PW'),
        {
          ssr: true,
        },
      ),
      name: 'Subscribe (Ags) $1 per week',
      preprocessor: redirectIfPianoDisabled,
    },
    'subscribe_fullpage_v2_ags_app.html': {
      component: dynamic(
        () => import('themes/autumn/templates/subscribe/SubscribePageAgsApp'),
        {
          ssr: true,
        },
      ),
      name: 'Subscribe Page App (Ags)',
      preprocessor: redirectIfPianoDisabled,
    },

    'subscribe_fullpage_v2_ags_durotanker_giveaway.html': {
      component: dynamic(
        () =>
          import(
            'themes/autumn/templates/subscribe/ags/SubscribePageAgsDurotankerGiveaway'
          ),
        {
          ssr: true,
        },
      ),
      name: 'Subscribe Page Ags - Durotanker Giveaway',
      preprocessor: redirectIfPianoDisabled,
    },
    'subscribe_fullpage_v2_ags_febannual_promo.html': {
      component: dynamic(
        () =>
          import(
            'themes/autumn/templates/subscribe/ags/SubscribePageAgsFebAnnualPromo'
          ),
        {
          ssr: true,
        },
      ),
      name: 'Subscribe Page Ags - Feb Annual Promo',
      preprocessor: redirectIfPianoDisabled,
    },
    'subscribe_fullpage_v2_ags_just_a_fiver.html': {
      component: dynamic(
        () =>
          import(
            'themes/autumn/templates/subscribe/ags/SubscribePageAgsJustAFiver'
          ),
        {
          ssr: true,
        },
      ),
      name: 'Subscribe Page Ags - Just A Fiver',
      preprocessor: redirectIfPianoDisabled,
    },
    'subscribe_fullpage_v2_ct_flow.html': {
      component: dynamic(
        () => import('themes/autumn/templates/subscribe/SubscribePageCTFlow'),
        {
          ssr: true,
        },
      ),
      name: 'Subscribe (CT Flow)',
      preprocessor: redirectIfPianoDisabled,
    },
    'subscribe_fullpage_v2_ct_print.html': {
      component: dynamic(
        () => import('themes/autumn/templates/subscribe/SubscribePageCTPrint'),
        {
          ssr: true,
        },
      ),
      name: 'Subscribe (CT Print)',
      preprocessor: redirectIfPianoDisabled,
    },
    subscribe_fullpage_v2_eoss: {
      component: dynamic(
        () => import('themes/autumn/templates/subscribe/SubscribePageEOSS'),
        {
          ssr: true,
        },
      ),
      name: 'Subscribe EOSS',
      preprocessor: redirectIfPianoDisabled,
    },
    'subscribe_fullpage_v2_eoss_ct_flow_.html': {
      component: dynamic(
        () =>
          import('themes/autumn/templates/subscribe/SubscribePageEOSSCTFlow'),
        {
          ssr: true,
        },
      ),
      name: 'Subscribe EOSS (CT Flow)',
      preprocessor: redirectIfPianoDisabled,
    },
    'subscribe_fullpage_v2_eoss_flow_.html': {
      component: dynamic(
        () =>
          import('themes/autumn/templates/subscribe/SubscribePageEOSSFlow'),
        {
          ssr: true,
        },
      ),
      name: 'Subscribe EOSS (Flow)',
      preprocessor: redirectIfPianoDisabled,
    },
    'subscribe_fullpage_v2_flow.html': {
      component: dynamic(
        () => import('themes/autumn/templates/subscribe/SubscribePageFlow'),
        {
          ssr: true,
        },
      ),
      name: 'Subscribe (Flow)',
      preprocessor: redirectIfPianoDisabled,
    },
    'subscribe_gift_subs_acm.html': {
      component: dynamic(
        () => import('themes/autumn/templates/gifts/GiftPage'),
        {
          ssr: true,
        },
      ),
      name: 'Gift Page',
      preprocessor: redirectIfPianoDisabled,
    },
    'subscribe_gift_subs_fathers_day.html': {
      component: dynamic(
        () => import('themes/autumn/templates/gifts/FathersDayGiftPage'),
        {
          ssr: true,
        },
      ),
      name: 'Fathers Day Gift Page',
      preprocessor: redirectIfPianoDisabled,
    },
    'subscribe_gift_subs_mothers_day.html': {
      component: dynamic(
        () => import('themes/autumn/templates/gifts/MothersDayGiftPage'),
        {
          ssr: true,
        },
      ),
      name: 'Mothers Day Gift Page',
      preprocessor: redirectIfPianoDisabled,
    },
    'subscribe_gift_subs_xmas_acm.html': {
      component: dynamic(
        () => import('themes/autumn/templates/gifts/XmasGiftPage'),
        {
          ssr: true,
        },
      ),
      name: 'Christmas Day 2024 Gift Page',
      preprocessor: redirectIfPianoDisabled,
    },
    'subscribe_page_ags_uforce_promo.html': {
      component: dynamic(
        () =>
          import(
            'themes/autumn/templates/subscribe/SubscribePageAgsUforcePromo'
          ),
        {
          ssr: true,
        },
      ),
      name: 'Subscribe Page Ags Uforce Promo',
      preprocessor: redirectIfPianoDisabled,
    },
    'subscribe_page_angus_heifers_comp.html': {
      component: dynamic(
        () =>
          import(
            'themes/autumn/templates/subscribe/SubscribePageAngusHeifersComp'
          ),
        {
          ssr: true,
        },
      ),
      name: 'Subscribe Page Angus Heifers Comp',
      preprocessor: redirectIfPianoDisabled,
    },
    'subscribe_page_beef_week_promo.html': {
      component: dynamic(
        () =>
          import(
            'themes/autumn/templates/subscribe/SubscribePageBeefWeekPromo'
          ),
        {
          ssr: true,
        },
      ),
      name: 'Subscribe Page Beef Week Promo',
      preprocessor: redirectIfPianoDisabled,
    },
    'subscribe_page_black_friday_promo.html': {
      component: dynamic(
        () =>
          import(
            'themes/autumn/templates/subscribe/SubscribePageBlackFridayPromo'
          ),
        {
          ssr: true,
        },
      ),
      name: 'Subscribe Page Black Friday Promo',
      preprocessor: redirectIfPianoDisabled,
    },
    'subscribe_page_black_friday_promo_ct.html': {
      component: dynamic(
        () =>
          import(
            'themes/autumn/templates/subscribe/SubscribePageBlackFridayPromoCT'
          ),
        {
          ssr: true,
        },
      ),
      name: 'Subscribe Page Black Friday Promo (CT)',
      preprocessor: redirectIfPianoDisabled,
    },
    'subscribe_page_black_friday_promo_nch.html': {
      component: dynamic(
        () =>
          import(
            'themes/autumn/templates/subscribe/SubscribePageBlackFridayPromoNCH'
          ),
        {
          ssr: true,
        },
      ),
      name: 'Subscribe Page Black Friday Promo (NCH)',
      preprocessor: redirectIfPianoDisabled,
    },
    'subscribe_page_eofy.html': {
      component: dynamic(
        () => import('themes/autumn/templates/subscribe/SubscribePageEOFY'),
        {
          ssr: true,
        },
      ),
      name: 'Subscribe Page EOFY',
      preprocessor: redirectIfPianoDisabled,
      settings: 'SubscribePageSettings',
    },
    'subscribe_page_eofy_ags.html': {
      component: dynamic(
        () => import('themes/autumn/templates/subscribe/SubscribePageEOFYAgs'),
        {
          ssr: true,
        },
      ),
      name: 'Subscribe Page EOFY (Ags)',
      preprocessor: redirectIfPianoDisabled,
      settings: 'SubscribePageSettings',
    },
    'subscribe_page_eofy_ct.html': {
      component: dynamic(
        () => import('themes/autumn/templates/subscribe/SubscribePageEOFYCT'),
        {
          ssr: true,
        },
      ),
      name: 'Subscribe Page EOFY (CT Flow)',
      preprocessor: redirectIfPianoDisabled,
      settings: 'SubscribePageSettings',
    },
    'subscribe_page_eofy_flow.html': {
      component: dynamic(
        () =>
          import('themes/autumn/templates/subscribe/SubscribePageEOFYFlow'),
        {
          ssr: true,
        },
      ),
      name: 'Subscribe Page EOFY (Flow)',
      preprocessor: redirectIfPianoDisabled,
      settings: 'SubscribePageSettings',
    },
    'subscribe_page_flash_sale.html': {
      component: dynamic(
        () =>
          import('themes/autumn/templates/subscribe/SubscribePageFlashSale'),
        {
          ssr: true,
        },
      ),
      name: 'Subscribe Page Flash Sale',
      preprocessor: redirectIfPianoDisabled,
      settings: 'SubscribePageSettings',
    },
    'subscribe_page_flash_sale_lts.html': {
      component: dynamic(
        () =>
          import(
            'themes/autumn/templates/subscribe/SubscribePageFlashSaleLTS'
          ),
        {
          ssr: true,
        },
      ),
      name: 'Subscribe Page Flash Sale LTS',
      preprocessor: redirectIfPianoDisabled,
      settings: 'SubscribePageSettings',
    },
    'subscribe_page_food_promo.html': {
      component: dynamic(
        () =>
          import('themes/autumn/templates/subscribe/SubscribePageFoodPromo'),
        {
          ssr: true,
        },
      ),
      name: 'Subscribe Page Food Promo',
      preprocessor: redirectIfPianoDisabled,
    },
    'subscribe_page_november_sale_promo.html': {
      component: dynamic(
        () =>
          import(
            'themes/autumn/templates/subscribe/SubscribePageNovemberSalePromo'
          ),
        {
          ssr: true,
        },
      ),
      name: 'Subscribe Page November Sale',
      preprocessor: redirectIfPianoDisabled,
      settings: 'SubscribePageSettings',
    },
    'subscribe_page_spring_promo.html': {
      component: dynamic(
        () =>
          import('themes/autumn/templates/subscribe/SubscribePageSpringPromo'),
        {
          ssr: true,
        },
      ),
      name: 'Subscribe Page Spring Promo',
      preprocessor: redirectIfPianoDisabled,
      settings: 'SubscribePageSettings',
    },
    'subscribe_page_spring_promo_ct_flow.html': {
      component: dynamic(
        () =>
          import(
            'themes/autumn/templates/subscribe/SubscribePageSpringPromoCTFlow'
          ),
        {
          ssr: true,
        },
      ),
      name: 'Subscribe Page Spring Promo (CT Flow)',
      preprocessor: redirectIfPianoDisabled,
    },
    'subscribe_page_spring_promo_flow.html': {
      component: dynamic(
        () =>
          import(
            'themes/autumn/templates/subscribe/SubscribePageSpringPromoFlow'
          ),
        {
          ssr: true,
        },
      ),
      name: 'Subscribe Page Spring Promo (Flow)',
      preprocessor: redirectIfPianoDisabled,
    },
    'subscribe_page_standard_anniversary.html': {
      component: dynamic(
        () =>
          import(
            'themes/autumn/templates/subscribe/SubscribePageStandardAnniversary'
          ),
        {
          ssr: true,
        },
      ),
      name: 'Subscribe Page - Standard 150th Anniversary',
      preprocessor: redirectIfPianoDisabled,
    },
    'subscribe_page_super_car_promo.html': {
      component: dynamic(
        () =>
          import(
            'themes/autumn/templates/subscribe/SubscribePageSuperCarPromo'
          ),
        {
          ssr: true,
        },
      ),
      name: 'Subscribe Page - Super Car Promo',
      preprocessor: redirectIfPianoDisabled,
    },
    'subscribe_page_tas_print_bundle.html': {
      component: dynamic(
        () =>
          import(
            'themes/autumn/templates/subscribe/SubscribePageTASPrintBundle'
          ),
        {
          ssr: true,
        },
      ),
      name: 'Subscribe Page TAS Print Bundle',
      preprocessor: redirectIfPianoDisabled,
    },
    'subscribe_page_tcmf_promo.html': {
      component: dynamic(
        () =>
          import('themes/autumn/templates/subscribe/SubscribePageTCMFPromo'),
        {
          ssr: true,
        },
      ),
      name: 'Subscribe Page Tamworth Country Music Festival Promo',
      preprocessor: redirectIfPianoDisabled,
    },
    'subscribe_page_uci_road_race.html': {
      component: dynamic(
        () =>
          import('themes/autumn/templates/subscribe/SubscribePageUCIRoadRace'),
        {
          ssr: true,
        },
      ),
      name: 'Subscribe Page UCI Road Race',
      preprocessor: redirectIfPianoDisabled,
    },
    'subscribe_page_winter_sports.html': {
      component: dynamic(
        () =>
          import(
            'themes/autumn/templates/subscribe/SubscribePageWinterSports'
          ),
        {
          ssr: true,
        },
      ),
      name: 'Subscribe Page Winter Sports',
      preprocessor: redirectIfPianoDisabled,
      settings: 'SubscribePageSettings',
    },
    'subscribe_page_winter_sports_ct_flow.html': {
      component: dynamic(
        () =>
          import(
            'themes/autumn/templates/subscribe/SubscribePageWinterSportsCT'
          ),
        {
          ssr: true,
        },
      ),
      name: 'Subscribe Page Winter Sports (CT Flow)',
      preprocessor: redirectIfPianoDisabled,
      settings: 'SubscribePageSettings',
    },
    'subscribe_page_winter_sports_flow.html': {
      component: dynamic(
        () =>
          import(
            'themes/autumn/templates/subscribe/SubscribePageWinterSportsFlow'
          ),
        {
          ssr: true,
        },
      ),
      name: 'Subscribe Page Winter Sports (Flow)',
      preprocessor: redirectIfPianoDisabled,
      settings: 'SubscribePageSettings',
    },
    'subscribe_page_xmas_ags.html': {
      component: dynamic(
        () => import('themes/autumn/templates/subscribe/SubscribePageXmasAgs'),
        {
          ssr: true,
        },
      ),
      name: 'Subscribe Page Xmas Ags',
      preprocessor: redirectIfPianoDisabled,
      settings: 'SubscribePageSettings',
    },
    'subscribe_shared_alpa.html': {
      component: dynamic(
        () =>
          import('themes/autumn/templates/subscribe/shared/SubscribePageALPA'),
        {
          ssr: true,
        },
      ),
      name: 'Shared Subscribe Page (ALPA)',
      preprocessor: redirectIfPianoDisabled,
    },
    'subscribe_summer_sale.html': {
      component: dynamic(
        () => import('themes/autumn/templates/subscribe/SummerSalePage'),
        {
          ssr: true,
        },
      ),
      name: 'Subscribe Page Summer Sale',
      preprocessor: redirectIfPianoDisabled,
      settings: 'SubscribePageSettings',
    },
    'text.html': {
      component: dynamic(() => import('themes/autumn/templates/Text'), {
        ssr: true,
      }),
      name: 'Plain text file',
      zones: ['main'],
    },
    'tributes.html': {
      component: dynamic(() => import('themes/autumn/templates/Classifieds'), {
        ssr: true,
      }),
      name: 'Tributes & Funerals',
    },
    'upgradeandsave.html': {
      component: dynamic(
        () => import('themes/autumn/templates/subscribe/UpgradeAndSave'),
        {
          ssr: true,
        },
      ),
      name: 'Upgrade And Save',
      pianoOptions: {
        idParams: {
          screen: 'login',
        },
        preventReturnUrl: true,
      },
    },
    'vertical_homepage_exploretravel.html': {
      component: dynamic(
        () =>
          import('themes/autumn/templates/index/explore/v2/VerticalHomePage'),
        {
          ssr: true,
        },
      ),
      name: 'Homepage - Explore Travel',
      zones: [
        'main-top',
        'main-top-small-width',
        'main',
        'navigation',
        'bottom',
      ],
    },
    'vivaholidays.html': {
      component: dynamic(
        () => import('themes/autumn/templates/campaigns/VivaHolidaysGiveaway'),
        {
          ssr: true,
        },
      ),
      name: 'Viva Holidays Giveaway',
      preprocessor: redirectIfPianoDisabled,
    },
    'winback.html': {
      component: dynamic(
        () => import('themes/autumn/templates/subscribe/WinbackPromo'),
        {
          ssr: true,
        },
      ),
      name: 'Winback Promo',
      pianoOptions: {
        idParams: {
          screen: 'login',
        },
        preventReturnUrl: true,
      },
    },
  },
};

export default templates;
