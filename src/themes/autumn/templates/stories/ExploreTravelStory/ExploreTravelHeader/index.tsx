import clsx from 'clsx';

import { useAppSelector } from 'store/hooks';
import { PageThemeVariant } from 'store/slices/conf';
import PageBreadcrumb from 'themes/autumn/components/page/PageBreadcrumb';
import Signpost, {
  ColorPalette,
} from 'themes/autumn/components/stories/Signpost';
import { useStoryPageTheme, useWindowHref } from 'util/hooks';
import { textTruncateByWords } from 'util/text';

import CommentCount from '../../common/CommentCount';
import SaveButton from '../../common/SaveButton';
import ShareButton from '../../common/ShareButton';

interface Props {
  showCommentCount?: boolean;
}

export default function ExploreTravelHeader({
  showCommentCount = true,
}: Props): React.ReactNode {
  const story = useAppSelector((state) => state.story);
  const url = useWindowHref({ strip: true });
  const storyPageTheme = useStoryPageTheme();

  const [colorPalette, fontFamily, fontWeight] = (() => {
    switch (storyPageTheme) {
      case PageThemeVariant.EXPLORE_TRAVEL:
        return [ColorPalette.PALETTE_EXPLORE, 'font-playfair', 'font-medium'];
      default:
        return [ColorPalette.PALETTE_DEFAULT, 'font-inter', 'font-bold'];
    }
  })();

  return (
    <div className="mb-6 flex flex-col items-center justify-center gap-5 px-4 md:mb-0">
      <div className="relative w-full self-start">
        <PageBreadcrumb />
        <div className="absolute left-1/2 top-0 hidden -translate-x-1/2 md:block">
          <Signpost
            colorPalette={colorPalette}
            publishFrom={story.publishFrom}
            tags={story.tags}
          />
        </div>
      </div>

      <h1
        className={clsx(
          'w-full text-[26px]/[36px] text-gray-900 md:max-w-[980px] md:text-center md:text-5xl/[64px]',
          fontFamily,
          fontWeight,
        )}
      >
        {story.title}
      </h1>
      {story.summaryFull && (
        <p
          className={clsx(
            'line-clamp-4 w-full text-base font-normal text-gray-900 md:max-w-[980px] md:text-center md:text-lg',
            fontFamily,
          )}
          dangerouslySetInnerHTML={{
            __html: textTruncateByWords(story.summaryFull, 334),
          }}
        />
      )}
      <div className="flex w-full flex-row items-center justify-start md:w-fit md:justify-center">
        <Signpost
          colorPalette={ColorPalette.PALETTE_EXPLORE}
          containerClassName="md:hidden flex flex-col justify-center"
          publishFrom={story.publishFrom}
          tags={story.tags}
        />
        <SaveButton />
        <ShareButton url={url} />
        {showCommentCount && <CommentCount story={story} />}
      </div>
    </div>
  );
}
