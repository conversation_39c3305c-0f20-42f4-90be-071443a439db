import bundleAnalyzer from '@next/bundle-analyzer';
import { withSentryConfig } from '@sentry/nextjs';
import nrExternals from 'newrelic/load-externals';
import resolveConfig from 'tailwindcss/resolveConfig';

import tailwindConfig from './tailwind.config';

import type { NextConfig } from 'next';
import type { Configuration } from 'webpack';

const isCompress = process.env.COMPRESS === 'true';
const useSentry = process.env.SENTRY_DSN || process.env.NEXT_PUBLIC_SENTRY_DSN;

const withBundleAnalyzer = bundleAnalyzer({
  enabled: process.env.ANALYZE === 'true',
});

const fullConfig = resolveConfig(tailwindConfig);
const breakpoints = Object.entries(fullConfig.theme.screens).reduce(
  (all, [breakpoint, size]) => ({
    ...all,
    [breakpoint]: parseInt(size.substring(0, size.length - 2), 10),
  }),
  {},
);

let config: NextConfig = {
  assetPrefix: process.env.MONACO_URL ?? `/monaco/${process.env.ENVIRONMENT}/`,
  // Handled by proxy
  compress: isCompress,
  env: {
    tailwindBreakpoints: JSON.stringify(breakpoints),
  },
  eslint: {
    // Already run as a separate build step
    ignoreDuringBuilds: true,
  },
  experimental: {
    turbo: {
      resolveAlias: {
        classnames: 'clsx',
      },
      rules: {
        '*.svg': {
          as: '*.js',
          loaders: ['@svgr/webpack'],
        },
      },
    },
  },
  images: {
    // Prefer svgr loader
    disableStaticImages: true,
  },
  output: 'standalone',
  poweredByHeader: false,
  reactStrictMode: true,
  transpilePackages: [
    '@headlessui/react',
    'next-seo',
    'react-hook-form',
    'react-hotjar',
  ],
  typescript: {
    // Already run as a separate build step
    ignoreBuildErrors: true,
  },
  webpack: (webpackConfig: Configuration) => {
    // Use `clsx` as a faster drop-in replacement for `classnames`
    // Alias `classnames` to `clsx` since it's used by `react-slick`
    /* eslint-disable no-param-reassign */
    webpackConfig.resolve ??= {};
    webpackConfig.resolve.alias ??= {};
    const { alias } = webpackConfig.resolve;
    if (Array.isArray(alias)) {
      throw new Error('Unexpected webpack config');
    }
    alias.classnames = 'clsx';

    webpackConfig.module ??= {};
    webpackConfig.module.rules ??= [];
    /* eslint-enable no-param-reassign */
    webpackConfig.module.rules.push({
      loader: 'ignore-loader',
      test: /\.test\./,
    });

    webpackConfig.module.rules.push({
      test: /\.svg$/i,
      use: '@svgr/webpack',
    });

    nrExternals(webpackConfig);

    return webpackConfig;
  },
} satisfies NextConfig;

config = withBundleAnalyzer(config);

if (useSentry) {
  config = withSentryConfig(config, {
    disableLogger: true,
    telemetry: false,
  });
}

const finalConfig = config;

export default finalConfig;
