'use client';

/*
  Gallery implementation based on `SimpleCarousel` example from
  https://github.com/FormidableLabs/react-swipeable/tree/main/examples
*/
import clsx from 'clsx';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useSwipeable } from 'react-swipeable';

import { useAppSelector } from 'store/hooks';
import AdProvider from 'themes/autumn/components/ads/AdProvider';
import { GalleryItemType, GalleryLayout } from 'types/Gallery';
import { useLazyLoadComponentState } from 'util/hooks';
import { hasValidURI, storyImageUrl } from 'util/image';
import { generateHashString } from 'util/page';

import Ad, { AdSize } from '../../ads/Ad';
import StorySocials from '../../stories/StorySocials';
import { DropDownDirection } from '../DropDown';
import Link from '../Link';
import LoadingSpinner from '../LoadingSpinner';
import ShareButton from '../ShareButton';

import type { GalleryItem } from 'types/Gallery';

enum Direction {
  NEXT = 'next',
  PREVIOUS = 'previous',
}

const cutDescription = (description: string, length = 90) => {
  // Cut text and remove incomplete words
  if (description.length <= length) {
    return description;
  }
  const shortDescription = description.substring(0, length);
  return shortDescription.substring(0, shortDescription.lastIndexOf(' '));
};

interface CTAButtonLinkProps {
  className?: string;
}

function CTAButton({ className = '' }: CTAButtonLinkProps) {
  const photoGalleryFeature = useAppSelector(
    (state) => state.features.photoGallery,
  );

  if (
    !photoGalleryFeature.enabled ||
    !photoGalleryFeature.data.ctaUrl ||
    !photoGalleryFeature.data.ctaSlideButtonText
  )
    return null;

  return (
    <Link
      className={clsx(
        'rounded-md bg-red-600 px-8 py-4 font-inter font-medium text-white hover:bg-red-700',
        className,
      )}
      // eslint-disable-next-line @stylistic/max-len
      href={`${photoGalleryFeature.data.ctaUrl}?referrer=storylink&channel=photo_gallery&cta=start_here`}
      noStyle
    >
      {photoGalleryFeature.data.ctaSlideButtonText}
    </Link>
  );
}

function CTALink({ className = '' }: CTAButtonLinkProps) {
  const photoGalleryFeature = useAppSelector(
    (state) => state.features.photoGallery,
  );
  const communityShareContentEnabled = useAppSelector(
    (state) => state.features.communityShareContent.enabled,
  );

  // override cta config if community/noticeboard is enabled on the site
  if (communityShareContentEnabled) {
    return (
      <Link
        className="mt-6 text-sm text-white"
        href="/notice-board/share-photos/"
        noStyle
      >
        Want to be featured?{' '}
        <span className="font-semibold underline">Send us your photos.</span>
      </Link>
    );
  }

  if (
    !photoGalleryFeature.enabled ||
    !photoGalleryFeature.data.showCta ||
    !photoGalleryFeature.data.ctaUrl ||
    !photoGalleryFeature.data.ctaPersistentButtonText
  )
    return null;

  return (
    <div className={clsx('font-inter text-sm text-white', className)}>
      {photoGalleryFeature.data.ctaPersistentDescription}
      &nbsp;
      <Link
        className="font-semibold underline underline-offset-4"
        // eslint-disable-next-line @stylistic/max-len
        href={`${photoGalleryFeature.data.ctaUrl}?referrer=storylink&channel=photo_gallery&cta=send_photos`}
        noStyle
      >
        {photoGalleryFeature.data.ctaPersistentButtonText}
      </Link>
    </div>
  );
}

interface GalleryItemProps {
  className: string;
  containerWidth: number;
  item: GalleryItem;
  order: number;
  position: string;
  preloadImage?: boolean;
  visible: boolean;
}

function GalleryItemEntry({
  className,
  containerWidth,
  item,
  order,
  position,
  preloadImage = false,
  visible,
}: GalleryItemProps): React.ReactElement | null {
  const { transformUrl } = useAppSelector((state) => state.settings);
  const photoGalleryFeature = useAppSelector(
    (state) => state.features.photoGallery,
  );

  const adRef = useRef<HTMLDivElement>(null);
  const [adInitilzed, setAdInitialized] = useState(false);

  useEffect(() => {
    if (!adInitilzed && visible) {
      // force to load slide Ad slot is visible
      setTimeout(() => {
        if (
          adRef.current &&
          (adRef.current.clientHeight < 10 || adRef.current.clientWidth < 10)
        ) {
          AdProvider.refresh('gallery-slide-mrec');
        }
      }, 1000);
      setAdInitialized(true);
    }

    if (adInitilzed && !visible) {
      setAdInitialized(false);
    }
  }, [adInitilzed, visible]);

  if (!item) {
    return null;
  }

  let content: React.ReactElement | null;

  switch (item.type) {
    case GalleryItemType.VIDEO_YOUTUBE:
      content = (
        <div className="relative z-10 mx-auto flex size-full items-center justify-center lg:items-start">
          <div className="relative w-full pb-[56.25%]">
            <iframe
              allow="autoplay"
              className="absolute left-0 top-0 size-full border-0"
              id={item.id}
              src={`https://www.youtube.com/embed/${item.id}?autoplay=${
                visible ? 1 : 0
              }`}
              title={item.description}
            />
          </div>
        </div>
      );
      break;
    case GalleryItemType.AD:
      // Premium acccount already checked.
      // no need to use useLazyLoadComponentState here
      content = (
        <div className="flex size-full items-center justify-center">
          {visible && (
            <Ad
              forwardedRef={adRef}
              position={1}
              publiftName="gallery-3"
              sizes={AdSize.mrec}
              slotId="gallery-slide-mrec"
              targetingArguments={{ adslot: 'gallery' }}
              withLabel={false}
              withPlaceholderBackground={false}
            />
          )}
        </div>
      );
      break;
    case GalleryItemType.CTA:
      content = (
        <div className="mx-auto flex size-full max-w-mrec flex-col items-center justify-center space-y-6 font-inter text-white">
          <div className="text-center text-2xl font-bold">
            {photoGalleryFeature.enabled &&
              photoGalleryFeature.data.ctaSlideTitle}
          </div>
          <div className="text-center text-base">
            {photoGalleryFeature.enabled &&
              photoGalleryFeature.data.ctaSlideDescription}
          </div>
          <CTAButton />
        </div>
      );
      break;
    default: {
      let transformedUrl: string | undefined;
      if (item.type === GalleryItemType.PHOTO) {
        transformedUrl = encodeURI(item.url);
      } else {
        transformedUrl = hasValidURI(item)
          ? storyImageUrl({
              height: item.height,
              image: item,
              transformUrl,
              useFocalPoint: false,
              width: item.width,
            })
          : undefined;
      }
      content = (
        <>
          <div className="absolute z-20 size-full bg-neutral-900/20 lg:hidden" />
          <div
            className={clsx(
              'relative z-10 mx-auto size-full bg-contain bg-center bg-no-repeat',
            )}
            key={transformedUrl}
            style={{
              backgroundImage:
                preloadImage && transformedUrl
                  ? `url(${transformedUrl})`
                  : undefined,
            }}
          />
        </>
      );
      break;
    }
  }

  const showLoading =
    item.type !== GalleryItemType.AD && item.type !== GalleryItemType.CTA;

  return (
    <div
      className={`shrink-0 basis-full transform-gpu bg-neutral-900 ${className}`}
      style={{ order, transform: position }}
    >
      {containerWidth > 0 && showLoading && (
        <div
          className={clsx(
            'absolute left-1/2 top-1/2 z-0',
            '-ml-8 -mt-8 size-16',
          )}
        >
          <LoadingSpinner />
        </div>
      )}
      {content}
    </div>
  );
}

function refreshGalleryAds() {
  AdProvider.refresh('gallery-top', 'gallery-mrec');
}

function getOrder(index: number, pos: number, numItems: number) {
  // Offset by 1 so that the "first" item in the list
  // is the last item, as item 0 appears to the left
  // of the shown item
  const offset = (index + 1) % numItems;
  let relative = offset - pos;
  if (relative < 0) {
    relative += numItems;
  }
  return relative;
}

interface CloseButtonProps {
  className: string;
  onClickFn: () => void;
}

const CloseButton = ({ className, onClickFn }: CloseButtonProps) => {
  const { staticUrl } = useAppSelector((state) => state.settings);
  return (
    <button className={className} onClick={() => onClickFn()} type="button">
      <img
        alt="close"
        className="size-full"
        draggable="false"
        src={`${staticUrl}/images/photo-gallery/close.svg`}
      />
    </button>
  );
};

interface OpenGalleryProps {
  close: () => void;
  currentSlide: number;
  elements: GalleryItem[];
}

function Gallery({
  close,
  currentSlide,
  elements,
}: OpenGalleryProps): React.ReactElement | null {
  const photoGalleryFeature = useAppSelector(
    (state) => state.features.photoGallery,
  );
  const isWithThumbLayout =
    photoGalleryFeature.enabled &&
    photoGalleryFeature.data.layout === GalleryLayout.V2;

  const url = useAppSelector((state) => state.story.url);

  const [containerWidth, setContainerWidth] = useState(0);

  const [sliding, setSliding] = useState(false);
  const [direction, setDirection] = useState<Direction>(Direction.NEXT);

  const [initialized, setInitialized] = useState(false);
  const galleryTopRef = useRef<HTMLDivElement>(null);
  const { showComponentPlaceholder } = useLazyLoadComponentState();

  const [mobileFullDescription, setMobileFullDescription] = useState(false);

  const indexTableConversion: number[] = [];
  elements.forEach((el, index) => {
    if (el.type !== GalleryItemType.AD) {
      indexTableConversion.push(index);
    }
  });

  const [currentIdx, setCurrentIdx] = useState(0);
  const [currentItem, setCurrentItem] = useState<GalleryItem>(elements[0]);

  const isContentItem =
    currentItem.type !== GalleryItemType.AD &&
    currentItem.type !== GalleryItemType.CTA;

  const shortDescription = isContentItem
    ? cutDescription(currentItem.description)
    : '';
  const hasShortDescription =
    isContentItem &&
    shortDescription.length !== currentItem.description.length;

  const countImageVideoElements = elements.filter(
    (el) => el.type !== GalleryItemType.AD && el.type !== GalleryItemType.CTA,
  ).length;

  const getIndexWithoutAds = (index: number) => {
    // Count Ads displayed before index
    const adCount = elements.filter(
      (el, idx) => el.type === GalleryItemType.AD && idx <= index,
    ).length;
    return index - adCount;
  };

  useEffect(() => {
    if (initialized) return;

    const hashIdx = currentSlide < countImageVideoElements ? currentSlide : 0;

    if (hashIdx !== 0) {
      setCurrentItem(elements[indexTableConversion[hashIdx]]);
      setCurrentIdx(indexTableConversion[hashIdx]);
    }

    // force to load the ads when the gallery is opened
    setTimeout(() => {
      if (galleryTopRef.current && galleryTopRef.current?.clientHeight < 10) {
        refreshGalleryAds();
      }
    }, 1000);
    setInitialized(true);

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [initialized]);

  const slide = useCallback(
    (slideDirection: Direction) => {
      let newIdx;
      if (slideDirection === Direction.NEXT) {
        newIdx = (currentIdx + 1) % elements.length;
      } else {
        newIdx = (currentIdx - 1 + elements.length) % elements.length;
      }

      setCurrentIdx(newIdx);
      setCurrentItem(elements[newIdx]);
      refreshGalleryAds();
      setSliding(true);
      setDirection(slideDirection);
      setTimeout(() => {
        setSliding(false);
      }, 50);
    },
    [elements, currentIdx],
  );

  const slideLeft = useCallback(() => {
    slide(Direction.NEXT);
    setMobileFullDescription(false);
  }, [slide]);

  const slideRight = useCallback(() => {
    slide(Direction.PREVIOUS);
    setMobileFullDescription(false);
  }, [slide]);

  useEffect(() => {
    if (window.location && initialized && !isWithThumbLayout) {
      window.history.replaceState(
        null,
        '',
        currentIdx === 0
          ? window.location.hash.replace(/(&?slide=\d+&?)/, '')
          : generateHashString({
              slide: getIndexWithoutAds(currentIdx).toString(),
            }),
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentIdx, initialized]);

  const swipeHandlers = useSwipeable({
    onSwipedLeft: slideLeft,
    onSwipedRight: slideRight,
  });

  useEffect(() => {
    function onKeyDown(e: KeyboardEvent) {
      switch (e.key) {
        case 'ArrowLeft':
          slideRight();
          break;
        case 'ArrowRight':
          slideLeft();
          break;
        case 'Escape':
          close();
          break;
        default:
          break;
      }
    }
    document.addEventListener('keydown', onKeyDown);
    return () => {
      document.removeEventListener('keydown', onKeyDown);
    };
  }, [close, slide, slideLeft, slideRight]);

  const calcTranslate = () => {
    if (!sliding) return 'translateX(calc(-100%))';
    if (direction === Direction.PREVIOUS)
      return 'translateX(calc(2 * (-100%)))';
    return 'translateX(0%)';
  };

  const galleryControls = (
    <div className="z-20 flex h-16 w-full items-center justify-center bg-neutral-800 px-4 py-2 lg:relative">
      <div className="flex w-full flex-row items-center justify-between text-white">
        <button
          aria-label="Next"
          className="flex size-16 items-center justify-center"
          onClick={() => slideRight()}
          type="button"
        >
          <svg
            className="fill-current"
            height="48"
            viewBox="0 0 48 48"
            width="48"
          >
            <path d="M28.59 36L31.41 33.18L22.25 24L31.41 14.82L28.59 12L16.59 24L28.59 36Z" />
          </svg>
        </button>
        <div className="font-inter text-lg">
          {currentIdx + 1} of {elements.length}
        </div>
        <button
          aria-label="Previous"
          className="flex size-16 items-center justify-center"
          onClick={() => slideLeft()}
          type="button"
        >
          <svg
            className="fill-current"
            height="48"
            viewBox="0 0 48 48"
            width="48"
          >
            <path d="M19.41 12L16.59 14.82L25.75 24L16.59 33.18L19.41 36L31.41 24L19.41 12Z" />
          </svg>
        </button>
      </div>
    </div>
  );

  const galleryArrows = (
    <div className="absolute top-1/2 z-30 -mt-6 flex h-12 w-full items-center justify-center lg:hidden">
      <div className="flex w-full flex-row justify-between text-white">
        <button
          aria-label="Next"
          className="flex size-12 items-center justify-center"
          onClick={() => slideRight()}
          type="button"
        >
          <svg fill="none" height="26" viewBox="0 0 14 26" width="14">
            <path
              d="M13 24.6668L1.33333 13.0002L13 1.3335"
              stroke="white"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
            />
          </svg>
        </button>
        <button
          aria-label="Previous"
          className="flex size-12 items-center justify-center"
          onClick={() => slideLeft()}
          type="button"
        >
          <svg fill="none" height="26" viewBox="0 0 14 26" width="14">
            <path
              d="M0.999999 1.33317L12.6667 12.9998L1 24.6665"
              stroke="white"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
            />
          </svg>
        </button>
      </div>
    </div>
  );

  return (
    <div className="fixed left-0 top-0 z-50 flex size-full flex-col overflow-y-auto bg-neutral-900">
      {/* Close btn Desktop */}
      <CloseButton
        className="right-3 top-3 size-11 self-end lg:absolute lg:block lg:p-0"
        onClickFn={close}
      />

      <div className="mx-auto mt-2.5 flex size-full grid-cols-1 flex-col space-y-4 lg:max-w-lg">
        {/* Top Ad */}
        {showComponentPlaceholder && (
          <div className="mx-auto w-full">
            <Ad
              forwardedRef={galleryTopRef}
              mdSizes={AdSize.leaderboard}
              position={1}
              publiftName="gallery-1"
              sizes={AdSize.mobileBanner}
              slotId="gallery-top"
              targetingArguments={{ adslot: 'gallery' }}
              withLabel={false}
              withPlaceholderBackground={false}
            />
          </div>
        )}

        {initialized && (
          <div className="flex grow flex-col lg:flex-row lg:space-x-4 lg:px-6">
            {/* Gallery */}
            <div className="flex size-full flex-col lg:overflow-y-hidden">
              <div className="relative flex size-full flex-col overflow-hidden">
                {galleryArrows}

                {/* Gallery Images */}
                <div
                  className="size-full lg:mb-0"
                  ref={(ref) => {
                    setContainerWidth(ref?.clientWidth ?? 0);
                  }}
                >
                  <div
                    className="flex size-full"
                    // eslint-disable-next-line react/jsx-props-no-spreading
                    {...swipeHandlers}
                  >
                    {elements.map((item, index) => (
                      <GalleryItemEntry
                        className={clsx({ 'transition-all': !sliding })}
                        containerWidth={containerWidth}
                        item={item}
                        // eslint-disable-next-line react/no-array-index-key
                        key={index}
                        order={getOrder(index, currentIdx, elements.length)}
                        position={
                          elements.length === 1 ? 'none' : calcTranslate()
                        }
                        preloadImage={
                          // Preload last when current is the first one
                          (currentIdx === 0 &&
                            index === elements.length - 1) ||
                          // Preload First when current image is last one
                          (currentIdx === elements.length - 1 &&
                            index === 0) ||
                          // Preload 1 next and 1 prev image
                          Math.abs(index - currentIdx) <= 1
                        }
                        visible={currentIdx === index}
                      />
                    ))}
                  </div>
                </div>
              </div>
            </div>
            <div className="flex flex-col justify-center">
              <div className="mt-4 flex h-48 shrink-0 grow-0 flex-col font-inter text-sm lg:mt-0 lg:h-auto lg:min-w-mrec">
                <div
                  className={clsx(
                    'flex items-center px-4 text-white lg:hidden',
                    {
                      hidden: mobileFullDescription,
                      'justify-between': isContentItem,
                      'mt-px h-9 justify-end': !isContentItem,
                    },
                  )}
                >
                  {isContentItem && (
                    <ShareButton dropDownDirection={DropDownDirection.UP}>
                      <div className="flex items-center gap-2 rounded border border-white px-4 py-2 hover:bg-neutral-800">
                        <span className="font-normal">Share</span>
                        <svg
                          fill="none"
                          height="14"
                          viewBox="0 0 16 14"
                          width="16"
                        >
                          <path
                            d="M9.66667 4.08333V0.75L15.5 6.58333L9.66667 12.4167V9C5.5 9 2.58333 10.3333 0.5 13.25C1.33333 9.08333 3.83333 4.91667 9.66667 4.08333Z"
                            fill="white"
                          />
                        </svg>
                      </div>
                    </ShareButton>
                  )}

                  <div className="font-inter">
                    {currentIdx + 1} of {elements.length}
                  </div>
                </div>

                {/* Description */}
                {isContentItem && (
                  <>
                    <div
                      className={clsx(
                        'flex grow items-center justify-center',
                        {
                          'lg:hidden': !currentItem.description,
                          'lg:mb-6': !!currentItem.description,
                        },
                      )}
                    >
                      <button
                        className={clsx(
                          'relative mx-auto flex w-full max-w-md focus:outline-0 focus:ring-0 active:outline-0 active:ring-0 lg:max-w-mrec lg:text-left',
                          {
                            'pointer-events-none': !hasShortDescription,
                          },
                        )}
                        onClick={() =>
                          hasShortDescription &&
                          setMobileFullDescription(!mobileFullDescription)
                        }
                        type="button"
                      >
                        <div
                          className={clsx(
                            'mx-auto px-4 text-white lg:mx-0 lg:px-0',
                          )}
                        >
                          {mobileFullDescription
                            ? cutDescription(currentItem.description, 300)
                            : shortDescription}
                          {!mobileFullDescription &&
                            shortDescription.length !==
                              currentItem.description.length && (
                              <>
                                ...{' '}
                                <span className="font-semibold underline underline-offset-4">
                                  Read more
                                </span>
                              </>
                            )}
                          {mobileFullDescription && (
                            <div className="mt-3 font-semibold underline underline-offset-4">
                              Read less
                            </div>
                          )}
                        </div>
                      </button>
                    </div>

                    <div
                      className={clsx(
                        'mx-auto flex grow-0 items-end justify-between px-2 pb-10 lg:hidden',
                        {
                          hidden: mobileFullDescription,
                        },
                      )}
                    >
                      <CTALink />
                    </div>
                  </>
                )}

                {!mobileFullDescription && (
                  <div className="hidden flex-col lg:flex">
                    <div className="mb-2 font-inter font-semibold text-white">
                      Share this gallery
                    </div>
                    {isWithThumbLayout ? (
                      <StorySocials className="inline-flex" url={url} />
                    ) : (
                      <StorySocials
                        className="inline-flex"
                        url={`${url}${generateHashString({
                          slide: getIndexWithoutAds(currentIdx).toString(),
                        })}`}
                      />
                    )}
                    <CTALink className="mt-6" />
                  </div>
                )}
              </div>
              <div className="mt-6 hidden lg:block">
                {showComponentPlaceholder && (
                  <div className="w-full lg:shrink-0">
                    <Ad
                      mdSizes={AdSize.mrec}
                      position={1}
                      publiftName="gallery-2"
                      sizes={[]}
                      slotId="gallery-mrec"
                      targetingArguments={{ adslot: 'gallery' }}
                      withLabel={false}
                      withPlaceholderBackground={false}
                    />
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        <div className="hidden flex-row lg:flex">
          {/* Gallery Controls */}
          {elements.length > 1 && <>{galleryControls}</>}
        </div>
      </div>
    </div>
  );
}

export default Gallery;
