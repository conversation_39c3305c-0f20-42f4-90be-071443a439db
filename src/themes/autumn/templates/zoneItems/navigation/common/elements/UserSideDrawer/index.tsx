'use client';

import {
  faChevronRight,
  faExternalLinkAlt,
} from '@fortawesome/free-solid-svg-icons';
import { XMarkIcon } from '@heroicons/react/24/outline';
import clsx from 'clsx';
import { useEffect, useRef } from 'react';
import { FontAwesomeSvgIcon as FontAwesomeIcon } from 'react-fontawesome-svg-icon';

import { pianoLogout } from 'components/Piano';
import { useAppSelector } from 'store/hooks';
import Link from 'themes/autumn/components/generic/Link';
import { BookmarkThumbnail } from 'themes/autumn/templates/index/IndexPageUserBookmarks/components/Bookmark';
import { setGtmDataLayer } from 'util/gtm';
import { getTimeBasedGreeting, useDate } from 'util/time';

import { useSideDrawer } from './SideDrawerProvider';

import type { ImageElement } from 'types/Story';

interface UserSideDrawerProps {
  children?: React.ReactNode;
}

export default function UserSideDrawer({
  children,
}: UserSideDrawerProps): React.ReactElement {
  const { user } = useAppSelector((state) => state.piano);
  const date = useDate();
  const userBookmarksEnabled = useAppSelector(
    (state) => state.features.userBookmarks.enabled,
  );
  const { bookmarks, isOpen, toggleSideDrawer } = useSideDrawer();
  const currentSiteId = useAppSelector((state) => state.settings.siteId);
  const menuRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const hasScrollbar = document.body.scrollHeight > window.innerHeight;
    const classes = hasScrollbar
      ? ['fixed', 'overflow-y-scroll', 'left-0', 'right-0']
      : ['overflow-hidden'];
    const top = document.documentElement.scrollTop;

    if (isOpen) {
      document.body.classList.add(...classes);
      document.body.style.top = `-${top}px`;
      menuRef.current?.focus();
    }

    return () => {
      if (isOpen) {
        document.body.classList.remove(...classes);
        document.body.style.top = '';
        window.scrollTo({
          top,
        });
      }
    };
  }, [isOpen]);

  const handleBackdropInteraction = () => {
    toggleSideDrawer();
  };

  return (
    <>
      <div
        aria-hidden="true"
        className={clsx(
          'fixed inset-0 z-[9998] !ml-0 size-full text-left transition-all',
          {
            'block bg-black opacity-50': isOpen,
            hidden: !isOpen,
          },
        )}
        onClick={handleBackdropInteraction}
        onKeyDown={(e) => {
          if (e.key === 'Escape') {
            handleBackdropInteraction();
          }
        }}
        ref={menuRef}
        role="presentation"
        tabIndex={-1}
      />
      <div className="relative">
        <nav
          aria-label="Side Drawer"
          className={clsx(
            'fixed right-0 top-0 z-[9999] h-full w-4/5 overflow-y-auto bg-white p-4 transition-all duration-200 ease-in-out will-change-transform md:w-80 lg:w-80',
            {
              'translate-x-0 shadow-2xl': isOpen,
              'translate-x-full': !isOpen,
            },
          )}
          data-testid="user-side-drawer"
        >
          <div className="mb-3 flex items-center justify-between border-b border-gray-300 py-1">
            <span
              className="grow overflow-hidden truncate whitespace-nowrap font-inter text-xs font-semibold text-gray-900"
              title={user?.email}
            >
              {user?.email}
            </span>
            <button
              aria-label="Close"
              className="ml-3 flex size-10 grow-0 flex-col items-end justify-center rounded bg-white transition-colors duration-600 ease-default hover:bg-gray-100 md:ml-6"
              onClick={() => toggleSideDrawer()}
              type="button"
            >
              <XMarkIcon className="size-6" />
            </button>
          </div>
          <h2 className="mt-8 font-merriweather text-xl font-normal leading-6 text-gray-900">
            {getTimeBasedGreeting(date.getHours())}, {user?.given_name}
          </h2>
          <h3 className="mt-8 font-inter text-sm font-bold uppercase text-gray-900">
            Your Content
          </h3>
          <ol className="mt-1 divide-y-1 divide-gray-300">
            <li className="flex items-center hover:underline">
              <Link
                className="flex w-full items-center justify-between py-4 font-inter text-sm font-medium"
                href="/newsletters/"
                noStyle
                onClick={() => {
                  setGtmDataLayer({
                    data: {
                      label: 'newsletters',
                      section: 'your_content',
                    },
                    event: 'profile_interaction',
                  });
                }}
              >
                Newsletters
                <FontAwesomeIcon icon={faChevronRight} />
              </Link>
            </li>
            {userBookmarksEnabled && (
              <li className="py-4">
                <Link
                  className="flex w-full items-center justify-between font-inter text-sm font-medium hover:underline"
                  href="/saved/"
                  noStyle
                  onClick={() => {
                    setGtmDataLayer({
                      data: {
                        label: 'my_saved_list',
                        section: 'your_content',
                      },
                      event: 'profile_interaction',
                    });
                  }}
                >
                  My saved List
                  {!bookmarks?.length && (
                    <FontAwesomeIcon icon={faChevronRight} />
                  )}
                </Link>
                <ol className="mt-2 divide-y divide-gray-300">
                  {bookmarks?.map((bookmark) => {
                    const isExternal =
                      bookmark.metadata?.siteId !== currentSiteId;
                    const handleLinkClick = () => {
                      setGtmDataLayer({
                        data: {
                          label: 'bookmark_link',
                          section: 'your_content',
                        },
                        event: 'profile_interaction',
                      });
                    };
                    return (
                      <li
                        className="flex items-center gap-x-2 py-2 hover:underline"
                        key={bookmark.resourceTypeAndId}
                      >
                        <Link
                          className="line-clamp-2 grow font-merriweather text-sm font-bold leading-6 text-gray-900"
                          href={bookmark.metadata?.url}
                          noStyle
                          onClick={handleLinkClick}
                          target={isExternal ? '_blank' : undefined}
                        >
                          {isExternal && (
                            <FontAwesomeIcon
                              className="mr-2"
                              icon={faExternalLinkAlt}
                              size="xs"
                            />
                          )}
                          {bookmark.metadata?.title}
                        </Link>
                        <div className="size-12 shrink-0 overflow-hidden rounded-md">
                          <BookmarkThumbnail
                            height={48}
                            onClick={handleLinkClick}
                            thumbnail={
                              bookmark.metadata?.thumbnail as
                                | string
                                | ImageElement
                            }
                            title={bookmark.metadata?.title}
                            url={bookmark.metadata?.url}
                            width={48}
                          />
                        </div>
                      </li>
                    );
                  })}
                </ol>
                {bookmarks?.length > 0 && (
                  <Link
                    className="mt-3 block w-full rounded border border-gray-900 p-2 text-center font-inter text-sm font-medium hover:border-gray-300 hover:shadow-md"
                    href="/saved/"
                    noStyle
                    onClick={() => {
                      setGtmDataLayer({
                        data: {
                          label: 'all_saved_list',
                          section: 'your_content',
                        },
                        event: 'profile_interaction',
                      });
                    }}
                  >
                    See all saved
                  </Link>
                )}
              </li>
            )}
          </ol>
          <h3 className="mt-8 border-b border-gray-300 pb-2 font-inter text-sm font-bold uppercase text-gray-900">
            Account
          </h3>
          <ol className="mt-1 divide-y-1 divide-gray-300">
            <li>
              <Link
                className="flex w-full items-center justify-between py-4 font-inter text-sm font-medium hover:underline"
                href="/my-account/"
                noStyle
                onClick={() => {
                  setGtmDataLayer({
                    data: {
                      label: 'my_account',
                      section: 'account_support',
                    },
                    event: 'profile_interaction',
                  });
                }}
              >
                My Account
                <FontAwesomeIcon icon={faChevronRight} />
              </Link>
            </li>
          </ol>
          <button
            className="mt-8 font-inter text-sm font-semibold leading-4 text-gray-900 underline"
            onClick={() => {
              pianoLogout();
              setGtmDataLayer({
                data: {
                  label: 'logout',
                  section: 'account_support',
                },
                event: 'profile_interaction',
              });
              toggleSideDrawer();
            }}
            type="button"
          >
            Logout
          </button>
          {children}
        </nav>
      </div>
    </>
  );
}
