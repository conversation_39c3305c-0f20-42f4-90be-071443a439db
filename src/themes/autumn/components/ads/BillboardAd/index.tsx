'use client';

import { useEffect, useState } from 'react';

import { useAppDispatch } from 'store/hooks';
import pageSlice from 'store/slices/page';
import Ad, { AdSize } from 'themes/autumn/components/ads/Ad';

import type { Props as AdProps } from 'themes/autumn/components/ads/Ad';

type Props = Pick<AdProps, 'className' | 'onRender' | 'sticky'>;

export default function BillboardAd(props: Props) {
  const [isBonzaiTruSkin, setBonzaiTruSkin] = useState(false);
  const dispatch = useAppDispatch();

  // Detect TruSkin ads by monitoring the `body` class for `has-bonzai` which
  // the TruSkin ad will add on render
  // Cannot use the ad render callback to check for Tru<PERSON>kin, as the ads can
  // load indirectly through an ad partner
  useEffect(() => {
    const observer = new MutationObserver(() => {
      const firstChild = document.body.children[0];
      if (
        // Desktop
        firstChild.className === 'bz-custom-container' ||
        // Mobile
        firstChild.id === 'bz-topfiller'
      ) {
        dispatch(pageSlice.actions.setHasBonzaiAd());
        setBonzaiTruSkin(true);
        observer.disconnect();
      }
    });

    observer.observe(document.body, {
      childList: true,
    });

    return () => {
      observer.disconnect();
    };
  }, [dispatch]);

  return (
    <Ad
      autoRefresh={false}
      isBonzaiTruSkin={isBonzaiTruSkin}
      mdSizes={AdSize.leaderboard as [number, number]}
      moveWhenGutterAdPresent
      placeholderPadding="py-3"
      position={1}
      publiftName="header-1"
      sizes={[]}
      slotId="billboard"
      withLabel={false}
      xlSizes={
        [
          [996, 120],
          [996, 250],
          [970, 250],
          [940, 250],
          [940, 120],
          // TypeScript infers this as an array, not a tuple
        ] as [number, number][]
      }
      // eslint-disable-next-line react/jsx-props-no-spreading
      {...props}
    />
  );
}
