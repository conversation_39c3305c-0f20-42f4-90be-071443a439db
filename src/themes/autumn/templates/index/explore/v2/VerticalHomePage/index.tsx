import clsx from 'clsx';
import React from 'react';

import { IndexPageFeatures } from 'themes/autumn/components/features';
import Container from 'themes/autumn/components/generic/Container';
import TemplateWrapper from 'themes/autumn/components/generic/TemplateWrapper';
import Zone from 'themes/autumn/components/zone/Zone';
import { ExploreTravelLogo } from 'themes/autumn/templates/zoneItems/navigation/ExploreTravelSubNav';
import { ZoneName } from 'types/ZoneItems';

import styles from './styles.module.css';

export const MARGIN_BETWEEN_ITEMS = 'mb-18';
export const MARGIN_HEADING = 'mb-6 md:mb-7';

export default function IndexPage(): React.ReactElement {
  return (
    <TemplateWrapper
      navigationStickyMobileContent={<ExploreTravelLogo />}
      navigationStickyWideContent={
        <Zone itemMargin="m-0" name={ZoneName.NAVIGATION} snapHeading />
      }
      showNavigationAd
      showStickyFooterAd
    >
      <Container maxWidthMd="max-w-full" maxWidthSm="max-w-full" noGutter>
        <div className={clsx(styles.bonzaiMargin, 'border-b border-gray-300')}>
          <Zone itemMargin="m-0" name={ZoneName.NAVIGATION} snapHeading />
        </div>
        <Zone
          itemMargin={MARGIN_BETWEEN_ITEMS}
          name={ZoneName.MAIN_TOP}
          snapHeading
        />
      </Container>
      <Container
        className="flex flex-col"
        maxWidthMd="max-w-container"
        maxWidthSm="max-w-container"
      >
        <div className="mx-auto mt-8 w-full empty:hidden md:max-w-md">
          <Zone
            itemMargin={MARGIN_BETWEEN_ITEMS}
            name={ZoneName.MAIN_TOP_SMALL_WIDTH}
            snapHeading
            snapMargin="mb-4"
          />
        </div>
        <div className="mt-8">
          <Zone
            itemMargin={MARGIN_BETWEEN_ITEMS}
            name={ZoneName.MAIN}
            snapHeading
            snapMargin={MARGIN_HEADING}
          />
        </div>
        <div className="mt-8">
          <Zone
            itemMargin={MARGIN_BETWEEN_ITEMS}
            name={ZoneName.BOTTOM}
            snapHeading
          />
        </div>
      </Container>
      <IndexPageFeatures />
    </TemplateWrapper>
  );
}
