import { TRIBUTES_FUNERALS_SLUG } from 'store/slices/features';
import { createStore } from 'store/store';
import { TestWrapper } from 'util/jest';

import { CardLayoutAd } from '../CardLayout';

import { Ad } from '.';

import type { Meta } from '@storybook/react';
import type { ClassifiedAd } from 'types/Classified';

export default {
  component: Ad,
  title: 'Notice board/Tributes & Funerals',
} as Meta<typeof Ad>;

function mockAd(options?: Partial<ClassifiedAd>): ClassifiedAd {
  return {
    canonicalUrl: '/',
    category: {
      id: 0,
      name: 'Death Notice',
      slug: '',
    },
    categoryText: '',
    customerEmail: '',
    customerName: '',
    customerPhone: '',
    customerPostcode: '',
    customerState: '',
    customerTown: '',
    dateBorn: '',
    dateDeceased: '',
    enableComments: false,
    expirationDate: '',
    funeralDate: '',
    funeralHomeAddress: '',
    funeralHomeCity: '',
    funeralHomeName: '',
    funeralHomePostcode: '',
    funeralHomeState: '',
    funeralStartTime: '',
    funeralVenueAddress: '',
    funeralVenueCity: '',
    funeralVenueName: '',
    funeralVenuePostcode: '',
    funeralVenueState: '',
    id: 1,
    images: [],
    location: '',
    logo: null,
    publicationDate: '2025-01-01',
    quoteText: '',
    text: '',
    title: 'Smith, John',
    url: '',
    yearBorn: null,
    yearDeceased: null,
    ...options,
  };
}

const ads = [
  mockAd({
    dateBorn: '1/3/1970',
    dateDeceased: '2/3/2025',
    location: 'Sydney, NSW',
    quoteText: 'Rest in Peace',
  }),
  mockAd({
    location: 'Sydney, NSW',
  }),
  mockAd({
    quoteText: 'Rest in Peace',
  }),
  mockAd({
    dateBorn: '1/3/1970',
    dateDeceased: '2/3/2025',
  }),
  mockAd({
    yearBorn: 1950,
    yearDeceased: 2025,
  }),
  mockAd({
    yearDeceased: 2025,
  }),
  mockAd(),
];

export const MasonryCard = () => (
  <TestWrapper store={createStore()}>
    <div className="container flex gap-2">
      {ads.map((ad) => (
        <Ad item={ad} key={ad.id} />
      ))}
    </div>
  </TestWrapper>
);

export const Card = () => (
  <TestWrapper
    store={createStore((state) => ({
      ...state,
      pages: {
        ...state.pages,
        primary: {
          altMenuName: '',
          id: 1,
          menuName: '',
          name: 'Tributes & Funerals',
          showHeading: true,
          showSiblingsOnChildPages: false,
          url: TRIBUTES_FUNERALS_SLUG,
        },
      },
    }))}
  >
    <div className="container">
      {ads.map((ad) => (
        <CardLayoutAd item={ad} key={ad.id} />
      ))}
    </div>
  </TestWrapper>
);
