import {
  faAd,
  faAlignLeft,
  faBars,
  faBriefcase,
  faCloudSun,
  faCode,
  faComments,
  faCompass,
  faEnvelope,
  faFolder,
  faHeading,
  faHome,
  faImage,
  faList,
  faLocationDot,
  faNewspaper,
  faPerson,
  faPlane,
  faPrint,
  faWindowMaximize,
} from '@fortawesome/free-solid-svg-icons';

import zoneItemTemplates from 'templates/zoneItemTemplates';
import { ZoneItemType } from 'types/ZoneItems';
import {
  fetchAuthorCollections,
  fetchPageCollections,
  fetchPages,
  fetchStoryLists,
  fetchStorylistCollections,
} from 'util/organization/suzuka';

import type { IconDefinition } from '@fortawesome/free-solid-svg-icons';

type FieldChoices =
  | string[]
  | number[]
  | [string, string][]
  | [number, string][];

export interface ZoneItemInfo {
  fields?: Array<
    | string
    | {
        choices?:
          | FieldChoices
          | ((siteId: number, token: string) => Promise<FieldChoices>);
        cols?: number;
        default?: boolean | number | string;
        dependent?: string;
        flipStoryDisplay?: boolean;
        helpText?: string;
        label?: string;
        max?: number;
        min?: number;
        name: string;
        rows?: number;
        rules?: {
          max?: number;
          min?: number;
          required?: boolean | string;
        };
        type?: 'boolean' | 'multiline' | 'number' | 'string';
      }
  >;
  icon: IconDefinition;
  name: string;
}

function sortSecondIndex(
  a: [number | string, string],
  b: [number | string, string],
) {
  if (a[1] < b[1]) {
    return -1;
  }

  if (a[1] > b[1]) {
    return 1;
  }

  return 0;
}

async function getStoryListChoices(
  siteId: number,
  token: string,
): Promise<[number, string][]> {
  const items: [number, string][] = (
    await fetchStoryLists([siteId, token])
  ).map(({ id, title }) => [id, title]);

  items.sort(sortSecondIndex);

  return items;
}

async function getPageChoices(
  siteId: number,
  token: string,
): Promise<[number, string][]> {
  const items: [number, string][] = (await fetchPages(siteId, token)).map(
    ({ id, name }) => [id, name],
  );

  items.sort(sortSecondIndex);

  return items;
}

async function getPageCollectionChoices(
  siteId: number,
  token: string,
): Promise<[number, string][]> {
  const items: [number, string][] = (
    await fetchPageCollections(siteId, token)
  ).map(({ id, title }) => [id, title]);

  items.sort(sortSecondIndex);

  return items;
}

async function getAuthorCollectionChoices(
  siteId: number,
  token: string,
): Promise<[number, string][]> {
  try {
    return (await fetchAuthorCollections(siteId, token))
      .map<[number, string]>(({ id, title }) => [id, title])
      .sort(sortSecondIndex);
  } catch (error) {
    console.error('Error fetching author collections', error);
    throw error;
  }
}

async function getStorylistCollectionChoices(
  siteId: number,
  token: string,
): Promise<[number, string][]> {
  const items: [number, string][] = (
    await fetchStorylistCollections(siteId, token)
  ).map(({ id, title }) => [id, title]);

  items.sort(sortSecondIndex);

  return items;
}

function getTemplateChoices(type: ZoneItemType) {
  // TODO Hard coded theme
  const templates = zoneItemTemplates.autumn[type];

  // Remove templates that are only meant for global zones, as editing them
  // is not supported yet
  const items: [string, string][] = Object.entries(templates)
    .filter(([, { globalOnly }]) => !globalOnly)
    .map(([template, { name }]) => [template, name]);

  items.sort(sortSecondIndex);

  return items;
}

const REQUIRED_MESSAGE = 'This field is required.';

const zoneItemTypeInfo: Record<ZoneItemType, ZoneItemInfo> = {
  [ZoneItemType.Advertisement]: {
    fields: [
      {
        helpText: 'Set to zero for automatic numbering',
        name: 'position',
        rules: {
          required: REQUIRED_MESSAGE,
        },
        type: 'number',
      },
    ],
    icon: faAd,
    name: 'Ad',
  },
  [ZoneItemType.Authors]: {
    fields: [
      {
        choices: getAuthorCollectionChoices,
        label: 'Author Collection',
        name: 'authorCollection',
        type: 'number',
      },
    ],
    icon: faFolder,
    name: 'Author Collection',
  },
  [ZoneItemType.Banner]: {
    fields: [
      'backgroundUrl',
      'title',
      'subTitle',
      {
        name: 'openNewWindow',
        type: 'boolean',
      },
      'url',
    ],
    icon: faImage,
    name: 'Banner',
  },
  [ZoneItemType.Carousel]: {
    fields: [
      {
        label: 'Slide 1 - Title',
        name: 'title1',
      },
      {
        label: 'Slide 1 - Description',
        name: 'description1',
      },
      {
        label: 'Slide 1 - URL',
        name: 'url1',
      },
      {
        label: 'Slide 1 - Image',
        name: 'image1',
      },
      {
        label: 'Slide 1 - Open in new window',
        name: 'openNewWindow1',
        type: 'boolean',
      },
      {
        label: 'Slide 2 - Title',
        name: 'title2',
      },
      {
        label: 'Slide 2 - Description',
        name: 'description2',
      },
      {
        label: 'Slide 2 - URL',
        name: 'url2',
      },
      {
        label: 'Slide 2 - Image',
        name: 'image2',
      },
      {
        label: 'Slide 2 - Open in new window',
        name: 'openNewWindow2',
        type: 'boolean',
      },
      {
        label: 'Slide 3 - Title',
        name: 'title3',
      },
      {
        label: 'Slide 3 - Description',
        name: 'description3',
      },
      {
        label: 'Slide 3 - URL',
        name: 'url3',
      },
      {
        label: 'Slide 3 - Image',
        name: 'image3',
      },
      {
        label: 'Slide 3 - Open in new window',
        name: 'openNewWindow3',
        type: 'boolean',
      },
      {
        label: 'Slide 4 - Title',
        name: 'title4',
      },
      {
        label: 'Slide 4 - Description',
        name: 'description4',
      },
      {
        label: 'Slide 4 - URL',
        name: 'url4',
      },
      {
        label: 'Slide 4 - Image',
        name: 'image4',
      },
      {
        label: 'Slide 4 - Open in new window',
        name: 'openNewWindow4',
        type: 'boolean',
      },
    ],
    icon: faImage,
    name: 'Carousel',
  },
  [ZoneItemType.CinematicFeatured]: {
    fields: [
      {
        label: 'Video ID',
        name: 'video_id',
      },
      {
        label: 'Title',
        name: 'title',
      },
      {
        label: 'Description',
        name: 'description',
      },
    ],
    icon: faCompass,
    name: 'Cinematic Featured',
  },
  [ZoneItemType.Classified]: {
    fields: [
      {
        choices: [3, 6, 9, 12],
        name: 'numberOfItems',
        type: 'number',
      },
    ],
    icon: faBriefcase,
    name: 'Classifieds',
  },
  [ZoneItemType.ClassifiedList]: {
    fields: [
      {
        choices: [3, 6, 9, 12],
        name: 'title',
        type: 'string',
      },
      {
        choices: [], // TODO
        label: 'Classified list',
        name: 'classifiedListId',
        type: 'number',
      },
      {
        name: 'limit',
        rules: {
          max: 20,
          min: 1,
          required: REQUIRED_MESSAGE,
        },
        type: 'number',
      },
      {
        default: 1,
        helpText:
          'The offset refers to the position' +
          ' that the classifiedlist will begin. \n' +
          'Example - if offset is set to 4, the first classified shown will ' +
          'be the 4th story from that classifiedlist',
        name: 'offset',
        rules: {
          min: 1,
          required: REQUIRED_MESSAGE,
        },
        type: 'number',
      },
      {
        name: 'pinnedClassifiedsOnly',
        type: 'boolean',
      },
    ],
    icon: faBriefcase,
    name: 'Classified List',
  },
  [ZoneItemType.ClusteredStoryList]: {
    fields: [
      {
        choices: getStoryListChoices,
        label: 'Story list',
        name: 'storyListId',
        type: 'number',
      },
      'fromOrganisation',
      {
        default: 1,
        helpText:
          'The offset refers to the position' +
          ' that the storylist will begin. \n' +
          'Example - if offset is set to 4, the first story shown will ' +
          'be the 4th story from that storylist',
        name: 'offset',
        rules: {
          min: 1,
          required: REQUIRED_MESSAGE,
        },
        type: 'number',
      },
      {
        name: 'limit',
        rules: {
          max: 20,
          min: 1,
          required: REQUIRED_MESSAGE,
        },
        type: 'number',
      },
      {
        helpText:
          "Use the story's canonical URL instead of opening on this site, " +
          'and open in a new window.',
        label: 'Use canonical URL',
        name: 'useCanonicalUrl',
        type: 'boolean',
      },
    ],
    icon: faNewspaper,
    name: 'Clustered story list',
  },
  [ZoneItemType.Comments]: {
    fields: ['title'],
    icon: faComments,
    name: 'Comments',
  },
  [ZoneItemType.DPECard]: {
    // No fields
    icon: faPrint,
    name: 'DPE card',
  },
  [ZoneItemType.DPEList]: {
    fields: [
      {
        label: 'Items per page',
        name: 'limit',
        rules: {
          required: REQUIRED_MESSAGE,
        },
        type: 'number',
      },
      {
        label: 'DPE ID',
        name: 'dpeId',
      },
    ],
    icon: faPrint,
    name: 'DPE list',
  },
  [ZoneItemType.EMagList]: {
    fields: [
      {
        label: 'Items per page',
        name: 'limit',
        rules: {
          required: REQUIRED_MESSAGE,
        },
        type: 'number',
      },
    ],
    icon: faPrint,
    name: 'EMag List',
  },
  [ZoneItemType.ExploreSimpleLinkWidget]: {
    fields: [
      {
        label: 'Title',
        name: 'title',
        type: 'string',
      },
      {
        label: 'Description',
        name: 'description',
        type: 'string',
      },
      {
        label: 'Button Text',
        name: 'buttonText',
        type: 'string',
      },
      {
        label: 'URL',
        name: 'url',
        type: 'string',
      },
    ],
    icon: faNewspaper,
    name: 'Explore Simple Link Widget',
  },
  [ZoneItemType.ExploreTravelDealList]: {
    fields: [
      {
        label: 'Filter Deal by Id',
        name: 'apiFilterById',
        type: 'string',
      },
      {
        label: 'Filter Deal by Destination',
        name: 'apiFilterByDestination',
        type: 'string',
      },
    ],
    icon: faNewspaper,
    name: 'Explore Travel Deal List',
  },
  [ZoneItemType.FAQ]: {
    // TODO: Implement a pair of fields for FAQ items
    fields: [],
    icon: faNewspaper,
    name: 'FAQ',
  },
  [ZoneItemType.FeaturedDestination]: {
    fields: [
      {
        label: 'Title',
        name: 'title',
      },
      {
        label: 'Description',
        name: 'description',
      },
      {
        label: 'URL',
        name: 'url',
      },
      {
        label: 'Desktop Image',
        name: 'desktopImage',
      },
      {
        label: 'Tablet Image',
        name: 'tabletImage',
      },
      {
        label: 'Mobile Image',
        name: 'mobileImage',
      },
    ],
    icon: faPlane,
    name: 'Featured Destination',
  },
  [ZoneItemType.Footer]: {
    fields: [],
    icon: faList,
    name: 'Footer',
  },
  [ZoneItemType.Heading]: {
    fields: [
      'heading',
      {
        label: 'Open in a new window',
        name: 'openNewWindow',
        type: 'boolean',
      },
      'url',
    ],
    icon: faHeading,
    name: 'Heading',
  },
  [ZoneItemType.CodeSnippet]: {
    fields: [
      {
        // TODO WYSIWYG editor
        name: 'code',
        rows: 5,
        type: 'multiline',
      },
    ],
    icon: faCode,
    name: 'HTML',
  },
  [ZoneItemType.Iframe]: {
    fields: [
      {
        label: 'URL',
        name: 'url',
      },
      {
        helpText: 'Custom CSS',
        name: 'style',
      },
      {
        helpText: 'Allow feature policy options for the iframe',
        name: 'allow',
        // TODO Multiple choice checkboxes
        // choices: [
        //   'autoplay',
        //   'camera',
        //   'fullscreen',
        //   'geolocation',
        //   'microphone',
        //   'speaker',
        // ],
      },
    ],
    icon: faWindowMaximize,
    name: 'iframe',
  },
  [ZoneItemType.Image]: {
    fields: [
      {
        label: 'Desktop Image',
        name: 'desktopImage',
      },
      {
        label: 'Tablet Image',
        name: 'tabletImage',
      },
      {
        label: 'Mobile Image',
        name: 'mobileImage',
      },
      {
        label: 'Alt',
        name: 'alt',
      },
      {
        label: 'Title',
        name: 'title',
      },
      {
        label: 'Open New Window',
        name: 'openNewWindow',
        type: 'boolean',
      },
      {
        label: 'Url',
        name: 'url',
      },
    ],
    icon: faImage,
    name: 'image',
  },
  [ZoneItemType.Navigation]: {
    fields: [
      {
        choices: [
          ['white', 'White'],
          ['black', 'Black'],
          ['custom', 'Custom'],
        ],
        label: 'Brand Colour',
        name: 'brandColor',
      },
      {
        label: 'Custom Colour',
        name: 'customColor',
      },
      {
        choices: [
          ['dark', 'Dark'],
          ['light', 'Light'],
        ],
        label: 'Font Colour',
        name: 'fontColor',
      },
      {
        choices: [
          ['standard', 'Standard'],
          ['reversed', 'Reversed'],
        ],
        label: 'Brand Image',
        name: 'brandImage',
      },
      {
        label: 'Est. Year',
        name: 'estYear',
      },
      {
        label: 'Shortcut 1 Label',
        name: 'shortcut1Label',
      },
      {
        label: 'Shortcut 1 Description',
        name: 'shortcut1Description',
      },
      {
        label: 'Shortcut 1 URL',
        name: 'shortcut1Url',
      },
      {
        label: 'Shortcut 1 Icon',
        name: 'shortcut1Icon',
      },
      {
        label: 'Shortcut 1 CTA',
        name: 'shortcut1Cta',
      },
      {
        label: 'Shortcut 1 Badge',
        name: 'shortcut1Badge',
      },
      {
        label: 'Shortcut 1 Open in new window',
        name: 'shortcut1NewWindow',
        type: 'boolean',
      },
      {
        label: 'Shortcut 2 Label',
        name: 'shortcut2Label',
      },
      {
        label: 'Shortcut 2 Description',
        name: 'shortcut2Description',
      },
      {
        label: 'Shortcut 2 URL',
        name: 'shortcut2Url',
      },
      {
        label: 'Shortcut 2 Icon',
        name: 'shortcut2Icon',
      },
      {
        label: 'Shortcut 2 CTA',
        name: 'shortcut2Cta',
      },
      {
        label: 'Shortcut 2 Badge',
        name: 'shortcut2Badge',
      },
      {
        label: 'Shortcut 2 Open in new window',
        name: 'shortcut2NewWindow',
        type: 'boolean',
      },
      {
        label: 'Shortcut 3 Label',
        name: 'shortcut3Label',
      },
      {
        label: 'Shortcut 3 Description',
        name: 'shortcut3Description',
      },
      {
        label: 'Shortcut 3 URL',
        name: 'shortcut3Url',
      },
      {
        label: 'Shortcut 3 Icon',
        name: 'shortcut3Icon',
      },
      {
        label: 'Shortcut 3 CTA',
        name: 'shortcut3Cta',
      },
      {
        label: 'Shortcut 3 Badge',
        name: 'shortcut3Badge',
      },
      {
        label: 'Shortcut 3 Open in new window',
        name: 'shortcut3NewWindow',
        type: 'boolean',
      },
      {
        label: 'Shortcut 4 Label',
        name: 'shortcut4Label',
      },
      {
        label: 'Shortcut 4 Description',
        name: 'shortcut4Description',
      },
      {
        label: 'Shortcut 4 URL',
        name: 'shortcut4Url',
      },
      {
        label: 'Shortcut 4 Icon',
        name: 'shortcut4Icon',
      },
      {
        label: 'Shortcut 4 CTA',
        name: 'shortcut4Cta',
      },
      {
        label: 'Shortcut 4 Badge',
        name: 'shortcut4Badge',
      },
      {
        label: 'Shortcut 4 Open in new window',
        name: 'shortcut4NewWindow',
        type: 'boolean',
      },
      {
        label: 'External Link 1 Text',
        name: 'externalLink1Text',
      },
      {
        label: 'External Link 1 URL',
        name: 'externalLink1Url',
      },
      {
        label: 'External Link 1 New Window',
        name: 'externalLink1NewWindow',
        type: 'boolean',
      },
      {
        label: 'External Link 1 Icon',
        name: 'externalLink1Icon',
      },
      {
        label: 'External Link 2 Text',
        name: 'externalLink2Text',
      },
      {
        label: 'External Link 2 URL',
        name: 'externalLink2Url',
      },
      {
        label: 'External Link 2 New Window',
        name: 'externalLink2NewWindow',
        type: 'boolean',
      },
      {
        label: 'External Link 2 Icon',
        name: 'externalLink2Icon',
      },
      {
        label: 'External Link 3 Text',
        name: 'externalLink3Text',
      },
      {
        label: 'External Link 3 URL',
        name: 'externalLink3Url',
      },
      {
        label: 'External Link 3 New Window',
        name: 'externalLink3NewWindow',
        type: 'boolean',
      },
      {
        label: 'External Link 3 Icon',
        name: 'externalLink3Icon',
      },
      {
        label: 'External Link 4 Text',
        name: 'externalLink4Text',
      },
      {
        label: 'External Link 4 URL',
        name: 'externalLink4Url',
      },
      {
        label: 'External Link 4 New Window',
        name: 'externalLink4NewWindow',
        type: 'boolean',
      },
      {
        label: 'External Link 4 Icon',
        name: 'externalLink4Icon',
      },
      {
        label: 'External Link 5 Text',
        name: 'externalLink5Text',
      },
      {
        label: 'External Link 5 URL',
        name: 'externalLink5Url',
      },
      {
        label: 'External Link 5 New Window',
        name: 'externalLink5NewWindow',
        type: 'boolean',
      },
      {
        label: 'External Link 5 Icon',
        name: 'externalLink5Icon',
      },
      {
        label: 'Advertising URL',
        name: 'advertisingUrl',
      },
    ],
    icon: faBars,
    name: 'Navigation',
  },
  [ZoneItemType.MailingList]: {
    fields: [
      'headingText',
      // TODO Textarea to allow multiple lines
      { label: 'Support text', name: 'text' },
      'tags',
      {
        helpText:
          'If unchecked, widget will not be visible to non-subscribers',
        label: 'Widget visible',
        name: 'subscribeVisible',
        type: 'boolean',
      },
      {
        helpText: 'Additional query string data for the form action',
        name: 'formData',
      },
      'marketingCloudUrl',
    ],
    icon: faEnvelope,
    name: 'Mailing list',
  },
  [ZoneItemType.MenuList]: {
    fields: [
      {
        choices: getPageChoices,
        name: 'page',
        type: 'number',
      },
      'subtitle',
    ],
    icon: faList,
    name: 'Menu list',
  },
  [ZoneItemType.Newsletter]: {
    icon: faNewspaper,
    name: 'Newsletters',
  },
  [ZoneItemType.Socials]: {
    icon: faHome,
    name: 'Socials',
  },
  [ZoneItemType.PageCollection]: {
    fields: [
      {
        choices: getPageCollectionChoices,
        label: 'Collection',
        name: 'collection',
        type: 'number',
      },
    ],
    icon: faFolder,
    name: 'Page Collection',
  },
  [ZoneItemType.PlayHq]: {
    fields: [
      {
        label: 'Organisation Id',
        name: 'organisationId',
        type: 'string',
      },
      {
        label: 'Season Id',
        name: 'seasonId',
        type: 'string',
      },
      {
        label: 'Grade #1 Id',
        name: 'grade1Id',
        type: 'string',
      },
      {
        label: 'Grade #2 Id',
        name: 'grade2Id',
        type: 'string',
      },
      {
        label: 'League URL',
        name: 'leagueUrl',
        type: 'string',
      },
      {
        label: 'League name',
        name: 'leagueName',
        type: 'string',
      },
      {
        label: 'League image ID',
        name: 'leagueImageId',
        type: 'string',
      },
    ],
    icon: faHome,
    name: 'PlayHQ widget',
  },
  [ZoneItemType.REVWidget]: {
    // No fields
    icon: faHome,
    name: 'REV widget',
  },
  [ZoneItemType.SportsHub]: {
    fields: [
      {
        label: 'Title',
        name: 'title',
        type: 'string',
      },
    ],
    icon: faHome,
    name: 'Sports hub widget',
  },
  [ZoneItemType.DailyMotion]: {
    fields: [
      {
        label: 'Player list id',
        name: 'player_list_id',
        type: 'string',
      },
      {
        label: 'Number of Video',
        name: 'number_of_video',
        rules: {
          max: 20,
          min: 1,
        },
        type: 'number',
      },
      {
        label: 'url',
        name: 'url',
        type: 'string',
      },
    ],
    icon: faHome,
    name: 'Daily motion',
  },
  [ZoneItemType.StoryList]: {
    fields: [
      {
        choices: getStoryListChoices,
        label: 'Story list',
        name: 'storyListId',
        type: 'number',
      },
      {
        label: 'URL parameters',
        name: 'urlParams',
      },
      {
        label: 'Label',
        name: 'label',
      },
      {
        default: 1,
        helpText:
          'The offset refers to the position' +
          ' that the storylist will begin. \n' +
          'Example - if offset is set to 4, the first story shown will ' +
          'be the 4th story from that storylist',
        name: 'offset',
        rules: {
          min: 1,
          required: REQUIRED_MESSAGE,
        },
        type: 'number',
      },
      {
        default: 10,
        helpText:
          'The number of the stories will be shown (value range 1 to 20)',
        max: 20,
        min: 1,
        name: 'limit',
        rules: {
          max: 20,
          min: 1,
          required: REQUIRED_MESSAGE,
        },
        type: 'number',
      },
      {
        choices: [
          ['E', 'Enable(only show summary that is enabled in Newsnow)'],
          ['A', 'Always show(override settings in Newsnow)'],
          ['D', 'hide summary; override settings in Newsnow'],
        ],
        label: 'Summary: select option for story list',
        name: 'summaryOptions',
        type: 'string',
      },
      {
        label: 'Show lead story',
        name: 'largeLeadStory',
        type: 'boolean',
      },
      {
        dependent: 'largeLeadStory',
        label: 'Toggle large image on lead story',
        name: 'isHeroImage',
        type: 'boolean',
      },
      {
        helpText: 'Put the large story on the right',
        name: 'flipStoryDisplay',
        type: 'boolean',
      },
      {
        name: 'pinnedStoriesOnly',
        type: 'boolean',
      },
      {
        helpText: 'Display ads if the layout supports it',
        name: 'allowAds',
        type: 'boolean',
      },
      {
        helpText:
          "Use the story's canonical URL instead of opening on this site, " +
          'and open in a new window.',
        label: 'Use canonical URL',
        name: 'useCanonicalUrl',
        type: 'boolean',
      },
      {
        helpText: 'Hide the bottom border line',
        label: 'Disable bottom border',
        name: 'disableBottomBorder',
        type: 'boolean',
      },
    ],
    icon: faNewspaper,
    name: 'Story list',
  },
  [ZoneItemType.StoryListCollection]: {
    fields: [
      {
        choices: getStorylistCollectionChoices,
        label: 'Collection',
        name: 'collection',
        type: 'number',
      },
      {
        default: 10,
        helpText:
          'The number of the stories will be shown (value range 1 to 20)',
        max: 20,
        min: 1,
        name: 'limit',
        rules: {
          max: 20,
          min: 1,
          required: REQUIRED_MESSAGE,
        },
        type: 'number',
      },
      {
        helpText:
          'Comma separated list of tags that get merged with each storylist in the collection to provide extra context',
        label: 'Tags',
        name: 'tags',
      },
      {
        helpText:
          "Use the story's canonical URL instead of opening on this site, " +
          'and open in a new window.',
        label: 'Use canonical URL',
        name: 'useCanonicalUrl',
        type: 'boolean',
      },
    ],
    icon: faFolder,
    name: 'Storylist Collection',
  },
  [ZoneItemType.TitledStoryList]: {
    fields: [
      {
        choices: getStoryListChoices,
        label: 'Story list',
        name: 'storyListId',
        type: 'number',
      },
      'title',
      {
        label: 'URL parameters',
        name: 'urlParams',
      },
      {
        name: 'limit',
        rules: {
          max: 20,
          min: 1,
          required: REQUIRED_MESSAGE,
        },
        type: 'number',
      },
      {
        name: 'pinnedStoriesOnly',
        type: 'boolean',
      },
      {
        helpText: 'Display ads if the layout supports it',
        name: 'allowAds',
        type: 'boolean',
      },
      {
        helpText:
          "Use the story's canonical URL instead of opening on this site, " +
          'and open in a new window.',
        label: 'Use canonical URL',
        name: 'useCanonicalUrl',
        type: 'boolean',
      },
    ],
    icon: faNewspaper,
    name: 'Titled story list',
  },
  [ZoneItemType.TextBlock]: {
    // TODO WYSIWYG editor
    fields: ['text'],
    icon: faAlignLeft,
    name: 'Text block',
  },
  [ZoneItemType.Traffic]: {
    fields: [
      {
        default: 3,
        helpText: 'Limit the number of listings shown',
        min: 1,
        name: 'limit',
        rules: {
          min: 1,
          required: REQUIRED_MESSAGE,
        },
        type: 'number',
      },
      {
        label: 'Title',
        name: 'title',
        type: 'string',
      },
      {
        label: 'Distance range',
        name: 'range',
        type: 'string',
      },
    ],
    icon: faLocationDot,
    name: 'Traffic',
  },
  [ZoneItemType.UgcList]: {
    fields: [
      {
        choices: [3, 6, 9, 12],
        name: 'title',
        type: 'string',
      },
      {
        choices: [], // TODO
        label: 'UGC list',
        name: 'ugcListId',
        type: 'number',
      },
      {
        name: 'limit',
        rules: {
          max: 20,
          min: 1,
          required: REQUIRED_MESSAGE,
        },
        type: 'number',
      },
      {
        default: 1,
        helpText:
          'The offset refers to the position' +
          ' that the ugclist will begin. \n' +
          'Example - if offset is set to 4, the first ugc shown will ' +
          'be the 4th ugc from that ugclist',
        name: 'offset',
        rules: {
          min: 1,
          required: REQUIRED_MESSAGE,
        },
        type: 'number',
      },
      {
        name: 'pinnedUgcOnly',
        type: 'boolean',
      },
    ],
    icon: faPerson,
    name: 'UGC List',
  },
  [ZoneItemType.ViewJobs]: {
    fields: [
      {
        default: 2,
        helpText: 'Limit the number of listings shown',
        min: 1,
        name: 'limit',
        rules: {
          min: 1,
          required: REQUIRED_MESSAGE,
        },
        type: 'number',
      },
      {
        default: 1,
        helpText: 'The offset in the listings to start from',
        name: 'offset',
        rules: {
          min: 1,
          required: REQUIRED_MESSAGE,
        },
        type: 'number',
      },
      {
        default: false,
        label: 'Featured listings appear first',
        name: 'stickyFeatured',
        type: 'boolean',
      },
    ],
    icon: faLocationDot,
    name: 'ViewJobs',
  },
  [ZoneItemType.Weather]: {
    // No fields
    icon: faCloudSun,
    name: 'Weather',
  },
};

// Add the template field to each zone item type, if it has template choices
Object.entries(zoneItemTypeInfo).forEach(([type, info]) => {
  const templates = getTemplateChoices(type as ZoneItemType);

  if (templates.length === 0) {
    return;
  }

  if (!info.fields) {
    // eslint-disable-next-line no-param-reassign
    info.fields = [];
  }

  info.fields.unshift({
    choices: templates,
    name: 'template',
  });
});

export default zoneItemTypeInfo;
