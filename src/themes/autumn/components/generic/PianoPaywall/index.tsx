import { PianoHook } from 'components/Piano';
import { useAppSelector } from 'store/hooks';
import Paywall from 'themes/autumn/templates/auth/Paywall';

interface PianoPaywallProps {
  className?: string;
}

export default function PianoPaywall({ className }: PianoPaywallProps) {
  const pianoFeature = useAppSelector((state) => state.features.piano);

  if (!pianoFeature.enabled) {
    return null;
  }

  const enableNewPaywall = pianoFeature.data.supportAuthServerPaywall;

  return (
    <>
      {!enableNewPaywall && <PianoHook id="paywall-hook" />}
      {enableNewPaywall && <Paywall className={className} />}
    </>
  );
}
