import { createSlice } from '@reduxjs/toolkit';

import { StoryLayout } from 'types/ZoneItems';

export enum ThemeVariant {
  AGS = 'AGS',
  DEFAULT = 'DEFAULT',
  ECHIDNA = 'ECHIDNA',
  EXPLORE = 'EXPLORE',
  MOP = 'MOP',
}

export enum PageThemeVariant {
  CLASSIFIEDS = 'CLASSIFIEDS',
  EXPLORE_TRAVEL = 'EXPLORE_TRAVEL',
  LOCAL_PARTNER = 'LOCAL_PARTNER',
  SPONSORED = 'SPONSORED',
  SPORT = 'SPORT',
}

export enum RenderMode {
  NORMAL = 'normal',
  EDIT = 'editmode',
}

export interface RevNotification {
  enabled: boolean;
  url: string;
}

export interface ConfState {
  brightcoveAccountId: string;
  brightcoveAdfreePlayerId: string;
  brightcovePlayerId: string;
  chartbeatDomain: string;
  chartbeatUid: number;
  dailymotionDefaultPlayerlistId: string;
  dailymotionPipEnabledDesktop: boolean;
  dailymotionPipEnabledMobile: boolean;
  dailymotionPlayerId: string;
  dailymotionPlayerIdForExploreTravelArticles: string;
  dailymotionPlayerIdForLooperVideos: string;
  dailymotionPlayerIdVideoShorts: string;
  dailymotionPlayerIdWithAutoplayDisabled: string;
  dailymotionSyndicationKey: string;
  defaultCommentFeature: 'facebook' | 'viafoura';
  displayAgsBanner: boolean;
  domain: string;
  domainVerifyCodeBing: string;
  domainVerifyCodeFacebook: string;
  domainVerifyCodeGoogle: string;
  facebookUrl: string;
  gaDomain1: string;
  gaDomain2: string;
  gaDomain3: string;
  gaId1: string;
  gaId2: string;
  gaId3: string;
  googleAdManagerNetworkIdentifier: string;
  googleSearchId: string;
  googleVertexConfigId: string;
  hasBeta: boolean;
  hasDpe: boolean;
  hasGift: boolean;
  hasGroupSubscriptions: boolean;
  hasPuzzles: boolean;
  indexExchangeId: string;
  instagramUsername: string;
  latitude: number | null;
  location: string;
  logoAlt: string;
  logoMain: string;
  logoSvgOnly: string;
  logoSvgSquare: string;
  logoSvgUnderline: string;
  logoSvgUnderlineBlack: string;
  longitude: number | null;
  mailchimpListManage: string;
  mode: RenderMode;
  name: string;
  organizationId: string;
  publication: string;
  revNotification: RevNotification;
  revOrganizationId: string;
  storyLayout: StoryLayout;
  themeVariant: ThemeVariant;
  tiktokUsername: string;
  timestamp: number;
  topDownAdCatTargeting: boolean;
  twitterUsername: string;
  useCleanStoryUrl: boolean;
  youtubeUrl: string;
}

export const initialState: ConfState = {
  brightcoveAccountId: '',
  brightcoveAdfreePlayerId: '',
  brightcovePlayerId: '',
  chartbeatDomain: '',
  chartbeatUid: 0,
  dailymotionDefaultPlayerlistId: '',
  dailymotionPipEnabledDesktop: false,
  dailymotionPipEnabledMobile: false,
  dailymotionPlayerId: '',
  dailymotionPlayerIdForExploreTravelArticles: '',
  dailymotionPlayerIdForLooperVideos: '',
  dailymotionPlayerIdVideoShorts: '',
  dailymotionPlayerIdWithAutoplayDisabled: '',
  dailymotionSyndicationKey: '',
  defaultCommentFeature: 'viafoura',
  displayAgsBanner: false,
  domain: '',
  domainVerifyCodeBing: '',
  domainVerifyCodeFacebook: '',
  domainVerifyCodeGoogle: '',
  facebookUrl: '',
  gaDomain1: '',
  gaDomain2: '',
  gaDomain3: '',
  gaId1: '',
  gaId2: '',
  gaId3: '',
  googleAdManagerNetworkIdentifier: '',
  googleSearchId: '',
  googleVertexConfigId: '',
  hasBeta: false,
  hasDpe: false,
  hasGift: false,
  hasGroupSubscriptions: false,
  hasPuzzles: false,
  indexExchangeId: '',
  instagramUsername: '',
  latitude: null,
  location: '',
  logoAlt: '',
  logoMain: '',
  logoSvgOnly: '',
  logoSvgSquare: '',
  logoSvgUnderline: '',
  logoSvgUnderlineBlack: '',
  longitude: null,
  mailchimpListManage: '',
  mode: RenderMode.NORMAL,
  name: '',
  organizationId: '',
  publication: '',
  revNotification: {
    enabled: false,
    url: '',
  },
  revOrganizationId: '',
  storyLayout: StoryLayout.SINGLE_STORY,
  themeVariant: ThemeVariant.DEFAULT,
  tiktokUsername: '',
  timestamp: 0,
  topDownAdCatTargeting: false,
  twitterUsername: '',
  useCleanStoryUrl: false,
  youtubeUrl: '',
};

const confSlice = createSlice({
  initialState,
  name: 'conf',
  reducers: {},
});

export default confSlice;
