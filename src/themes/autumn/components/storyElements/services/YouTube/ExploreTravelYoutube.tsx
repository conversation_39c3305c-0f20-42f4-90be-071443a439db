import { twMerge } from 'tailwind-merge';

import { useAppSelector } from 'store/hooks';

import type { GenericHeightsProps } from '../../common/storyElements';
import type { ServiceProps } from '../types';

function ExploreTravelYoutube({
  element,
  widthClassName,
}: ServiceProps): React.ReactElement | null {
  const isClientSide = useAppSelector((state) => state.runtime.isClientSide);
  if (!isClientSide) {
    return null;
  }

  return (
    <iframe
      allowFullScreen
      className={twMerge(
        'mx-auto aspect-video w-full border-0',
        widthClassName,
      )}
      src={`https://www.youtube.com/embed/${element.serviceId}`}
      title={`Youtube video - ${element.description}`}
    />
  );
}
export function exploreTravelYouTubeHeights(
  // eslint-disable-next-line no-empty-pattern
  {}: GenericHeightsProps,
): number {
  return 300;
}

export default ExploreTravelYoutube;
