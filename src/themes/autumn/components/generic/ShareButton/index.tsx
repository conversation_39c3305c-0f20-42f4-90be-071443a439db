'use client';

import {
  faFacebook,
  faWhatsapp,
  faXTwitter,
} from '@fortawesome/free-brands-svg-icons';
import { faLink } from '@fortawesome/free-solid-svg-icons';
import { useEffect, useState } from 'react';

import DropDown from 'themes/autumn/components/generic/DropDown';
import ShareItem from 'themes/autumn/components/generic/ShareItem';
import { getSocialShareUrls } from 'themes/autumn/components/stories/StorySocials/utils';

import type { DropDownDirection } from 'themes/autumn/components/generic/DropDown';

interface ShareButtonProps {
  children: React.ReactElement;
  dropDownDirection?: DropDownDirection;
  hideCopy?: boolean;
}

export default function ShareButton({
  children,
  dropDownDirection,
  hideCopy = false,
}: ShareButtonProps): React.ReactElement | null {
  const [url, setUrl] = useState<string>();
  const [canShare, setCanShare] = useState(false);
  const [canCopy, setCanCopy] = useState(false);

  useEffect(() => {
    setCanShare(!!navigator.share);

    // eslint-disable-next-line compat/compat
    setCanCopy(!!navigator.clipboard);
  }, []);

  useEffect(() => {
    setUrl(window.location.href);
  }, [setUrl]);

  function handleChange() {
    setUrl(window.location.href);
  }

  useEffect(() => {
    window.addEventListener('popstate', handleChange);
    return () => window.removeEventListener('popstate', handleChange);
  }, []);

  if (!url) {
    return null;
  }

  function onClick() {
    navigator.share({ title: document.title, url }).catch(() => {});
  }

  if (canShare) {
    return (
      <button className="font-semibold" onClick={onClick} type="button">
        {children}
      </button>
    );
  }

  const encodedUrl = encodeURIComponent(url);
  const socialUrls = getSocialShareUrls(encodedUrl);

  return (
    <DropDown
      className="font-semibold"
      direction={dropDownDirection}
      label={children}
    >
      <ShareItem
        bgColor="text-blue-500"
        className="gtm-hook-share-facebook appearance-none px-4 py-2 hover:bg-gray-100"
        color="text-white"
        href={socialUrls.facebook}
        icon={faFacebook}
        inDropDown
        label="Facebook"
      />
      <ShareItem
        bgColor="text-black"
        className="gtm-hook-share-twitter appearance-none px-4 py-2 hover:bg-gray-100"
        color="text-white"
        href={socialUrls.twitter}
        icon={faXTwitter}
        inDropDown
        label="Twitter"
      />
      <ShareItem
        bgColor="text-green-400"
        className="gtm-hook-share-whatsapp appearance-none px-4 py-2 hover:bg-gray-100"
        color="text-white"
        href={socialUrls.whatsapp}
        icon={faWhatsapp}
        inDropDown
        label="Whatsapp"
      />
      {canCopy && !hideCopy && (
        <ShareItem
          bgColor="text-gray-100"
          className="gtm-hook-share-copy-link appearance-none px-4 py-2 hover:bg-gray-100"
          color="text-gray-800"
          icon={faLink}
          inDropDown
          label="Copy Link"
          onClick={() => {
            // eslint-disable-next-line compat/compat
            navigator.clipboard
              .writeText(window.location.href)
              .catch(() => {});
          }}
          tag="button"
        />
      )}
    </DropDown>
  );
}
