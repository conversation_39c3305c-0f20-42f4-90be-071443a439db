import Link from 'themes/autumn/components/generic/Link';
import Signpost, {
  ColorPalette,
} from 'themes/autumn/components/stories/Signpost';
import NativeImage from 'themes/autumn/components/storyElements/common/NativeImage';

import type { Story } from 'types/Story';

export interface ExploreTravelRecirculationWidgetProps {
  latestStories: Story[];
  latestStoriesLimit: number;
  mostPopularStories: Story[];
  mostPopularStoriesLimit: number;
}

const ExploreTravelRecirculationWidget = ({
  latestStories = [],
  latestStoriesLimit = 5,
  mostPopularStories = [],
  mostPopularStoriesLimit = 10,
}: ExploreTravelRecirculationWidgetProps) => {
  const latestStoriesItems = latestStories.slice(0, latestStoriesLimit);
  const mostPopularStoriesItems = mostPopularStories.slice(
    0,
    mostPopularStoriesLimit,
  );

  return (
    <div className="flex w-full flex-col gap-10 md:flex-row md:gap-0">
      {/* Latest Stories Section */}
      <div className="border-gray-200 md:w-2/3 md:border-r-1 md:pr-11">
        <h2
          aria-label="Latest Stories"
          className="mb-7 font-inter text-lg font-semibold md:mb-4"
        >
          Travel&apos;s Top Picks
        </h2>
        <div className="space-y-6">
          {latestStoriesItems.map((story) => (
            <div
              className="group border-t-1 border-gray-300 pt-6 first:border-t-0 first:pt-0"
              key={story.id}
            >
              <Link className="flex flex-col" href={story.url} noStyle>
                <Signpost
                  className="mb-2"
                  colorPalette={ColorPalette.PALETTE_EXPLORE}
                  publishFrom={story.publishFrom}
                  tags={story.tags}
                />
                <div className="flex items-start gap-3 md:gap-11">
                  <div className="flex-1">
                    <h3 className="mb-2 font-playfair text-lg/[26px] font-medium group-hover:underline md:text-xl">
                      {story.title}
                    </h3>
                    <p className="hidden font-inter text-sm text-gray-900 md:line-clamp-2">
                      {story.summaryFull ?? ''}
                    </p>
                  </div>
                  {story.leadImage && (
                    <div className="shrink-0 overflow-hidden rounded-md">
                      <NativeImage
                        alt={story.leadImage.title || story.title}
                        height={144}
                        image={story.leadImage}
                        imageClassName="!w-[120px] !h-[89px] 
                          md:!w-[216px] md:!h-[144px] object-cover"
                        width={216}
                      />
                    </div>
                  )}
                </div>
              </Link>
            </div>
          ))}
        </div>
      </div>

      {/* Most Viewed Section */}
      <div className="md:mt-0 md:w-1/3 md:pl-11">
        <h2
          aria-label="Most Viewed Stories"
          className="mb-7 font-inter text-lg font-semibold md:mb-4"
        >
          Travel&apos;s Most Viewed
        </h2>
        <div className="flex flex-col gap-4">
          {mostPopularStoriesItems.map((story, index) => (
            <Link
              className="group flex items-start gap-3 rounded border-t-1 border-gray-200 pt-4 first:border-t-0 first:pt-0 md:-mx-2"
              href={story.url}
              key={story.id}
              noStyle
            >
              <div className="w-6 font-playfair text-[28px]/[26px] font-medium text-gray-900">
                {index + 1}
              </div>
              <div className="flex-1">
                <h3 className="line-clamp-3 font-inter text-base/[26px] font-normal text-gray-900 group-hover:underline">
                  {story.title}
                </h3>
              </div>
              {story.leadImage && (
                <div className="size-[90px] overflow-hidden rounded-md">
                  <NativeImage
                    alt={story.leadImage.title || story.title}
                    height={90}
                    image={story.leadImage}
                    imageClassName="size-full object-cover"
                    width={90}
                  />
                </div>
              )}
            </Link>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ExploreTravelRecirculationWidget;
