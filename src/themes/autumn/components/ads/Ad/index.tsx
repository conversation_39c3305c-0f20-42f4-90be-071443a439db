import { useAppSelector } from 'store/hooks';

/**
 * <Ad /> component for loading Google DoubleClick for Publishers (DFP) ads
 *
 * Notes: Please ensure to send the ad requests after the ad units are
 * checked to avoid the invalid traffic.
 * e.g. send the requests after paywall loading or after checking the
 * account subscription
 *
 * Usage:
 * Ads are responsive, and `sizes` props work similar to Tailwind's responsive
 * classes. They accept an array of tuples of ad sizes, or a single tuple.
 * The `sizes` prop provides the allowed ad sizes for all devices, but can be
 * overridden by the `mdSizes` or `xlSizes` props. Use an empty array `[]` to
 * disable ads for a given size.
 *
 * Use the `AdSizes` object for easy access to known ad sizes (`mrec` etc.).
 *
 * Examples:
 * Mrec on all devices:
 * <Ad sizes={[300, 250]} />
 *
 * Mrec on mobile & tablet, leaderboard on desktop:
 * <Ad xlSizes={[728, 90]} sizes={[300, 250]} />
 *
 * Mrec on tablet only:
 * <Ad mdSizes={[300, 250]} xlSizes={[]} sizes={[]} />
 */
import GAMAd from '../GAM/Ad';
import PubliftAd from '../Publift/Ad';

import type { Props as GAMProps } from '../GAM/Ad';
import type { Props as PubliftProps } from '../Publift/Ad';

export { AdSize, Viewport, viewportMapping } from 'util/ads';

export type { AdSizeType } from 'util/ads';

export type Props = GAMProps & PubliftProps;

export default function Ad(props: Props) {
  const adServing = useAppSelector((state) => state.features.adServing);

  if (!adServing.enabled) {
    return null;
  }

  const AdComponent = adServing.data.usePublift ? PubliftAd : GAMAd;

  // eslint-disable-next-line react/jsx-props-no-spreading
  return <AdComponent {...props} />;
}
