import { useAppSelector } from 'store/hooks';
import { ServiceType } from 'types/Story';
import { StoryViewType } from 'types/ZoneItems';
import { VIDEO_WIDTHS, charsToPixels, htmlToText } from 'util/device';

import Dailymotion from './index';

import type { GenericHeightsProps } from '../../common/storyElements';
import type { ServiceProps } from '../types';

function ExploreTravelDailymotion({
  element,
  widthClassName,
}: ServiceProps): React.ReactElement | null {
  const explorePlayerId = useAppSelector(
    (state) => state.conf.dailymotionPlayerIdForExploreTravelArticles,
  );
  // This story element component is reused by story-commercial
  const isStoryCommercial = useAppSelector(
    (state) =>
      (state.settings.viewType as StoryViewType) ===
      StoryViewType.STORY_COMMERCIAL,
  );

  return (
    <Dailymotion
      customPlayerId={isStoryCommercial ? undefined : explorePlayerId}
      element={element}
      ignorePaywall={isStoryCommercial}
      widthClassName={widthClassName}
    />
  );
}

// Copied from DailyMotionHeights
export function ExploreTravelDailymotionHeights({
  deviceType,
  item: {
    element: { description, serviceId, serviceType },
  },
}: GenericHeightsProps): number {
  const store = window.getStore();
  const state = store.getState();
  const { dailymotionPlayerId: playerId } = state.conf;

  if (serviceType !== ServiceType.Video || !playerId || !serviceId.trim()) {
    return 0;
  }

  // Add 2 for chars worth for icon.
  const chars = description ? htmlToText(description).length + 2 : 0;

  // When description add top padding
  const descHeight = description
    ? 12 + charsToPixels(chars, deviceType, 'caption')
    : 0;
  const videoHeight = VIDEO_WIDTHS[deviceType];
  return videoHeight + descHeight;
}

export default ExploreTravelDailymotion;
