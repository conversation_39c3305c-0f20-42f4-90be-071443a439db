import { faChevronDown } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import DropDown from 'themes/autumn/components/generic/DropDown';

export const DATE_RANGE_OPTIONS = [
  'Today',
  'This Weekend',
  'This Week',
  'Next Week',
  'Next 30 Days',
];

export const DISTANCE_OPTIONS = [
  { label: '10 km', value: '10' },
  { label: '25 km', value: '25' },
  { label: '50 km', value: '50' },
  { label: '100 km', value: '100' },
  { label: '200 km', value: '200' },
];

interface ClearFilterProps {
  onClear: () => void;
}

export function ClearFilter({ onClear }: ClearFilterProps) {
  return (
    <div className="flex justify-end px-4 py-2">
      <button
        className="text-xs text-gray-500 underline underline-offset-2 hover:text-gray-700"
        onClick={onClear}
        type="button"
      >
        Clear
      </button>
    </div>
  );
}

interface RadioOptionProps {
  hideButton?: boolean;
  isSelected: boolean;
  label: string;
  onClick: () => void;
}

export function RadioOption({
  hideButton = false,
  isSelected,
  label,
  onClick,
}: RadioOptionProps) {
  return (
    <button
      aria-checked={isSelected}
      className="flex w-full cursor-pointer items-center px-4 py-2 text-left text-sm text-gray-900 hover:bg-gray-200"
      onClick={onClick}
      role="menuitemradio"
      type="button"
    >
      {!hideButton && (
        <div className="mr-3 flex size-4 items-center justify-center rounded-full border border-gray-400">
          {isSelected && <div className="size-2 rounded-full bg-gray-900" />}
        </div>
      )}
      {label}
    </button>
  );
}

interface FilterDropdownProps {
  children: React.ReactNode;
  classNameInner?: string;
  label: React.ReactNode;
  preventAutoClose?: boolean;
}

export function FilterDropdown({
  children,
  classNameInner,
  label,
  preventAutoClose,
}: FilterDropdownProps) {
  return (
    <DropDown
      className="ml-2 inline-flex shrink-0 items-center rounded-full bg-gray-100 p-2.5 text-sm font-medium text-slate-750 transition-colors duration-600 ease-default hover:bg-gray-900 hover:text-white focus:transition-none"
      classNameInner={classNameInner}
      label={
        <>
          {label}
          <FontAwesomeIcon className="ml-3" icon={faChevronDown} />
        </>
      }
      preventAutoClose={preventAutoClose}
      selectionClassName="rounded-md border border-gray-300 bg-gray-100"
    >
      {children}
    </DropDown>
  );
}

interface LocationFilterProps {
  clearLocation: () => void;
  handleLocationInputChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handleLocationSubmit: () => void;
  initialLocation: string;
  locationInput: string;
}

export function LocationFilter({
  clearLocation,
  handleLocationInputChange,
  handleLocationSubmit,
  initialLocation,
  locationInput,
}: LocationFilterProps) {
  return (
    <FilterDropdown
      classNameInner="w-auto min-w-72"
      label="Place"
      preventAutoClose
    >
      <div className="p-3">
        <div className="flex w-full">
          <input
            className="h-10 w-full rounded-l-md border border-gray-300 px-4 py-2 text-sm font-medium"
            onChange={handleLocationInputChange}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                e.preventDefault();
                handleLocationSubmit();
              }
            }}
            placeholder="Enter location"
            type="text"
            value={locationInput}
          />
          <button
            className="h-10 rounded-r-md border border-l-0 border-gray-300 bg-gray-100 px-3 text-sm font-medium text-gray-800 hover:bg-gray-200"
            onClick={handleLocationSubmit}
            type="button"
          >
            Search
          </button>
        </div>
        <div className="mt-2 flex justify-end">
          {initialLocation && (
            <button
              className="text-xs text-gray-500 underline underline-offset-2 hover:text-gray-700"
              onClick={clearLocation}
              type="button"
            >
              Clear
            </button>
          )}
        </div>
      </div>
    </FilterDropdown>
  );
}
